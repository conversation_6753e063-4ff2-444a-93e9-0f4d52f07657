using Extric.Towbook.Dispatch;
using Extric.Towbook.Utility;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.MotorClubs
{

    partial class Program
    {
        public class SanjayPaymentImporter
        {
            class PaymentRecord
            {
                public DateTime Date;
                public decimal Amount;
                public string CheckNumber;
                public string PoNumber;
                public int CallNumber;
                public int DispatchEntryId;
                public int InvoiceId;
            }

            public static async Task ImportPayments(int companyId, int accountId, string filename)
            {
                if (!File.Exists(filename))
                {
                    Console.WriteLine("File doesn't exist:" + filename);
                    return;
                }

                using (var package = new ExcelPackage(new FileInfo(filename)))
                {
                    Console.WriteLine(package.Workbook.Worksheets.Count);
                    var acc = Accounts.Account.GetById(accountId);

                    if (acc == null)
                    {
                        Console.WriteLine("Account doesn't exist: " + accountId);
                        return;
                    }
                    if (companyId == 0)
                        companyId = acc.CompanyId;

                    var comp = Company.Company.GetById(companyId);
                    Console.WriteLine("Importing payments for " + comp.Name + " / " + acc.Company);

                    var accounts = Accounts.Account.GetByCompany(comp);
                    var rates = RateItem.GetByCompanyId(companyId);

                    var workSheet = package.Workbook.Worksheets[1];

                    var rowCount = workSheet.Dimension.Rows;
                    int referenceNumberIndex = 0;

                    int poIndex = 0;
                    int callNumberIndex = 0;
                    int dateIndex = 0;
                    int amountIndex = 0;
                    int checkNumberIndex = 0;


                    for (int i = workSheet.Dimension.Start.Column; i <= workSheet.Dimension.End.Column; i++)
                    {
                        
                        if (workSheet.Cells[1, i]?.Value == null)
                            continue;

                        var colName = workSheet.Cells[1, i].Value.ToString().ToLowerInvariant();
                        Console.WriteLine(i + ":" + colName + " --- UNKNOWN");
                        if (colName.Contains("dispatch number")&& poIndex == 0)
                        {
                            poIndex = i;
                        }
                        if (colName.Contains("reference number") && callNumberIndex == 0)
                        {
                            callNumberIndex = i;
                        }
                        if (colName.Contains("amount") && amountIndex ==0)
                        {
                            amountIndex = i;
                        }
                        else if (colName.Contains("date") && dateIndex == 0 )
                        {
                            dateIndex = i;
                        }
                        else if (colName.Contains("check") && checkNumberIndex == 0)
                        {
                            checkNumberIndex = i;
                        }
                      //  else
                        //    Console.WriteLine(i + ":" + colName + " --- UNKNOWN");
                        
                   //     Console.WriteLine(i + ":" + colName);
                    }
                    

                    if (amountIndex == 0)
                    {
                        Console.WriteLine("cannot import: can't find amount column.");
                        return;
                    }

                    if (poIndex == 0 && callNumberIndex == 0)
                    {
                        Console.WriteLine("cannot import: can't find po or call number column.");
                        return;
                    }

                    List<PaymentRecord> payments = new List<PaymentRecord>();

                    for (int i = workSheet.Dimension.Start.Row + 1;
                             i <= workSheet.Dimension.End.Row;
                             i++)
                    {
                        for (int j = workSheet.Dimension.Start.Column;
                                 j <= workSheet.Dimension.End.Column;
                                 j++)
                        {
                            // Console.WriteLine(j + ":" + workSheet.Cells[i, j].Value);
                        }


                        string poValue = poIndex > 0 ? workSheet.Cells[i, poIndex].Value?.ToString().Replace("$", "") : null;
                        int callNumberValue = callNumberIndex > 0 ? workSheet.Cells[i, callNumberIndex].GetValue<int>() : 0;
                        DateTime dateValue = dateIndex > 0 ? workSheet.Cells[i, dateIndex].GetValue<DateTime>() : DateTime.MinValue;

                        decimal amountValue = amountIndex > 0 ? workSheet.Cells[i, amountIndex].GetValue<decimal>() : 0;
                        if (amountValue == 0)
                            continue;
                        string checkNumberValue = checkNumberIndex > 0 ? workSheet.Cells[i, checkNumberIndex].Value?.ToString().Replace("$", "") : null;

                        var p = new PaymentRecord
                        {
                            Date = dateValue,
                            Amount = amountValue,
                            CheckNumber = checkNumberValue,
                            PoNumber = poValue,
                            CallNumber = callNumberValue
                        };
                        payments.Add(p);
                    }

                    var entries = await Entry.GetByCallNumberAsync(
                        payments.Select(o => o.CallNumber).ToArray(), comp);


                    var missing = payments.Select(o => o.CallNumber).Where(o => !entries.Where(r => r.CallNumber == o).Any());

                    if (missing.Any())
                        Console.WriteLine("Missing call numbers: " + missing.ToJson());
                    else
                        Console.WriteLine("Call numbers are valid");

                    var cancelledCalls = entries.Where(o => o.Status.Id == 255).ToList();
                    if (cancelledCalls.Any())
                    {
                        Console.WriteLine(cancelledCalls.Select(o => o.CallNumber + "/" + o.PurchaseOrderNumber).ToJson());
                        foreach (var uncancel in cancelledCalls)
                        {
                            await uncancel.Uncancel();
                            uncancel.Notes = "Uncancelled call because of payment import: " +
                                payments.Where(o => o.CallNumber == uncancel.CallNumber).ToJson().Trim('{').Trim('}') +
                                "\n" + (uncancel.Notes ?? "");
                            await uncancel.Save();
                            Console.WriteLine("Uncancelled call " + uncancel.CallNumber);
                        }
                    }

                    var existingPayments = InvoicePayment.GetByDispatchEntryIds(entries.Select(o => o.Id).ToArray(),null).Where( o =>o.ClassId == 2);
                    Console.WriteLine(existingPayments.Where( r => r.ClassId == 2).Select(o => new
                    {
                        amount = o.Amount,
                        date = o.CreateDate,
                        payDate = o.PaymentDate,
                        classId = o.ClassId
                    }).ToJson()
                    );
                    foreach(var en in entries)
                    {
                        foreach(var p in payments)
                        {

                            if (p.CallNumber == en.CallNumber)
                            {
                                p.InvoiceId = en.Invoice.Id;
                                p.DispatchEntryId = en.Id;
                            }
                        }
                    }

                    var duplicatePayments = existingPayments.Where(o => payments.Where(pp => pp.InvoiceId == o.InvoiceId && pp.Amount == o.Amount).Any())
                        .Select(o => new
                        {
                            amount = o.Amount,
                            date = o.CreateDate,
                            payDate = o.PaymentDate,
                            classId = o.ClassId,
                            matched = payments.Where(rx => rx.InvoiceId == o.InvoiceId).FirstOrDefault()?.ToJson()
                        });

                    if (duplicatePayments.Any())
                    {
                        Console.Title = "existing payments found";
                        Console.WriteLine(duplicatePayments.ToJson(true));

                        existingPayments = existingPayments.Where(o => !payments.Where(pp => pp.InvoiceId == o.InvoiceId && pp.Amount == o.Amount).Any());

                    }


                    var app = new Accounts.Payment()
                    {
                        Amount =
                         payments.Sum(o => o.Amount),
                        OwnerUserId = 1,
                        PaymentDate = DateTime.Now,
                        Type = PaymentType.EFT,
                        AccountId = accountId,
                        CompanyId = companyId
                    };
                    app.Save();

                    foreach (var ip in payments)
                    {
                        var newpayment = new InvoicePayment()
                        {
                            AccountPaymentId = app.Id,
                            OwnerUserId = app.OwnerUserId,
                            Amount = ip.Amount,
                            PaymentDate = ip.Date,
                            ClassId = 2,
                            InvoiceId = ip.InvoiceId,
                            PaymentType = PaymentType.EFT,
                            ReferenceNumber = ip.CheckNumber
                        };
                        await newpayment.Save();
                        Console.WriteLine("Payment " + newpayment.Id + " created");
                    }

                    Console.WriteLine(app.ToJson(true));

                }
            
            }
        }
    }
}
