using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Text;
using System.Data.SqlClient;
using Extric.Towbook.Company;
using System.Diagnostics;
using NVelocity;
using NVelocity.App;
using System.IO;
using Commons.Collections;
using Extric.Towbook.Dispatch;
using Glav.CacheAdapter.Core.DependencyInjection;
using System.Linq;
using ProtoBuf;
using Extric.Towbook.Tasks;
using System.Data;
using Extric.Towbook.Utility;
using Extric.Towbook.ActivityLogging;
using System.Threading.Tasks;
using Extric.Towbook.Company.Accounting;
using Extric.Towbook.Generated;
using Attribute = Extric.Towbook.Dispatch.Attribute;

namespace Extric.Towbook.Impounds
{
    public enum ImpoundType
    {
        NotSpecified = 0,
        Other = 1,
        Police = 2,
        Accident = 3,
        PrivatePropertyImpound = 4,
        Repossession = 5
    }

    public enum ImpoundDueType
    {
        NotSpecified = 0,
        Today = 1,
        Tomorrow = 2,
        Next = 3,
        Overdue = 4,
        Completed = 5
    }

    [Serializable]
    [ProtoContract(Name = "Impound")]
    public partial class Impound : TrackableObject
    {
        //private static ICacheManager cache = CacheFactory.GetCacheManager();
        private const int CacheTimeout = 10;

        /// <summary>
        /// Release reasons have been moved to the db as of 10/1/2021.
        /// Use the ReleaseReason class instead.
        /// </summary>
        [Obsolete]
        public enum ReleaseReasonEnum
        {
            ReleaseWithPayment = 1,
            ReleaseNewOwner = 2,
            Scrapped = 3,
            Other = 4,
            TowOut = 5,
            ReleaseToInsurance = 6
        }

        public enum TowTypeEnum
        {
            None = 0,
            Flatbed = 1,
            WheelLift = 2
        }

        public decimal StorageDailyRate
        {
            get
            {
                if (this.InvoiceItems == null || !this.InvoiceItems.Any())
                    return 0;

                decimal cost = 0;

                var si = this.InvoiceItems.Where(o => o.RateItem != null &&
                    ((o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE) ||
                     (o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_STORAGE_DAILYRATE) ||
                     (o.RateItem.CategoryId == 5)
                    )).FirstOrDefault();

                if (si != null)
                    cost = si.Price;

                return cost;
            }
        }

        public static async Task<string> ParseTemplateAsync(LetterTemplate lt, Impound imp, Dictionary<string, string> licenses = null)
        {
            StringBuilder sb = new StringBuilder();

            #region Old Style Parsing

            sb.Append(lt.Contents);
            sb.Replace("##impound.id##", imp.Id.ToString());
            sb.Replace("##impound.vin##", imp.DispatchEntry.VIN);
            sb.Replace("##impound.date##", Core.OffsetDateTime(imp.Company, imp.ImpoundDate.Value).ToShortDateString());
            sb.Replace("##impound.plate##", imp.DispatchEntry.LicenseNumber);
            sb.Replace("##impound.platestate##", imp.DispatchEntry.LicenseState);
            sb.Replace("##impound.vcolor##", (imp.DispatchEntry.Color != null ? imp.DispatchEntry.Color.Name : ""));
            sb.Replace("##impound.vyear##", imp.DispatchEntry.Year.ToString());

            sb.Replace("##impound.vmake##", imp.DispatchEntry.VehicleMake);
            sb.Replace("##impound.vmodel##", imp.DispatchEntry.VehicleModel);

            sb.Replace("##company##", imp.Company.Name);

            string accountName = "";
            if (imp.DispatchEntry.Account != null)
            {
                accountName = imp.DispatchEntry.Account.Company;
            }

            sb.Replace("##account.name##", accountName);

            sb.Replace("##company.address##", imp.Company.Address);
            sb.Replace("##company.city##", imp.Company.City);
            sb.Replace("##company.state##", imp.Company.State);
            sb.Replace("##company.zip##", imp.Company.Zip);
            sb.Replace("##company.phone##", imp.Company.Phone);

            bool foundLienholder = false;
            bool foundOwner = false;

            foreach (Dispatch.EntryContact ec in imp.DispatchEntry.Contacts)
            {
                if (ec.Type == Dispatch.ContactType.Lienholder && foundLienholder == false)
                {
                    imp.LienholderName = ec.Name;
                    imp.LienholderAddress = ec.Address;
                    imp.LienholderCity = ec.City;
                    imp.LienholderState = ec.State;
                    imp.LienholderZip = ec.Zip;
                    foundLienholder = true;

                }
                else if (ec.Type == Dispatch.ContactType.Owner && foundOwner == false)
                {
                    imp.OwnerName = ec.Name;
                    imp.OwnerAddress = ec.Address;
                    imp.OwnerCity = ec.City;
                    imp.OwnerState = ec.State;
                    imp.OwnerZip = ec.Zip;
                    foundOwner = true;
                }

                if (foundLienholder && foundOwner) break;
            }

            if (imp.OwnerName == null) imp.OwnerName = "";

            sb.Replace("##owner.name##", imp.OwnerName);
            sb.Replace("##owner.address##", imp.OwnerAddress);
            sb.Replace("##owner.city##", imp.OwnerCity);
            sb.Replace("##owner.state##", imp.OwnerState);
            sb.Replace("##owner.zip##", imp.OwnerZip);

            sb.Replace("##leinholder##", (imp.LienholderName + "\n" + imp.LienholderAddress + "\n" +
                imp.LienholderCity + " " + imp.LienholderState + " " + imp.LienholderZip).Replace("\n", "<br />\n"));

            sb.Replace("##date##", Core.OffsetDateTime(imp.Company, DateTime.Now).ToShortDateString());

            sb.Replace("##impound.currenttotal##", imp.InvoiceTotal.ToString("C"));
            sb.Replace("##impound.storagetotal##", imp.InvoiceStorageTotal.ToString("C"));
            sb.Replace("##storage.total##", imp.InvoiceStorageTotal.ToString("C"));

            IRateItem r = new RateItem(imp.Company, await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE));
            if (imp.DispatchEntry.Account != null)
                r = Accounts.RateItem.GetByRateItem(imp.DispatchEntry.Account, r);

            decimal cost = 0;
            foreach (Extric.Towbook.Dispatch.InvoiceItem ii in imp.InvoiceItems)
            {
                if (ii.RateItem != null && ii.RateItem.Predefined != null && ii.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE)
                {
                    cost = ii.CustomPrice.Value;
                }
            }

            sb.Replace("##storage.dailyrate##", cost.ToString("C"));

            #endregion

            string output = sb.ToString();

            var coreAttributes = await Attribute.GetByDispatchEntryAsync(imp.Company, imp.DispatchEntry, true);

            Dictionary<string, string> attributes = new Dictionary<string, string>();

            foreach (var av in coreAttributes)
            {
                string value = "";

                if (imp.DispatchEntry.Attributes.ContainsKey(av.Id))
                {
                    value = imp.DispatchEntry.Attributes[av.Id].Value;
                }
                else
                {
                    value = "<span style='color: red; font-weight: bold; font-size:25px;'>[" + av.Name.ToUpper() + " NOT ENTERED]</span>";
                    imp.DispatchEntry.Attributes.Add(av.Id, new Extric.Towbook.Dispatch.AttributeValue() { Value = value });
                }

                if (!attributes.ContainsKey(av.Name))
                    attributes.Add(av.Name, value);
            }

            // check is attribute even exists so we don't cause an exception. Return empty string if not found.
            if (lt != null && lt.Contents.IndexOf("$Attributes.get_item") != -1)
            {
                IEnumerable<int> indices = Enumerable.Range(0, lt.Contents.Length - "$Attributes.get_item".Length).Where(i => "$Attributes.get_item".Equals(lt.Contents.Substring(i, "$Attributes.get_item".Length)));

                foreach (var start in indices)
                {
                    var field = lt.Contents.Substring(start);
                    var end = field.IndexOf(')');
                    if (end != -1)
                    {
                        string text = lt.Contents.Substring(start, end);
                        string key = text.Split('(')[1];
                        string id = "";
                        string value = "";
                        if (key.IndexOf('"') != -1)
                        {
                            key = text.Split(new char[] { '\"', '\"' })[1]; // http://stackoverflow.com/a/7215364
                        }

                        int keyId = 0;
                        Dispatch.Attribute attr = null;
                        if (int.TryParse(key, out keyId))
                        {
                            attr = await Attribute.GetByIdAsync(keyId);
                        }
                        else
                        {
                            attr = (await Attribute.GetByCompanyAsync(imp.Company, false)).FirstOrDefault(w => w.Name == key);
                        }


                        if (attr != null)
                        {
                            key = attr.Name;
                            id = attr.Id.ToString();
                            if (imp.DispatchEntry.Attributes.ContainsKey(attr.Id))
                                value = imp.DispatchEntry.Attributes[attr.Id].Value;
                        }
                        else
                        {
                            // check company licenses
                            if (licenses != null)
                            {
                                var findOne = licenses.Where(w => w.Key == key).ToCollection();
                                if (findOne.Count() > 0)
                                {
                                    id = "temp_" + findOne[0].Key;
                                    value = findOne[0].Value;
                                }
                            }
                        }

                        if (!attributes.ContainsKey(key))
                        {
                            if (!string.IsNullOrWhiteSpace(id))
                                attributes.Add(id, value);

                            attributes.Add(key, value);
                        }
                    }
                }
            }

            Company.Company company = imp.Company;
            var billingAddress = AddressBookEntry.GetByCompany(imp.Company).SingleOrDefault(o => o.Name == "Billing Address");
            if (billingAddress != null)
            {
                company.Address = billingAddress.Address;
                company.State = billingAddress.State;
                company.Zip = billingAddress.Zip;
                company.Phone = billingAddress.Phone;
            }

            var ve = new VelocityEngine();
            var ep = new ExtendedProperties();

            ve.Init(ep);

            var vc = new VelocityContext();

            vc.Put("Impound", imp);
            vc.Put("Ticket", imp.DispatchEntry);
            vc.Put("Attributes", attributes);
            vc.Put("Company", company);
            vc.Put("Now", DateTime.Now);
            vc.Put("Odometer", (imp.DispatchEntry != null & imp.DispatchEntry.Odometer > 0 ? imp.DispatchEntry.Odometer.ToString() : string.Empty));

            foreach (EntryContact ec in imp.DispatchEntry.Contacts)
            {
                if (ec.Type == Extric.Towbook.Dispatch.ContactType.Owner)
                    vc.Put("Owner", ec);
                else if (ec.Type == Extric.Towbook.Dispatch.ContactType.Lienholder)
                    vc.Put("Lienholder", ec);
            }

            // compile a list of all categories and total them up

            StringWriter writer = new StringWriter();

            ve.Evaluate(vc, writer, lt.Title, output);

            output = writer.GetStringBuilder().ToString().Replace("<tk:Dynamic>", "").Replace("</TK:Dynamic>", "");

            return output;
        }

        public decimal GetCategoryTotal(int categoryId)
        {
            Dictionary<int?, decimal> categoryTotals = new Dictionary<int?, decimal>();

            foreach (InvoiceItem i in this.InvoiceItems)
            {
                int? cId = null;
                if (i.CategoryId != null)
                {
                    cId = i.CategoryId;
                }

                if (cId == null) cId = 0;

                if (!categoryTotals.ContainsKey(cId))
                    categoryTotals.Add(cId, Convert.ToDecimal(i.Total));
                else
                    categoryTotals[cId] += Convert.ToDecimal(i.Total);
            }

            try
            {
                return categoryTotals[categoryId];
            }
            catch
            {
                return 0;
            }
        }

        [ProtoMember(1)]
        private int _id;
        [ProtoMember(2)]
        private int _companyId;
        [ProtoMember(3)]
        private int _accountId;
        [ProtoMember(4)]
        private int _lotId;
        [ProtoMember(5)]
        private string _propertyNumber;
        [ProtoMember(6)]
        private int _ownerUserId;
        [ProtoMember(7)]
        private DateTime _createDate;
        [ProtoMember(8)]
        private Nullable<ImpoundType> _impoundType;
        [ProtoMember(9)]
        private string _reason;
        [ProtoMember(10)]
        private Nullable<DateTime> _impoundDate;
        [ProtoMember(11)]
        private Nullable<DateTime> _releaseDate;
        [ProtoMember(12)]
        private ReleaseDetails _releaseDetails;
        [ProtoMember(13)]
        private int? _releaseReason;
        [ProtoMember(14)]
        private string _releaseNotes;
        [ProtoMember(15)]
        private Nullable<DateTime> _releasePickupDate;
        [ProtoMember(16)]
        private TowTypeEnum _towType;
        [ProtoMember(17)]
        private bool _deleted;

        //1-released with payment
        //2-released - to new owner/title turn over
        //3-vehicle was scrapped
        //4-other

        [ProtoMember(18)]
        private bool _hold;
        [ProtoMember(19)]
        private int _dispatchEntryId;
        [ProtoMember(20)]
        public bool IsTaxExempt { get; set; }
        [ProtoMember(21)]
        public DateTime? MotorVehicleReportDate { get; set; }
        [ProtoMember(22)]
        public Extric.Towbook.Dispatch.Status CurrentStatus { get; set; }
        [ProtoMember(23)]
        public bool HasKeys { get; set; }

        public bool InternalBlockWrites { get; internal set; }

        /// <summary>
        /// Create a new impound
        /// </summary>
        public Impound()
        {
            _id = -1;
        }

        [Obsolete("Prefer using GetById method instead")]
        public Impound(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "ImpoundsGetById", new SqlParameter("@Id", id)))
            {
                if (dr.HasRows)
                {
                    dr.Read();
                    InitializeFromDataReader(dr);
                }
                else
                {
                    throw new Extric.Towbook.TowbookException("Impound doesn't exist!");
                }
            }
        }

        public static async Task<Collection<Impound>> FindAsync(int[] companies, string search)
        {
            return await FindAsync(companies, search, true, true);
        }

        public static async Task<Collection<Impound>> FindAsync(int[] companies, string search, bool searchImpounded = true, bool searchReleased = true, int pageNum = 1, int pageSize = 250)
        {
            var entries = new Collection<Impound>();

            if (string.IsNullOrEmpty(search))
                return entries;

            int? searchInt = null;
            if (int.TryParse(search, out int convertSearch))
                searchInt = convertSearch;

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ImpoundsFindByCompanyId",
                    new SqlParameter("@CompanyId", string.Join(",", companies)),
                    new SqlParameter("@Search", search),
                    new SqlParameter("SearchInt", searchInt),
                    new SqlParameter("@ShowImpounded", searchImpounded),
                    new SqlParameter("@ShowReleased", searchReleased),
                    new SqlParameter("@Page", pageNum),
                    new SqlParameter("@Size", pageSize)))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new Impound(dr));
                }
            }

            return (await InitAsync(entries)).ToCollection();
        }

        public static void ClearCacheById(int id)
        {
            AppServices.Cache.InvalidateCacheItem("impnd:" + id);
        }

        public static Impound GetById(int id)
        {
            if (id == 0)
                return null;

            return AppServices.Cache.Get("impnd:" + id, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "ImpoundsGetById", new SqlParameter("@Id", id)))
                {
                    if (dr.Read())
                    {
                        return new Impound(dr);
                    }
                    else
                    {
                        return null;
                    }
                }
            });
        }

        public static async Task<Impound> GetByIdAsync(int id)
        {
            if (id == 0)
                return null;

            return await AppServices.Cache.GetAsync("impnd:" + id, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    "ImpoundsGetById", new SqlParameter("@Id", id)))
                {
                    if (await dr.ReadAsync())
                    {
                        return new Impound(dr);
                    }
                    else
                    {
                        return null;
                    }
                }
            });
        }

        protected Impound(SqlDataReader dr)
        {
            InitializeFromDataReader(dr);
            AppServices.Cache.Add("impnd:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        public override string ToString()
        {
            return String.Format("Extric.Towbook.Impounds.Impound [Id = {0}, CompanyId = {1}, Dispatch.EntryId = {2}]", _id, _companyId, _dispatchEntryId);
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = Convert.ToInt32(reader["ImpoundId"]);
            _companyId = reader.GetValue<int>("CompanyId");
            _dispatchEntryId = reader.GetValue<int>("DispatchEntryId");
            _propertyNumber = reader.GetValue<string>("PropertyNumber");

            _hold = reader.GetValue<bool>("Hold");
            _deleted = reader.GetValue<bool>("Deleted");
            _accountId = reader.GetValue<int>("AccountId");
            _lotId = reader.GetValue<int>("ImpoundLotId");

            if (reader["ImpoundTypeId"] != DBNull.Value)
                _impoundType = (ImpoundType)Convert.ToInt32(reader["ImpoundTypeId"]);
            else
                _impoundType = Extric.Towbook.Impounds.ImpoundType.NotSpecified;

            if (reader["ReleaseDate"] != DBNull.Value)
                _releaseDate = Convert.ToDateTime(reader["ReleaseDate"]);

            _releaseReason = reader.GetValue<int>("ReleaseReason");

            if (reader["ImpoundDate"] != DBNull.Value)
                _impoundDate = Convert.ToDateTime(reader["ImpoundDate"]);

            if (reader["ReleasePickupDate"] != DBNull.Value)
                _releasePickupDate = Convert.ToDateTime(reader["ReleasePickupDate"]);

            if (reader["MotorVehicleReportDate"] != DBNull.Value)
                MotorVehicleReportDate = reader.GetValue<DateTime>("MotorVehicleReportDate");

            _releaseNotes = reader.GetValue<string>("ReleaseNotes");
            _reason = reader.GetValue<string>("Reason");
            _towType = (TowTypeEnum)reader.GetValue<int>("TowTypeId");
            _ownerUserId = reader.GetValue<int>("OwnerUserId");
            _createDate = reader.GetValue<DateTime>("CreateDate");

            HasKeys = reader.GetValue<bool>("HasKeys");
            IsTaxExempt = reader.GetValue<bool>("IsTaxExempt");
            CurrentStatus = Dispatch.Status.GetById(reader.GetValue<int>("StatusId"), reader.GetValue<int>("CompanyId"));
            Auction = reader.GetValue<bool>("Auction");

            MarkAsClean();
        }


        public static async Task<Impound> GetByPlateForVehicleLookupAsync(Company.Company company, string plate, bool includeReleased = false)
        {
            if (company == null)
            {
                throw new ApplicationException("Must pass a non-null Company object");
            }

            if (string.IsNullOrWhiteSpace(plate))
            {
                throw new ApplicationException("Must pass a plate number.");
            }

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "TWS.ImpoundsGetByPlate",
                new SqlParameter("@CompanyId", company.Id),
                new SqlParameter("@Plate", plate.Replace(" ", "").Replace("\t", "").Trim()),
                new SqlParameter("@IncludeReleased", includeReleased)))
            {
                if (await dr.ReadAsync())
                {
                    return new Impound(dr);
                }
            }

            return null;
        }

        public static async Task<Impound> GetByVinForVehicleLookupAsync(Company.Company company, string vin, bool includeReleased = false)
        {
            if (company == null)
            {
                throw new ApplicationException("Must pass a non-null Company object");
            }

            if (string.IsNullOrWhiteSpace(vin) || vin.Length != 17)
            {
                throw new ApplicationException("Must pass a 17-digit VIN number.");
            }

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "TWS.ImpoundsGetByVin",
                new SqlParameter("@CompanyId", company.Id),
                new SqlParameter("@VIN", vin.Replace(" ", "").Replace("\t", "").Trim()),
                new SqlParameter("@IncludeReleased", includeReleased)))
            {
                if (await dr.ReadAsync())
                {
                    return new Impound(dr);
                }
            }

            return null;
        }

        public static async Task<Collection<Impound>> SearchAsync(Company.Company company, SearchQuery sq, int[] preExistingIds = null)
        {
            var impounds = new Collection<Impound>();

            if (company == null)
            {
                throw new ApplicationException("Must pass a non-null Company object");
            }

            if (sq == null)
            {
                throw new ApplicationException("Must pass a non-null SearchQuery object");
            }

            if (sq.VIN == null &&
                sq.MakeString == null &&
                sq.ModelString == null &&
                sq.ModelYear == null &&
                sq.LicensePlate == null &&
                sq.StartDate == null &&
                sq.EndDate == null &&
                sq.OwnerName == null &&
                sq.OwnerPhone == null &&
                sq.AccountId == null &&
                sq.ImpoundLotId == null &&
                sq.ImpoundTypeId == null &&
                sq.ImpoundId == null &&
                sq.CallNumber == null &&
                sq.InvoiceNumber == null &&
                sq.MinDaysHeld == null &&
                sq.DisplayImpounded == false &&
                sq.DisplayReleased == false &&
                sq.CaseString == null &&
                sq.AttributeId == null &&
                sq.AttributeValue == null &&
                sq.TowSource == null)
            {
                return new Collection<Impound>();
            }

            if (sq.ModelYear == 0) sq.ModelYear = null;
            if (sq.DynamicNumber == "") sq.DynamicNumber = null;

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ImpoundsGetBySearchQuery",
                new SqlParameter("@CompanyId", company.Id),
                new SqlParameter("@VIN", ReplaceEmptyStringWithNull(sq.VIN)),
                new SqlParameter("@MakeString", ReplaceEmptyStringWithNull(sq.MakeString)),
                new SqlParameter("@ModelString", ReplaceEmptyStringWithNull(sq.ModelString)),
                new SqlParameter("@ModelYear", sq.ModelYear),
                new SqlParameter("@LicensePlate", ReplaceEmptyStringWithNull(sq.LicensePlate)),
                new SqlParameter("@StartDate", sq.StartDate),
                new SqlParameter("@EndDate", sq.EndDate),
                new SqlParameter("@OwnerName", ReplaceEmptyStringWithNull(sq.OwnerName)),
                new SqlParameter("@OwnerPhone", ReplaceEmptyStringWithNull(sq.OwnerPhone)),
                new SqlParameter("@DisplayImpounded", sq.DisplayImpounded),
                new SqlParameter("@DisplayReleased", sq.DisplayReleased),
                new SqlParameter("@AccountId", sq.AccountId),
                new SqlParameter("@ImpoundLotId", sq.ImpoundLotId),
                new SqlParameter("@ImpoundType", sq.ImpoundTypeId),
                new SqlParameter("@ImpoundId", sq.ImpoundId),
                new SqlParameter("@CallNumber", sq.CallNumber),
                new SqlParameter("@InvoiceNumber", ReplaceEmptyStringWithNull(sq.InvoiceNumber)),
                new SqlParameter("@DynamicNumber", sq.DynamicNumber),
                new SqlParameter("@MinDaysHeld", sq.MinDaysHeld),
                new SqlParameter("@AttributeId", sq.AttributeId),
                new SqlParameter("@AttributeValue", ReplaceEmptyStringWithNull(sq.AttributeValue)),
                new SqlParameter("@SearchDateByImpoundDate", sq.SearchDateByImpoundDate),
                new SqlParameter("@TowSource", sq.TowSource),
                new SqlParameter("@BatchSize", sq.BatchSize),
                new SqlParameter("@Page", sq.PageNum)))
            {
                int minDaysHeld = int.MinValue;

                if (sq.MinDaysHeld != null)
                    minDaysHeld = sq.MinDaysHeld.Value;

                while (await dr.ReadAsync())
                {
                    var newImpound = new Impound(dr);

                    if (newImpound.DaysHeld < minDaysHeld)
                        continue;
                    impounds.Add(newImpound);
                }
            }

            if (preExistingIds == null)
                preExistingIds = Array.Empty<int>();

            await InitAsync(impounds.Where(o => !preExistingIds.Any(r => r == o._dispatchEntryId)).ToList());

            return impounds;
        }

        private static string ReplaceEmptyStringWithNull(string input)
        {
            if (String.IsNullOrWhiteSpace(input))
                return null;
            else
                return input;
        }

        public class ListContainer<T>
        {
            public List<T> List { get; set; }
            public int TotalCount { get; set; }
        }


        public enum ImpoundListReturnType
        {
            All,
            Current,
            Released,
            Auction
        }

        /// <summary>
        /// Returns list from CosmosDB. 
        /// </summary>
        /// <param name="companyIds"></param>
        /// <param name="showImpounded"></param>
        /// <param name="showReleased"></param>
        /// <param name="showAuction"></param>
        /// <param name="startAt"></param>
        /// <param name="limit"></param>
        /// <param name="accountId"></param>
        /// <returns></returns>
        public static async Task<ListContainer<Impound>> GetByCompanyAsync(
            int[] companyIds, // x
            ImpoundListReturnType type,
            int pageNumber = 0,
            int pageSize = 0,
            int? accountId = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var pk = companyIds.First().ToString();

            if (companyIds.Length > 1)
                pk = null;

            if (pageSize == 0)
                pageSize = 250;

            var fieldConstant = "__FIELDS__";
            var fields = "c.id, c.companyId, c.impound, c.impoundDetails";

            var queryText = $"SELECT {fieldConstant} FROM c WHERE c.companyId IN ({string.Join(",", companyIds)}) AND c.impound=true and c.status.id = 5";

            var c = new Collection<CosmosParameter>();

            if (accountId != null)
            {
                queryText += " AND c.account.id=@accountId";
                c.Add(new CosmosParameter("@accountId", accountId));
            }

            if (type == ImpoundListReturnType.Auction)
            {
                queryText += " AND c.impoundDetails.auction=@auction AND NOT IS_DEFINED(c.impoundDetails.releaseDate)";
                c.Add(new CosmosParameter("@auction", true));
            }
            else if (type == ImpoundListReturnType.Current)
            {
                queryText += " AND c.impoundDetails.auction=false";
                queryText += " AND NOT IS_DEFINED(c.impoundDetails.releaseDate)";
            }
            else if (type == ImpoundListReturnType.Released)
            {
                queryText += " AND c.impoundDetails.auction=false";
                queryText += " AND IS_DEFINED(c.impoundDetails.releaseDate)";
            }
            
            //  we're grabbing the TOP 1000, so we want the most recent 1000... make this configurable.

            var orderBy = "c.impoundDetails.impoundDate";

            if (type == ImpoundListReturnType.Released)
                orderBy = "c.impoundDetails.releaseDate";

            int totalCount = -1;
            if (pageNumber == 1)
            {
                var countQuery = new Microsoft.Azure.Cosmos.QueryDefinition(queryText.Replace(fieldConstant, "VALUE COUNT(1)"));

                foreach (var x in c)
                {
                    countQuery = countQuery.WithParameter(x.Key, x.Value);
                }

                var countResult = await CosmosDB.Get().QueryScalarAsync<int>(
                    "calls",
                    countQuery,
                    pk);

                totalCount = countResult;
            }

            queryText += $" ORDER BY {orderBy} DESC";

            if (pageSize > 0)
            {
                if (pageNumber < 1)
                    pageNumber = 1;

                int offset = (pageNumber * pageSize) - pageSize;

                if (offset < 0)
                    offset = 0;

                queryText += $" OFFSET {offset} LIMIT {pageSize}";
            }

            var query = new Microsoft.Azure.Cosmos.QueryDefinition(queryText.Replace("__FIELDS__", fields));

            foreach (var x in c)
            {
                query = query.WithParameter(x.Key, x.Value);
            }

            // Get it from cosmos
            var cosmosCalls = await CosmosDB.Get().QueryItemsAsync<Dispatch.CallModels.CallModel>(
                "calls",
                query,
                pk);

            var ret = (await GetByDispatchEntryIdsAsync(cosmosCalls.Select(o => o.Id).ToCollection()))
                .OrderByDescending(o => (type == ImpoundListReturnType.Released) ? o.ReleaseDate : o.ImpoundDate).ToList();

            return new ListContainer<Impound>()
            {
                TotalCount = totalCount,
                List = ret
            };
        }

        public static async Task<List<Impound>> GetByCompanyAsync(
            int[] companyIds, 
            bool showImpounded, 
            bool showReleased, 
            bool showAuction, 
            int startAt = 0, 
            int limit = 0, 
            int? accountId = null)
        {
            if (companyIds.Length == 0 || (showImpounded == false && showReleased == false && showAuction == false))
                return new List<Impound>();

            var impounds = new List<Impound>();

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ImpoundsGetByCompanyId",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@DispatchReasonId", null),
                new SqlParameter("@ShowImpounded", showImpounded),
                new SqlParameter("@ShowReleased", showReleased),
                new SqlParameter("@Limit", limit),
                new SqlParameter("@StartAtImpoundId", startAt),
                new SqlParameter("@ShowAuction", showAuction),
                new SqlParameter("@AccountId", accountId)))
            {
                while (await dr.ReadAsync())
                {
                    impounds.Add(new Impound(dr));
                }
            }

            if (impounds.Count == 0)
                return new List<Impound>();

            
            impounds = await InitAsync(impounds);

            return impounds;
        }

        public static async Task<List<Impound>> GetByCompanyAsync(Company.Company company, ImpoundType? type, bool showImpounded, bool showReleased)
        {
            // TODO: fix this method, it can return 100,000's of impounds...

            var impounds = new List<Impound>();

            if (type == Impounds.ImpoundType.NotSpecified)
                type = null;

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ImpoundsGetByCompanyIdType",
                new SqlParameter("@CompanyId", company.Id),
                new SqlParameter("@ImpoundTypeId", type),
                new SqlParameter("@ShowImpounded", showImpounded),
                new SqlParameter("@ShowReleased", showReleased)))
            {
                while (await dr.ReadAsync())
                {
                    impounds.Add(new Impound(dr));
                }
            }

            return impounds;
        }

        /// <summary>
        /// Used for Generate Report.
        /// </summary>
        /// <param name="companyIds"></param>
        /// <param name="type"></param>
        /// <param name="showImpounded"></param>
        /// <param name="showReleased"></param>
        /// <param name="accountIds"></param>
        /// <returns></returns>
        public static async Task<List<Impound>> GetByCompanyAsync(int[] companyIds, ImpoundType? type, bool showImpounded, bool showReleased, int[] accountIds)
        {
            var impounds = new List<Impound>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ImpoundsGetByCompanyIdTypeAccount",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@ImpoundTypeId", type),
                new SqlParameter("@ShowImpounded", showImpounded),
                new SqlParameter("@ShowReleased", showReleased),
                new SqlParameter("@AccountId", String.Join(",", accountIds))))
            {
                while (await dr.ReadAsync())
                {
                    impounds.Add(new Impound(dr));
                }
            }

            return impounds;
        }

        public static async Task<List<Impound>> GetByCompanyAsync(
            int[] companyIds,
            ImpoundType? type,
            bool showImpounded,
            bool showReleased,
            int[] accountIds,
            DateTime? impoundStartDate,
            DateTime? impoundEndDate,
            DateTime? releasedStartDate,
            DateTime? releasedEndDate,
            bool excludeAuctions,
            int? includePoliceHold /* 0 = all, 1 = exclude, 2 = include only */,
            int[] impoundLotIds)
        {
            var impounds = new List<Impound>();

            var accounts = String.Join(",", accountIds ?? Array.Empty<int>());
            var lots = String.Join(",", impoundLotIds ?? Array.Empty<int>());

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ImpoundsGenerateReport",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@ImpoundTypeId", type),
                new SqlParameter("@ShowImpounded", showImpounded),
                new SqlParameter("@ShowReleased", showReleased),
                new SqlParameter("@AccountId", accounts),
                new SqlParameter("@StartDate", impoundStartDate),
                new SqlParameter("@EndDate", impoundEndDate),
                new SqlParameter("@ReleasedStartDate", releasedStartDate),
                new SqlParameter("@ReleasedEndDate", releasedEndDate),
                new SqlParameter("@IncludeAuction", excludeAuctions ? 0 : 1),
                new SqlParameter("@IncludePoliceHold", includePoliceHold == 0 ? (int?)null : includePoliceHold),
                new SqlParameter("@ImpoundLotId", lots)))
            {
                while (await dr.ReadAsync())
                {
                    impounds.Add(new Impound(dr));
                }
            }

            return impounds;
        }

        public static List<Impound> Init(IEnumerable<Impound> impounds)
        {
            var entries = Entry.GetByIds(impounds.Select(o => o._dispatchEntryId).ToArray());

            foreach (var x in entries)
            {
                var imp = impounds.FirstOrDefault(o => o._dispatchEntryId == x.Id);

                if (imp != null)
                {
                    imp._dispatchEntry = x;
                    imp.invoice = x.Invoice;
                    imp.invoice.DispatchEntry = x;
                    imp.invoice.Impound = imp;
                }
            }

            InvoiceItemRuleApplied.GetByDispatchEntryId(entries.Select(o => o.Id).ToArray());

            var commissions = InvoiceItem.InvoiceItemDriverCommission.GetByInvoiceItemIds(
                entries.SelectMany(o => o.InvoiceItems).Select(o => o.Id).ToArray());

            foreach (var commissionInvoice in commissions.GroupBy(o => o.InvoiceId))
            {
                var inv = entries.FirstOrDefault(o => o.Invoice.InvoiceItems.Any(io => io.InvoiceId == commissionInvoice.Key));
                if (inv == null)
                    continue;

                foreach (var group in commissionInvoice.GroupBy(o => o.InvoiceItemId))
                {
                    var iil = inv?.InvoiceItems?.FirstOrDefault(o => o.Id == group.Key);
                    if (iil != null)
                        iil.DriverCommissions = group.ToCollection();
                }
            }

            foreach (var x in entries.SelectMany(o => o.InvoiceItems)
                .Where(o => !commissions.Any(cx => cx.InvoiceItemId == o.Id)))
            {
                x.DriverCommissions = new Collection<InvoiceItem.InvoiceItemDriverCommission>();
            }

            return impounds.ToList();
        }

        public static async Task<List<Impound>> InitAsync(IEnumerable<Impound> impounds)
        {
            var entries = await Entry.GetByIdsAsync(impounds.Select(o => o._dispatchEntryId).ToArray());

            foreach (var x in entries)
            {
                var imp = impounds.FirstOrDefault(o => o._dispatchEntryId == x.Id);

                if (imp != null)
                {
                    imp._dispatchEntry = x;
                    imp.invoice = x.Invoice;
                    imp.invoice.DispatchEntry = x;
                    imp.invoice.Impound = imp;
                }
            }

            //TODO ASYNC
            InvoiceItemRuleApplied.GetByDispatchEntryId(entries.Select(o => o.Id).ToArray());

            var commissions = InvoiceItem.InvoiceItemDriverCommission.GetByInvoiceItemIds(
                entries.SelectMany(o => o.InvoiceItems).Select(o => o.Id).ToArray());

            foreach (var commissionInvoice in commissions.GroupBy(o => o.InvoiceId))
            {
                var inv = entries.FirstOrDefault(o => o.Invoice.InvoiceItems.Any(io => io.InvoiceId == commissionInvoice.Key));
                if (inv == null)
                    continue;

                foreach (var group in commissionInvoice.GroupBy(o => o.InvoiceItemId))
                {
                    var iil = inv?.InvoiceItems?.FirstOrDefault(o => o.Id == group.Key);
                    if (iil != null)
                        iil.DriverCommissions = group.ToCollection();
                }
            }

            foreach (var x in entries.SelectMany(o => o.InvoiceItems)
                .Where(o => !commissions.Any(cx => cx.InvoiceItemId == o.Id)))
            {
                x.DriverCommissions = new Collection<InvoiceItem.InvoiceItemDriverCommission>();
            }

            return impounds.ToList();
        }

        public static async Task<Collection<Impound>> GetByDispatchEntryIdsAsync(Collection<int> idList)
        {
            Collection<Impound> items = new Collection<Impound>();

            if (idList.Count == 0)
                return items;

            
            string ids = string.Join(",", idList);
            Console.WriteLine(ids);

            using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                CommandType.StoredProcedure,
                "[ImpoundsGetByDispatchEntryIdArray]",
                new SqlParameter[] { new SqlParameter("@DispatchEntryIds", ids) }))
            {
                while (await reader.ReadAsync())
                {
                    items.Add(new Impound(reader));
                }
            }

            return (await InitAsync(items)).ToCollection();
        }


        public class ImpoundCallMinimalModel
        {
            public int DispatchEntryId { get; set; }
            public int ImpoundId { get; set; }
        }

        public static List<ImpoundCallMinimalModel> GetImpoundIdsByDispatchEntryIds(int[] callIds)
        {
            var entries = new List<ImpoundCallMinimalModel>();

            foreach (var ids in callIds.Batch(500))
            {
                entries.AddRange(SqlMapper.Query<ImpoundCallMinimalModel>(
                    @"select ImpoundId, DispatchEntryId
                    from Impounds WITH (NOLOCK) where DispatchEntryId in @CallIds", 
                    new { CallIds = ids }));
            }
            return entries;
        }

        public static async Task<List<ImpoundCallMinimalModel>> GetImpoundIdsByDispatchEntryIdsAsync(int[] callIds)
        {
            var entries = new List<ImpoundCallMinimalModel>();

            foreach (var ids in callIds.Batch(500))
            {
                // TODO: Can be optimized further to avoid making n calls
                entries.AddRange(await SqlMapper.QueryAsync<ImpoundCallMinimalModel>(
                    @"select ImpoundId, DispatchEntryId
                    from Impounds WITH (NOLOCK) where DispatchEntryId in @CallIds",
                    new { CallIds = ids }));
            }
            return entries;
        }

        public static Impound GetByDispatchEntry(Dispatch.Entry entry, bool ignoreCache = false)
        {
            Impound ir = null;

            if (ignoreCache)
                AppServices.Cache.InvalidateCacheItem("ImpoundGetByDispatchEntryId" + entry.Id);

            int? n = (int?)AppServices.Cache.Get<object>("ImpoundGetByDispatchEntryId" + entry.Id, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "ImpoundsGetByDispatchEntryId",
                new SqlParameter("@DispatchEntryId", entry.Id)))
                {
                    if (dr.Read())
                    {
                        ir = new Impound(dr);
                        return ir.Id;
                    }
                    else
                    {
                        return null;
                    }
                }
            });

            if (ir != null)
                return ir;

            if (n != null)
            {
                ir = Impound.GetById(n.Value);
                if (ir != null)
                    return ir;
                else
                    AppServices.Cache.InvalidateCacheItem("ImpoundByDispatchEntryId" + entry.Id);
            }
            return null;
        }

        public static async Task<Impound> GetByDispatchEntryAsync(Dispatch.Entry entry, bool ignoreCache = false)
        {
            Impound ir = null;

            if (ignoreCache)
                AppServices.Cache.InvalidateCacheItem("ImpoundGetByDispatchEntryId" + entry.Id);

            int? n = (int?) (await AppServices.Cache.GetAsync<object>("ImpoundGetByDispatchEntryId" + entry.Id, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ImpoundsGetByDispatchEntryId",
                new SqlParameter("@DispatchEntryId", entry.Id)))
                {
                    if (await dr.ReadAsync())
                    {
                        ir = new Impound(dr);
                        return ir.Id;
                    }
                    else
                    {
                        return null;
                    }
                }
            }));

            if (ir != null)
                return ir;

            if (n != null)
            {
                ir = await Impound.GetByIdAsync(n.Value);
                if (ir != null)
                    return ir;
                else
                    AppServices.Cache.InvalidateCacheItem("ImpoundByDispatchEntryId" + entry.Id);
            }
            return null;
        }

        public void Delete(User performer)
        {
            if (_id == 0)
            {
                throw new Extric.Towbook.TowbookException("No such Impound. Can't save " +
                    "object! (this object should have already been discarded!)");
            }
            if (_id == -1)
            {
                throw new Extric.Towbook.TowbookException("No such Impound. Can't save!");
            }

            if (this.IsWithinClosedAccountingPeriod())
            {
                throw new TowbookException("Impound release date is within the company's closed period and cannot be deleted.");
            }

            SqlHelper.ExecuteNonQuery(Core.ConnectionString, "ImpoundsDeleteById", new SqlParameter("@ImpoundId", _id));
            _deleted = true;

            AppServices.Cache.InvalidateCacheItem("impnd:" + Id);
        }


        private void ThrowIfInternalBlockWrites()
        {
            if (InternalBlockWrites)
                throw new ApplicationException("InternalBlockWrites is set. This is a temporary copy of the Impound and cannot be saved.");
        }

        public async Task Save(User performer, bool dontAddDefault = false) => await Save(performer, null, null, dontAddDefault);

        public async Task Save(User performer, AuthenticationToken token, string ipAddress, bool dontAddDefault = false) =>
            await Save(performer, token, ipAddress, dontAddDefault, false);

        public async Task Save(User performer, AuthenticationToken token, string ipAddress, bool dontAddDefault, bool overrideClosedPeriod)
        {
            ThrowIfInternalBlockWrites();

            if (_id == 0)
            {
                Impound.ClearCacheById(this.Id);

                throw new Extric.Towbook.TowbookException("No such Impound. Can't save " +
                    "object! (this object should have already been discarded!)");
            }

            if (_dispatchEntryId < 1)
                throw new NullReferenceException("DispatchEntryId hasn't been set!");


            if (!overrideClosedPeriod && Company.HasFeature(Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(this.DispatchEntry.CompanyId);
                if (this.IsWithinClosedAccountingPeriod(closedPeriodOptions))
                {
                    throw new TowbookException($"Impound {Id} is locked by closed period settings. An override is required to modify it.");
                }
            }

            try
            {
                bool create = false;

                if (_id == -1)
                {
                    DbInsert();

                    HistoryItem hi = new HistoryItem();

                    hi.ImpoundId = _id;
                    hi.User = performer;
                    hi.Action = HistoryItem.ActionEnum.Created;
                    hi.Save();

                    create = true;
                }
                else
                {
                    DbUpdate();

                    HistoryItem hi2 = new HistoryItem();
                    hi2.ImpoundId = _id;
                    hi2.User = performer;
                    hi2.Action = HistoryItem.ActionEnum.Modified;
                    hi2.Save();

                }

                // Create or update ImpoundTasks for the new Impound, according to the ImpoundReminders configuration
                await ImpoundTask.CreateTasksForImpoundAsync(this.Id, this._companyId);

                if (!dontAddDefault)
                    await AddStorageItemDefaultAsync();

                if (this.ReleaseDate != null)
                {
                    var storage = this.InvoiceItems.Where(o => o.RateItem != null &&
                        ((o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE) ||
                        o.RateItem.CategoryId == 5 && o.RateItem.ParentRateItemId == 0)).ToList();

                    if (storage != null)
                    {
                        foreach (var x in storage)
                        {
                            if (x != null)
                            {
                                this.Invoice.UpdateTieredItem(x, true);
                            }
                        }
                    }
                }

                if (this.Invoice != null)
                {
                    if (this.Invoice.Impound == null)
                        this.invoice.Impound = this;

                    await this.Invoice.SaveAsync(null, null, overrideClosedPeriod);
                }

                if (base.IsFieldDirty("PoliceHold"))
                {
                    HistoryItem hiph = new HistoryItem();
                    hiph.ImpoundId = _id;
                    hiph.User = performer;
                    hiph.Action = _hold ? HistoryItem.ActionEnum.PoliceHoldIssued : HistoryItem.ActionEnum.PoliceHoldReleased;
                    hiph.Save();
                }

                await base.SaveAsync(token,
                    ActivityLogType.DispatchEntry,
                    _dispatchEntryId,
                    ActivityLogType.Company,
                    _companyId,
                    create ? 1 : 2,
                    ipAddress);

                MarkAsClean();
            }
            finally
            {
                Impound.ClearCacheById(this.Id);
                Extric.Towbook.Dispatch.Entry.CacheClearById(this.DispatchEntry.Id);
            }
        }

        private Invoice invoice = null;

        public Invoice Invoice
        {
            get
            {
                if (invoice == null)
                {
                    if (this._dispatchEntry != null)
                        this.invoice = this._dispatchEntry.Invoice;

                    if (this.Id > 0)
                        invoice = Invoice.GetByImpound(this.Id);

                    if (invoice == null && this.DispatchEntry != null)
                    {
                        invoice = Invoice.GetByDispatchEntry(this._dispatchEntryId);
                    }
                }

                return invoice;
            }
            set
            {
                invoice = value;
            }
        }

        public async Task AddStorageItemDefaultAsync()
        {
            // don't add if the impound is deleted.
            if (this.Deleted)
                return;

            try
            {
                if (this.InvoiceItems.Any(o => o.RateItem != null && (
                        (o.RateItem.Predefined != null &&
                         o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE) ||
                        o.RateItem.CategoryId == 5)))
                    return;
            }
            catch (NullReferenceException)
            {
                return;
            }

            #region No storage items found, add the default and then return

            InvoiceItem x = new InvoiceItem();

            IRateItem r = new RateItem(DispatchEntry.Company, await PredefinedRateItem.GetByIdAsync(PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE));

            Accounts.RateItem ri = null;
            if (this.Lot != null)
            {
                if (this.Lot.AccountId != 1)
                {
                    // this account isnt doing the storage.. so don't calculate it.
                    return;
                }
            }

            if (this.ReleaseDate != null)
                return;

            if (this.DispatchEntry.Account != null)
                ri = await Accounts.RateItem.GetByRateItemAsync(this.DispatchEntry.Account, r);

            if (ri != null)
            {
                r = ri;
                // Debug.WriteLine("StorageRate is set to AccountRateItem: " + r.Cost + ", for Invoice " + Id + ", for " +
                //    (DispatchEntry.BodyType != null ? DispatchEntry.BodyType.Id : 0) + ", for " + r.RateItemId);
            }

            x.RateItem = ri ?? r;

            // RateItemId = -1 if predefinedrateitem doesnt exist for this company
            if (x.RateItem.RateItemId != -1)
            {
                if (r.ExtendedRateItems != null && DispatchEntry.BodyType != null &&
                        (r.ExtendedRateItems.ContainsKey(DispatchEntry.BodyType.Id) ||
                         (ri != null && ri.ExtendedRateItems.ContainsKey(DispatchEntry.BodyType.Id)))
                    )
                {
                    if (ri != null && ri.ExtendedRateItems.ContainsKey(DispatchEntry.BodyType.Id))
                    {
                        x.RateItem.Cost = ri.ExtendedRateItems[DispatchEntry.BodyType.Id].Amount;
                        // Debug.WriteLine("StorageRate is set to: " + x.RateItem.Cost + ", for Invoice " + Id + ", for " +
                        //    DispatchEntry.BodyType.Id + ", for " + ri.ExtendedRateItems[DispatchEntry.BodyType.Id].Id);
                    }
                    else
                    {
                        x.RateItem.Cost = r.ExtendedRateItems[DispatchEntry.BodyType.Id].Amount;
                        // Debug.WriteLine("StorageRate is set to: " + x.RateItem.Cost + ", for invoice " + Id);
                    }
                }

                x.CustomPrice = x.RateItem.Cost;
                x.Quantity = -1;
                x.InvoiceId = Invoice.Id;
                x.Quantity = this.DaysHeldBillable;

                this.InvoiceItems.Add(x);

            }
            #endregion
        }

        private void DbInsert()
        {
            _id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "ImpoundsInsert",
                    new SqlParameter("@CompanyId", _companyId),
                    new SqlParameter("@DispatchEntryId", _dispatchEntryId),
                    new SqlParameter("@ImpoundLotId", (_lotId > 0 ? (int?)_lotId : null)),
                    new SqlParameter("@AccountId", _accountId > 0 ? (int?)_accountId : null),
                    new SqlParameter("@PropertyNumber", _propertyNumber),
                    new SqlParameter("@OwnerUserId", _ownerUserId),
                    new SqlParameter("@ImpoundTypeId", _impoundType),
                    new SqlParameter("@ImpoundDate", _impoundDate),
                    new SqlParameter("@Reason", _reason),
                    new SqlParameter("@ReleaseDate", _releaseDate),
                    new SqlParameter("@ReleaseReason", _releaseReason),
                    new SqlParameter("@ReleaseNotes", _releaseNotes),
                    new SqlParameter("@ReleasePickupDate", _releasePickupDate),
                    new SqlParameter("@Hold", _hold),
                    new SqlParameter("@WinchingFee", 0),
                    new SqlParameter("@CleanupFee", 0),
                    new SqlParameter("@Miles", 0),
                    new SqlParameter("@MilePrice", 0),
                    new SqlParameter("@TowType", Convert.ToInt32(_towType)),
                    new SqlParameter("@OwnerName", OwnerName),
                    new SqlParameter("@OwnerAddress", OwnerAddress),
                    new SqlParameter("@OwnerCity", OwnerCity),
                    new SqlParameter("@OwnerState", OwnerState),
                    new SqlParameter("@OwnerZip", OwnerZip),
                    new SqlParameter("@OwnerPhone", OwnerPhone),
                    new SqlParameter("@LienholderName", LienholderName),
                    new SqlParameter("@LienholderAddress", LienholderAddress),
                    new SqlParameter("@LienholderCity", LienholderCity),
                    new SqlParameter("@LienholderState", LienholderState),
                    new SqlParameter("@LienholderZip", LienholderZip),
                    new SqlParameter("@LienholderPhone", LienholderPhone),
                    new SqlParameter("@HasKeys", HasKeys),
                    new SqlParameter("@IsTaxExempt", IsTaxExempt),
                    new SqlParameter("@MotorVehicleReportDate", MotorVehicleReportDate),
                    new SqlParameter("@Auction", Auction)));
        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "ImpoundsUpdateById",
                    new SqlParameter("@ImpoundId", _id),
                    new SqlParameter("@CompanyId", _companyId),
                    new SqlParameter("@DispatchEntryId", _dispatchEntryId),
                    new SqlParameter("@ImpoundLotId", (_lotId > 0 ? (int?)_lotId : null)),
                    new SqlParameter("@AccountId", _accountId > 0 ? (int?)_accountId : null),
                    new SqlParameter("@PropertyNumber", _propertyNumber),
                    new SqlParameter("@OwnerUserId", _ownerUserId),
                    new SqlParameter("@ImpoundTypeId", _impoundType),
                    new SqlParameter("@ImpoundDate", _impoundDate),
                    new SqlParameter("@Reason", _reason),
                    new SqlParameter("@ReleaseDate", _releaseDate),
                    new SqlParameter("@ReleaseReason", _releaseReason),
                    new SqlParameter("@ReleaseNotes", _releaseNotes),
                    new SqlParameter("@ReleasePickupDate", _releasePickupDate),
                    new SqlParameter("@Hold", _hold),
                    new SqlParameter("@WinchingFee", 0),
                    new SqlParameter("@CleanupFee", 0),
                    new SqlParameter("@Miles", 0),
                    new SqlParameter("@MilePrice", 0),
                    new SqlParameter("@TowType", Convert.ToInt32(_towType)),
                    new SqlParameter("@OwnerName", OwnerName),
                    new SqlParameter("@OwnerAddress", OwnerAddress),
                    new SqlParameter("@OwnerCity", OwnerCity),
                    new SqlParameter("@OwnerState", OwnerState),
                    new SqlParameter("@OwnerZip", OwnerZip),
                    new SqlParameter("@OwnerPhone", OwnerPhone),
                    new SqlParameter("@LienholderName", LienholderName),
                    new SqlParameter("@LienholderAddress", LienholderAddress),
                    new SqlParameter("@LienholderCity", LienholderCity),
                    new SqlParameter("@LienholderState", LienholderState),
                    new SqlParameter("@LienholderZip", LienholderZip),
                    new SqlParameter("@LienholderPhone", LienholderPhone),
                    new SqlParameter("@HasKeys", HasKeys),
                    new SqlParameter("@IsTaxExempt", IsTaxExempt),
                    new SqlParameter("@MotorVehicleReportDate", MotorVehicleReportDate),
                    new SqlParameter("@Auction", Auction));
        }

        public int Id
        {
            get { return _id; }
            internal set { _id = value; }
        }

        public DateTime CreateDate
        {
            get { return _createDate; }
            set { _createDate = value; }
        }

        public string OwnerName { get; set; }
        public string OwnerAddress { get; set; }
        public string OwnerCity { get; set; }
        public string OwnerState { get; set; }
        public string OwnerZip { get; set; }
        public string OwnerPhone { get; set; }
        public string LienholderName { get; set; }
        public string LienholderAddress { get; set; }
        public string LienholderCity { get; set; }
        public string LienholderState { get; set; }
        public string LienholderZip { get; set; }
        public string LienholderPhone { get; set; }
        public bool Auction { get; set; }

        /// <summary>
        /// The impound lot that the vehicle is being stored at.
        /// </summary>
        public Lot Lot
        {
            get { return Lot.GetById(_companyId, _lotId); }
            set { _lotId = (value == null ? 0 : value.Id); }
        }

        public async Task<Lot> GetLotAsync()
        {
            return await Lot.GetByIdAsync(_companyId, _lotId);
        }

        public Company.Company Company
        {
            get { return Extric.Towbook.Company.Company.GetById(_companyId); }
            set { _companyId = value.Id; }
        }

        public async Task<Company.Company> GetCompanyAsync()
        {
            return await Extric.Towbook.Company.Company.GetByIdAsync(_companyId);
        }

        public int OwnerUserId
        {
            get { return _ownerUserId; }
            set { _ownerUserId = value; }
        }

        public DateTime? ImpoundDate
        {
            get => _impoundDate;
            set
            {

                if (_impoundDate != value)
                {
                    _impoundDate = value;

                    if (this.Invoice != null)
                    {
                        this.invoice.NextScheduledRecalculate = DateTime.Now;

                        var ss = this.invoice.InvoiceItems.FirstOrDefault(o => o.IsStorageItem() &&
                                                                               o.RelatedInvoiceItemId.GetValueOrDefault() == 0);

                        if (ss != null)
                        {
                            ss.Locked = InvoiceItem.InvoiceItemLock.Unlocked;
                            ss.MarkAsClean();
                        }

                        this.Invoice.UpdateStorageQuantities(true);
                    }
                }
            }
        }

        /// <summary>
        /// Returns the number of days between Now and ImpoundDate, or ReleasePickupDate and ImpoundDate, if ReleaseDate isn't null.
        /// </summary>
        public int DaysHeld
        {
            get
            {
                if (!ImpoundDate.HasValue)
                    return 0;

                if (!ReleasePickupDate.HasValue)
                {
                    return ((TimeSpan)(DateTime.Now.Date - ImpoundDate.Value.Date)).Days + 1;
                }
                else
                {
                    return ((TimeSpan)(ReleasePickupDate.Value.Date - ImpoundDate.Value.Date)).Days + 1;
                }
            }
            set
            {
                // do nothing, this is only here for serialization, how can i avoid this?
            }
        }

        public string ZoresGetAuctionTotal()
        {
            if (this.DispatchEntry.Attributes.ContainsKey(1360))
            {
                var value = this.DispatchEntry.Attributes[1360];
                DateTime x;
                if (DateTime.TryParse(value.Value, out x))
                {
                    //   return (StorageDailyRate * GetDaysHeldBillable(x)).ToString("C");
                }

                InvoiceItem savedLine = null;
                decimal originalQuantity = 0;

                foreach (var line in this.InvoiceItems)
                {
                    if (line.RateItem != null &&
                        ((line.RateItem.Predefined != null && line.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE) ||
                            line.RateItem.CategoryId == 5))
                    {
                        savedLine = line;
                        originalQuantity = line.Quantity;
                        line.Quantity = GetDaysHeldBillable(null, x);
                    }
                }

                var total = this.InvoiceTotal.ToString("C");
                if (savedLine != null)
                {
                    savedLine.Quantity = originalQuantity;
                    return total;
                }
            }

            return "[auction date not set or invalid]";
        }

        /// <summary>
        /// Checks to see if the account and current storage item is set up to be hourly storage.
        /// </summary>
        /// <param name="a">The Account to check against</param>
        /// <param name="ri">The storage item to check against</param>
        /// <param name="startDate">The start time for the hourly calculation window</param>
        /// <param name="endDate">The end time for the hourly calculation window</param>
        /// <param name="bodyType">The body type...used for price calculation</param>
        /// <returns>NULL if the account is currently not calculated to be an hourly rate.  Otherwise, returns a temp invoice item 
        /// with name, customPrice, Quantity, and RateItem (that should be used by this account).  Note the RateItem will
        /// be the HOURLY storage item or the account/company default DAILY storage item</returns>
        public static Invoice.TempStorageItem CalculateHourlyStorageItem(Accounts.Account a, RateItem ri, DateTime startDate, DateTime endDate, Vehicle.BodyType bodyType)
        {
            if (a == null || ri == null || ri.CategoryId != 5)
                return null;

            var ari = Accounts.RateItem.GetByRateItem(a, ri);

            // no account pricing? return. Any pricing means that this account is opted in for this feature.
            if (ari == null)
                return null;

            var xImmediate = (TimeSpan)(endDate - startDate);
            if (xImmediate.TotalHours > ari.MaximumQuantity.GetValueOrDefault())
                return null;

            return new Invoice.TempStorageItem()
            {
                Name = ri.Name,
                Quantity = RateItem.GetTimedBasedTransformedCost(ri, null, startDate, endDate),
                Price = Accounts.RateItem.GetTransforemdCost(a, ari, bodyType),
                RateItem = ri
            };
        }

        // This method can be used to generate the child tiered items by passing the
        // parent tiered invoice item.  The child invoice items are added to the invoice
        //  - THIS METHOD DOES NOT SAVE THE TIERED ITEMS
        public static void GenerateTieredItems(Invoice invoice, InvoiceItem i, Vehicle.BodyType bodyType)
        {
            // doesn't apply to items that aren't associated with a RateItem.
            if (i.RateItem == null)
                return;

            // must provide the parent tiered storage item only please
            if (i.RateItem.ParentRateItemId > 0)
                return;

            // only apply to rates that are in the storage category
            if (i.RateItem.CategoryId != 5)
                return;

            // now lets add them.
            decimal totalDays = i.Quantity;
            decimal daysLeft = totalDays;

            // find the day ranges.
            decimal totalQuantity = 0;

            var children = RateItem.GetByParentId(i.RateItem.RateItemId);

            var rates = children.OrderBy(o => o.FreeQuantity).ToArray();

            // Debug.WriteLine("[TIERED] Generate Tiered Items, Days to generate for: " + i.Quantity);

            // step 1: start out with the total quantity, example: 15.5
            // step 2: sort the list of child rate items by the freeQuantity, so that if we have 1:$25, 2:$35, 11:$35, 13:$100
            // step 3: run through the list of child rates and add them to the invoiceItems list.


            var newItems = new List<Invoice.TempStorageItem>();

            // dont add any items if the days is 0.
            if (totalDays > 0)
            {
                for (int fi = 0; fi < rates.Length; fi++)
                {
                    var subRateItem = rates[fi];
                    InvoiceItem dailyItem = null;

                    // get the existing item, or create a new one.
                    dailyItem = invoice.InvoiceItems.FirstOrDefault(o => o.RateItem != null &&
                                                                         o.RateItem.RateItemId == subRateItem.RateItemId);

                    if (dailyItem == null)
                    {
                        dailyItem = new InvoiceItem()
                        {
                            InvoiceId = i.InvoiceId,
                            RateItem = subRateItem,
                            ClassId = subRateItem.DefaultClassId,
                            Taxable = i.RateItem.Taxable,
                            Locked = InvoiceItem.InvoiceItemLock.LockedByUser
                        };
                    }


                    #region set price
                    if (bodyType != null)
                    {
                        var extended = subRateItem.ExtendedRateItems.Where(o => o.Key == bodyType.Id).FirstOrDefault().Value;
                        if (extended != null)
                            dailyItem.CustomPrice = extended.Amount;
                        else
                            dailyItem.CustomPrice = subRateItem.Cost;
                    }
                    #endregion

                    #region set quantity
                    dailyItem.Quantity = totalDays - totalQuantity;

                    if (rates.Length > fi + 1)
                    {
                        var next = rates[fi + 1];
                        if (dailyItem.Quantity > next.FreeQuantity - subRateItem.FreeQuantity - 1)
                            dailyItem.Quantity = next.FreeQuantity - subRateItem.FreeQuantity;
                    }
                    #endregion

                    dailyItem.CustomName = dailyItem.Quantity + " days storage";

                    totalQuantity += dailyItem.Quantity;

                    // Debug.WriteLine("[TIERED] Add: " + dailyItem.Quantity + " @ " + dailyItem.Price);

                    dailyItem.RelatedInvoiceItemId = i.Id;

                    // Debug.WriteLine("[TIERED] Set RelatedId to " + i.Id);

                    newItems.Add(new Invoice.TempStorageItem()
                    {
                        Name = dailyItem.Name,
                        Price = dailyItem.Price,
                        Quantity = dailyItem.Quantity,
                        RateItem = dailyItem.RateItem
                    });

                    if (totalQuantity == totalDays)
                        break;
                }
            }

            // add tired items now as invoiceItems
            foreach (var newItem in newItems)
            {
                invoice.InvoiceItems.Add(new InvoiceItem()
                {
                    InvoiceId = i.InvoiceId,
                    RateItem = newItem.RateItem,
                    ClassId = newItem.RateItem.DefaultClassId,
                    Taxable = i.RateItem.Taxable,
                    Locked = InvoiceItem.InvoiceItemLock.LockedByUser,
                    CustomPrice = newItem.Price,
                    Quantity = newItem.Quantity,
                    CustomName = newItem.Name,
                    RelatedInvoiceItemId = i.Id
                });
            }
        }

        public static decimal CalculateDaysHeldBillable(int companyId, int? accountId, DateTime? impoundDate, DateTime? releaseDate)
        {
            var impound = new Impound();

            impound.Company = Extric.Towbook.Company.Company.GetById(companyId);
            impound.Account = accountId == null ? null : Extric.Towbook.Accounts.Account.GetById(accountId.Value);
            impound.ImpoundDate = impoundDate;
            impound.ReleaseDate = releaseDate == null ? DateTime.Now : releaseDate;

            return impound.GetDaysHeldBillable(impoundDate, releaseDate);
        }

        public static decimal CalculateDaysHeldBillable(StorageRate sr, int companyId, int? accountId, DateTime? impoundDate, DateTime? releaseDate)
        {
            var impound = new Impound();

            impound.ImpoundDate = impoundDate;
            impound.ReleaseDate = releaseDate == null ? DateTime.Now : releaseDate;
            impound.Company = Extric.Towbook.Company.Company.GetById(companyId);
            return impound.GetDaysHeldBillable(sr, impoundDate, impound.ReleaseDate);
        }

        public DateTime NextStorageDayIs()
        {
            StorageRate sr = null;

            if (this.Account != null)
                //TODO can be async
                sr = StorageRate.GetByAccountId(Company.Id, this.Account.Id);

            if (sr == null)
                sr = Company.StorageRate;

            return NextStorageDayIs(this._companyId,
                this._accountId > 0 ? (int?)this._accountId : null,
                this.ImpoundDate != null ? this.ImpoundDate : this.CreateDate,
                this.ReleaseDate,
                sr
            );

        }

        public static DateTime NextStorageDayIs(int companyId, int? accountId, DateTime? startDate, DateTime? endDate,
            StorageRate sr)
        {
            if (sr == null)
            {
                if (accountId != null)
                    //TODO can be async
                    sr = StorageRate.GetByAccountId(companyId, accountId.Value);

                if (sr == null)
                    sr = Extric.Towbook.Company.Company.GetById(companyId).StorageRate;
            }

            var days = CalculateDaysHeldBillable(sr, companyId, accountId, startDate, endDate);

            DateTime nextDate = DateTime.MinValue;

            if (sr.StorageGracePeriodHours > 0 && days == 0)
            {
                if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.ImmediateIgnoreGrace ||  
                    sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Each24HoursIgnoreGrace)
                    return startDate.Value;

                var newStartDate = startDate.Value.AddHours(sr.StorageGracePeriodHours);

                if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Midnight)
                {
                    startDate = newStartDate.Date;

                    if (sr.MidnightWaitForGracePeriod)
                        startDate = startDate.Value.AddDays(1);

                    return startDate.Value;

                }
                else
                {
                    return newStartDate;
                }
            }

            if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Midnight)
            {
                nextDate = startDate.Value.AddHours(sr.StorageGracePeriodHours).Date.AddDays(Convert.ToDouble(days));
            }
            else if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Immediate)
            {
                nextDate = startDate.Value.AddHours(sr.StorageGracePeriodHours).AddDays(Convert.ToDouble(days));
            }
            else if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.ImmediateIgnoreGrace)
            {
                nextDate = startDate.Value.AddDays(Convert.ToDouble(days));
            }
            else if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Each24HoursIgnoreGrace)
            {
                nextDate = startDate.Value.AddDays(Convert.ToDouble(days));
            }

            return nextDate;
        }

        /// <summary>
        /// Return the calculated number of billable days of this impound based on the storage rate
        /// provided. Optional dates can be used.
        /// IMPORTANT: dates must be in local company time.  The method will offset all dates passed!
        /// </summary>
        /// <param name="sr">The account or company storage rate</param>
        /// <param name="startDate">Optional starting date (in company local time) or the impound date will be used.</param>
        /// <param name="endDate">Optional ending date (in company local time). If not provided, the release date will be used.  Otherwise "now" will be used.</param>
        /// <param name="ri">Optional rateItem that can be passed for free quantity purposes</param>
		public decimal GetDaysHeldBillable(StorageRate sr, DateTime? startDate = null, DateTime? endDate = null, IRateItem ri = null)
        {
            if (sr == null)
                return DaysHeld;

            if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Midnight)
                return GetDaysHeldBillableMidnightOption(sr, startDate, endDate, ri);

            if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Immediate)
                return GetDaysHeldBillable24Option(sr, startDate, endDate, ri);

            if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.ImmediateIgnoreGrace || 
                sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Each24HoursIgnoreGrace)
                return GetDaysHeldBillableIgnoreGraceOption(sr, startDate, endDate, ri);

            return 0.0M;
        }

        /// <summary>
        /// Return the calculated number of billable days of this impound. Optional dates can be used.
        /// The account storage rate will be used first or the default company storage rate will be used.
        /// IMPORTANT: dates must be in local company time.  The method will offset all dates passed!
        /// </summary>
        /// <param name="startDate">Optional starting date (in company local time) or the impound date will be used.</param>
        /// <param name="endDate">Optional ending date (in company local time). If not provided, the release date will be used.  Otherwise "now" will be used.</param>
		public decimal GetDaysHeldBillable(DateTime? startDate = null, DateTime? endDate = null)
        {
            StorageRate sr = null;

            if (Account != null && Company != null)
                //TODO can be async
                sr = StorageRate.GetByAccountId(Company.Id, this.Account.Id);

            if (sr == null)
                sr = Company?.StorageRate;

            if (sr == null)
                return 0.0M;

            if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Midnight)
                return GetDaysHeldBillableMidnightOption(sr, startDate, endDate);
            else if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Immediate)
                return GetDaysHeldBillable24Option(sr, startDate, endDate);

            if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.ImmediateIgnoreGrace ||
                sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Each24HoursIgnoreGrace)
                return GetDaysHeldBillableIgnoreGraceOption(sr, startDate, endDate);

            return 0.0M;
        }


        /// <summary>
        /// Immediate (Every 24 Hours after the grace ends) Storage Option
        /// This method determines the days held but only when a full 24 hours has occured past the end of any grace period. 
        /// If no grace period is added, the calculations are taken 24 hours from the start date (impoundment date)
        /// </summary>
        /// <param name="sr">Storage rate that will provide the settings for calculations</param>
        /// <param name="startDate">The date that calculations should start from (impound date)</param>
        /// <param name="endDate">The date to end calculations at (release date or simply, "now")</param>
        /// <returns>The number of days held</returns>
        private decimal GetDaysHeldBillable24Option(StorageRate sr, DateTime? startDate = null, DateTime? endDate = null, IRateItem ri = null)
        {
            var nowDate = Core.OffsetDateTime(this.Company, DateTime.Now);
            var localDate = startDate != null ? Core.OffsetDateTime(this.Company, startDate.Value) : Core.OffsetDateTime(this.Company, this.ImpoundDate != null ? this.ImpoundDate.Value : this.CreateDate);

            DateTime? releasePickupDate = endDate != null ? (DateTime?)Core.OffsetDateTime(this.Company, endDate.Value) : ReleasePickupDate != null ? (DateTime?)Core.OffsetDateTime(this.Company, this.ReleasePickupDate.Value) : null;

            TimeSpan x;
            TimeSpan xImmediate;

            if (!releasePickupDate.HasValue)
            {
                xImmediate = (TimeSpan)(nowDate - localDate);
                x = (TimeSpan)(nowDate - localDate).Add(new TimeSpan(0, (int)(24 * sr.StorageRoundingValue), 0, 0));
            }
            else
            {
                x = (TimeSpan)(releasePickupDate.Value - localDate).Add(new TimeSpan(0, (int)(24 * sr.StorageRoundingValue), 0, 0));
                xImmediate = (TimeSpan)(releasePickupDate.Value - localDate);
            }

            // If a vehicle is impounded at 11:50pm, and it counts as one day of storage, and then midnight hits,
            // it would normally count as two days.  this property prevents it from charging more than 1 day for 
            // initial X number of hours [I swear this code keeps getting more and more confusing for times]

            // 5/6/2013 - must use xImmediate instead of x, otherwise value is always at least 24. 
            if (xImmediate.TotalHours < sr.StorageChargeInitialHoursLimitToOneDay)
            {
                // limit to one.
                x = new TimeSpan(0, (int)(24 * sr.StorageRoundingValue), 0, 0);
            }

            if (Account != null)
            {
                IRateItem r = (IRateItem)ri ?? Account.StorageRate;

                if (r != null && r.FreeQuantity > 0)
                {
                    if (r.FreeQuantity > 10000)
                        r.FreeQuantity = 10000; /* avoid max value of timespan */

                    x = x.Subtract(new TimeSpan((int)r.FreeQuantity * 24, 0, 0));
                }
            }

            if (sr.CompanyId == 18789)
            {
                if (xImmediate.TotalHours < sr.StorageGracePeriodHours)
                {
                    // there should be a better way to do this.. but this is so that we can return 0 if its within the first 6 hours. after the first 6 hours, then the grace period should be ignored.
                    return 0;
                }
                else
                {
                    sr = StorageRate.Copy(sr);
                    sr.StorageGracePeriodHours = 0;
                }
            }

            // if its zero... then charge immediately dont make a change... if its six hours 
            x = x.Subtract(new TimeSpan(sr.StorageGracePeriodHours, 0, 0));


            if (sr.FreeSundays || sr.FreeSundays)
            {
                var start = localDate;

                TimeSpan diff = x;
                int days = diff.Days;
                for (var i = 0; i <= days; i++)
                {
                    var testDate = start.AddDays(i);
                    switch (testDate.DayOfWeek)
                    {
                        case DayOfWeek.Saturday:
                            if (sr.FreeSaturdays)
                                x = x.Subtract(new TimeSpan(1, 0, 0, 0));
                            break;

                        case DayOfWeek.Sunday:
                            if (sr.FreeSundays)
                                x = x.Subtract(new TimeSpan(1, 0, 0, 0));
                            break;
                    }
                }
            }

            decimal final = Convert.ToDecimal(x.TotalDays);

            if (final < 0)
                final = 0;
            else
                final = Convert.ToDecimal(RoundDownToNearest(x.TotalDays, sr.StorageRoundingValue));

            if (Company?.State?.ToUpperInvariant() == "CA")
            {
                if (final < 1)
                    final = 1;
            }

            if (Company?.State?.ToUpperInvariant() == "CT")
            {
                // Connecticut must not charge for 8 hours of grace period for any storage item.
                // After 8 hours and under 12 hours, you may charge for half a day.
                // After 12 and up to 48 hours, you may charge 1 day.
                // 48+ hours goes to the regular
                // HOURS    RETURN
                // -----    ------
                // 0 - 8      0 days
                // 8 - 12   0.5 days
                // 12 - 48    1 day
                // 48 - 72    2 days

                if (x.TotalHours < 8)
                    final = 0;
                else if (xImmediate.TotalHours >= 8 && xImmediate.TotalHours < 12)
                    final = 0.5M;
                else if (xImmediate.TotalHours >= 12 && xImmediate.TotalHours < 48)
                    final = 1;
                else if (xImmediate.TotalHours >= 48 && xImmediate.TotalHours < 72)
                    final = 2;
                else
                    final = final - 1;
            }

            return final;
        }


        /// <summary>
        /// Midnight (Calendar Day Start) Storage Option
        /// This method determines the days held when a new calendar day occurs (Each Calendar Day at Midnight).  
        /// </summary>
        /// <param name="sr">Storage rate that will provide the settings for calculations</param>
        /// <param name="startDate">The date that calculations should start from (impound date)</param>
        /// <param name="endDate">The date to end calculations at (release date or simply, "now")</param>
        /// <returns>The number of days held</returns>
        private decimal GetDaysHeldBillableMidnightOption(StorageRate sr, DateTime? startDate = null, DateTime? endDate = null, IRateItem ri = null)
        {
            var nowDate = Core.OffsetDateTime(this.Company, DateTime.Now);
            var localDate = startDate != null ? Core.OffsetDateTime(this.Company, startDate.Value) : Core.OffsetDateTime(this.Company, this.ImpoundDate.Value);
            DateTime? releasePickupDate = endDate != null ? Core.OffsetDateTime(this.Company, endDate.Value) : ReleasePickupDate != null ? (DateTime?)Core.OffsetDateTime(this.Company, this.ReleasePickupDate.Value) : null;
            TimeSpan x = TimeSpan.MinValue;
            TimeSpan xImmediate = TimeSpan.MinValue;

            if (!releasePickupDate.HasValue)
            {
                if (nowDate == localDate)
                    nowDate = nowDate.AddMilliseconds(1);

                releasePickupDate = nowDate;
                xImmediate = (TimeSpan)(nowDate - localDate);
                x = (TimeSpan)(nowDate - localDate);
            }
            else
            {
                if (releasePickupDate.Value == localDate)
                    releasePickupDate = releasePickupDate.Value.AddMilliseconds(1);

                xImmediate = (TimeSpan)(releasePickupDate.Value - localDate);
                x = (TimeSpan)(releasePickupDate.Value - localDate);
            }

            if (Account != null)
            {
                IRateItem r = (IRateItem)ri ?? Account.StorageRate;

                if (r != null && r.FreeQuantity > 0)
                {
                    x = x.Subtract(new TimeSpan((int)r.FreeQuantity * 24, 0, 0));
                }
            }

            // Handle inital hours as no more than 1 day option
            if (xImmediate.TotalHours < sr.StorageChargeInitialHoursLimitToOneDay)
            {
                if (xImmediate.TotalHours > sr.StorageGracePeriodHours)
                    return Convert.ToDecimal(RoundDownToNearest(1, sr.StorageRoundingValue)); // limit to one.
            }


            // Exact time to midnight;
            TimeSpan toMidnightFirstDay = (TimeSpan)(new DateTime(localDate.Year, localDate.Month, localDate.Day).AddDays(1) - localDate);


            if (x.TotalHours < sr.StorageGracePeriodHours)
            {
                // Determine crossover occured (within grace period). We are concerned with first calendar day only
                var crossover = (localDate.AddHours(sr.StorageGracePeriodHours).Date - localDate.Date).Days != 0 && xImmediate >= toMidnightFirstDay;

                // within grace period and midnight crossover scenario
                if (crossover)
                {
                    if (!sr.MidnightWaitForGracePeriod /*Immediate - charge now*/)
                        x = new TimeSpan(1, 0, 0, 0);
                    else
                        x = new TimeSpan(0, 0, 0);
                }
                else
                    x = new TimeSpan(0, 0, 0, 0);
            }
            else
            {
                // get the number of calendar days after the grace period has ended
                var calendarDays = (releasePickupDate.Value.Date - localDate.Add(new TimeSpan(sr.StorageGracePeriodHours, 0, 0)).Date).Days;

                x = new TimeSpan((int)calendarDays + 1 /*grace period ended, forced day 1*/, 0, 0, 0);
            }

            if (sr.FreeSundays || sr.FreeSundays)
            {
                var start = localDate;

                TimeSpan diff = x;
                int days = diff.Days;
                for (var i = 0; i <= days; i++)
                {
                    var testDate = start.AddDays(i);
                    switch (testDate.DayOfWeek)
                    {
                        case DayOfWeek.Saturday:
                            if (sr.FreeSaturdays)
                                x = x.Subtract(new TimeSpan(1, 0, 0, 0));
                            break;

                        case DayOfWeek.Sunday:
                            if (sr.FreeSundays)
                                x = x.Subtract(new TimeSpan(1, 0, 0, 0));
                            break;
                    }
                }
            }

            decimal final = Convert.ToDecimal(RoundDownToNearest(x.TotalDays, sr.StorageRoundingValue));

            if (final < 0)
                final = 0;

            if (Company?.State?.ToUpperInvariant() == "CA")
            {
                if (final < 1)
                    final = 1;
            }

            return final;
        }


        /// <summary>
        /// Ignore grace (Uses impoundment date after grace period has ended) storage option
        /// This method determines the days held similar to the the immediate storage option with one exeption:
        /// the calculation are taken from the start date (impoundment date) essentially ignoring the grace period (once the grace period concludes).
        /// If no grace period is added, the calculations will match the Midnight (each calendar day) storge option.
        /// </summary>
        /// <param name="sr">Storage rate that will provide the settings for calculations</param>
        /// <param name="startDate">The date that calculations should start from (impound date)</param>
        /// <param name="endDate">The date to end calculations at (release date or simply, "now")</param>
        /// <returns>The number of days held</returns>
        private decimal GetDaysHeldBillableIgnoreGraceOption(StorageRate sr, DateTime? startDate = null, DateTime? endDate = null, IRateItem ri = null)
        {
            var nowDate = Core.OffsetDateTime(this.Company, DateTime.Now);
            var localDate = startDate != null ? Core.OffsetDateTime(this.Company, startDate.Value) : Core.OffsetDateTime(this.Company, this.ImpoundDate.Value);
            DateTime? releasePickupDate = endDate != null ? Core.OffsetDateTime(this.Company, endDate.Value) : ReleasePickupDate != null ? (DateTime?)Core.OffsetDateTime(this.Company, this.ReleasePickupDate.Value) : null;
            TimeSpan x = TimeSpan.MinValue;
            TimeSpan xImmediate = TimeSpan.MinValue;

            if (!releasePickupDate.HasValue)
            {
                if (nowDate == localDate)
                    nowDate = nowDate.AddMilliseconds(1);

                releasePickupDate = nowDate;
                xImmediate = (TimeSpan)(nowDate - localDate);
                x = (TimeSpan)(nowDate - localDate);
            }
            else
            {
                if (releasePickupDate.Value == localDate)
                    releasePickupDate = releasePickupDate.Value.AddMilliseconds(1);

                xImmediate = (TimeSpan)(releasePickupDate.Value - localDate);
                x = (TimeSpan)(releasePickupDate.Value - localDate);
            }

            if (Account != null)
            {
                IRateItem r = (IRateItem)ri ?? Account.StorageRate;

                if (r != null && r.FreeQuantity > 0)
                {
                    x = x.Subtract(new TimeSpan((int)r.FreeQuantity * 24, 0, 0));
                }
            }

            // Handle inital hours as no more than 1 day option
            if (xImmediate.TotalHours < sr.StorageChargeInitialHoursLimitToOneDay)
            {
                if (xImmediate.TotalHours > sr.StorageGracePeriodHours)
                    return Convert.ToDecimal(RoundDownToNearest(1, sr.StorageRoundingValue)); // limit to one.
            }


            // Exact time to midnight;
            TimeSpan toMidnightFirstDay = (TimeSpan)(new DateTime(localDate.Year, localDate.Month, localDate.Day).AddDays(1) - localDate);

            // Determine crossover occured (within grace period)
            var crossover = localDate.AddHours(sr.StorageGracePeriodHours).Day == localDate.AddHours(toMidnightFirstDay.TotalHours).Day;


            if (x.TotalHours < sr.StorageGracePeriodHours)
            {
                // within grace period and not midnight crossover scenario
                if (!(crossover && !sr.MidnightWaitForGracePeriod /*Immediate - charge now*/))
                    x = new TimeSpan(0, 0, 0);
            }
            else
            {
                // Note: Grace period ended

                if (xImmediate.TotalMinutes < toMidnightFirstDay.TotalMinutes)
                    x = new TimeSpan(1, 0, 0, 0);  // within the first day, no crossover.  Force to 1
                else
                {
                    if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.ImmediateIgnoreGrace)
                    {
                        var calendarDays = (releasePickupDate.Value.Date - localDate.Date).Days;
                        var totalNoOfDays = calendarDays + 1; //calendar days after grace period ends plus one for the grace period ending

                        x = new TimeSpan(totalNoOfDays, 0, 0, 0);
                    }
                    else if (sr.StorageChargeStart == StorageRate.StorageChargeStartEnum.Each24HoursIgnoreGrace)
                    {
                        var totalNoOfDays = x.Days + 1; //days since impound date plus one for the grace period ending
                        x = new TimeSpan(totalNoOfDays, x.Hours, x.Minutes, x.Seconds);
                    }
                }
            }

            if (sr.FreeSundays || sr.FreeSundays)
            {
                var start = localDate;

                TimeSpan diff = x;
                int days = diff.Days;
                for (var i = 0; i <= days; i++)
                {
                    var testDate = start.AddDays(i);
                    switch (testDate.DayOfWeek)
                    {
                        case DayOfWeek.Saturday:
                            if (sr.FreeSaturdays)
                                x = x.Subtract(new TimeSpan(1, 0, 0, 0));
                            break;

                        case DayOfWeek.Sunday:
                            if (sr.FreeSundays)
                                x = x.Subtract(new TimeSpan(1, 0, 0, 0));
                            break;
                    }
                }
            }

            decimal final = Convert.ToDecimal(RoundDownToNearest(x.TotalDays, sr.StorageRoundingValue));

            if (final < 0)
                final = 0;

            if (Company?.State?.ToUpperInvariant() == "CA")
            {
                if (final < 1)
                    final = 1;
            }

            return final;
        }

        /// <summary>
        /// Returns the number of days between Now and ImpoundDate, or ReleasePickupDate and ImpoundDate, if ReleaseDate isn't null.
        /// </summary>
        public decimal DaysHeldBillable
        {
            get
            {
                if (!ImpoundDate.HasValue)
                    return 0;

                return GetDaysHeldBillable();
            }
            set
            {
                // do nothing, this is only here for serialization, how can i avoid this?
            }
        }

        public static double RoundDownToNearest(double passednumber, double roundto)
        {
            if (roundto == 0)
                return passednumber;
            else
                return Math.Floor(passednumber / roundto) * roundto;
        }

        private List<HistoryItem> _history;

        public List<HistoryItem> History
        {

            get
            {
                if (_history == null)
                    _history = HistoryItem.GetByImpoundId(_id);

                return _history;
            }
        }

        private List<Note> _notes;

        public List<Note> Notes
        {
            get
            {
                if (_notes == null)
                    _notes = Note.GetByImpoundId(_id);

                return _notes;
            }
        }

        public Collection<Dispatch.InvoiceItem> InvoiceItems
        {
            get
            {
                return Invoice.InvoiceItems;
            }
        }

        public decimal InvoiceStorageTotal
        {
            get
            {
                decimal total = 0.0M;

                foreach (Dispatch.InvoiceItem ii in this.InvoiceItems)
                {
                    if (ii.RateItem != null &&
                        ((ii.RateItem.Predefined != null && ii.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE) ||
                            ii.RateItem.CategoryId == 5))
                        total += (ii.Price * ii.Quantity);
                }

                return total;
            }
        }

        public decimal InvoiceSubtotal
        {
            get
            {
                if (Invoice != null)
                    return Invoice.Subtotal;
                else
                    return 0;
            }
        }

        public decimal InvoiceTotal
        {
            get
            {
                return (InvoiceSubtotal + InvoiceTax);
            }
        }

        public decimal InvoiceTax
        {
            get
            {
                if (Invoice == null)
                    return 0;

                if (IsTaxExempt)
                    return 0;

                if (Id < 1)
                    return 0;

                if (Invoice != null)
                    return Invoice.Tax;
                else
                    return 0;
            }
        }

        public Accounts.Account Account
        {
            get
            {
                var d = this.DispatchEntry;

                if (d == null)
                    return null;

                if (d.Account != null && _accountId > 0 &&
                    d.Account.Id != _accountId)
                {
                    _accountId = DispatchEntry.Account.Id;
                }
                else if (d.Account != null && _accountId == 0)
                {
                    _accountId = d.Account.Id;
                }

                return Accounts.Account.GetById(_accountId);
            }
            set
            {
                if (value != null)
                    _accountId = value.Id;
            }
        }

        /// <summary>
        /// Date the vehicle was released from impound
        /// </summary>
        public DateTime? ReleaseDate
        {
            get
            {
                return _releaseDate;
            }
            set
            {
                if (_releaseDate != value)
                {
                    _releaseDate = value;

                    if (this.Invoice != null)
                    {
                        this.invoice.NextScheduledRecalculate = DateTime.Now;
                        this.invoice.UpdateStorageQuantities(true);
                    }
                }
            }
        }

        public ReleaseDetails ReleaseDetails
        {
            get
            {
                if (_releaseDetails == null)
                {
                    _releaseDetails =
                        Extric.Towbook.Impounds.ReleaseDetails.GetByImpound(this);
                }

                return _releaseDetails;
            }

        }

        public Nullable<DateTime> ReleasePickupDate
        {
            get
            {
                return _releasePickupDate;
            }
            set
            {
                _releasePickupDate = value;
            }
        }

        public int? ReleaseReason
        {
            get
            {
                return _releaseReason;
            }
            set
            {
                _releaseReason = value;
            }
        }

        public string ReleaseNotes
        {
            get
            {
                return _releaseNotes;
            }
            set
            {
                _releaseNotes = value;
            }
        }

        private Entry _dispatchEntry;
        public Entry DispatchEntry
        {
            get
            {
                if (_dispatchEntry == null)
                    _dispatchEntry = Entry.GetById(_dispatchEntryId);

                return _dispatchEntry;
            }
            set
            {
                _dispatchEntryId = value.Id;
            }
        }

        public int DispatchEntryId => _dispatchEntryId;

        public string PropertyNumber
        {
            get
            {
                return _propertyNumber;
            }
            set
            {
                _propertyNumber = value;
            }
        }

        /// <summary>
        /// If set to True, the vehicle should not be released.
        /// </summary>
        public bool Hold
        {
            get { return _hold; }
            set { SetField(ref _hold, value, "PoliceHold"); }
        }

        public Nullable<ImpoundType> ImpoundType
        {
            get { return _impoundType; }
            set { _impoundType = value; }
        }

        public string Reason
        {
            get { return _reason; }
            set { _reason = value; }
        }

        public bool Deleted
        {
            get { return _deleted; }
            set { _deleted = value; }
        }

        public async Task<bool> UpdateStatus(int statusId, User performer)
        {
            return await UpdateStatus(statusId, performer, null);
        }
        /// <summary>
        /// Updates the status of the current impound to the specified StatusId.
        /// Setting this does not have any relation to Releasing the vehicle, currently.
        /// </summary>
        /// <param name="statusId"></param>
        /// <param name="performer"></param>
        /// <param name="notes"></param>
        /// <returns></returns>
        public async Task<bool> UpdateStatus(int statusId, User performer, string notes)
        {
            if (Id < 1)
                throw new TowbookException("cannot update status before Impound is created");

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "ImpoundsUpdateStatusById",
                new SqlParameter("@ImpoundId", Id),
                new SqlParameter("@StatusId", statusId),
                new SqlParameter("@UserId", performer.Id),
                new SqlParameter("@Notes", notes));

            var oldStatus = CurrentStatus;

            CurrentStatus = await Status.GetByIdAsync(statusId, this.Company.Id);

            HistoryItem hi = new HistoryItem();
            hi.ImpoundId = Id;
            hi.User = performer;
            hi.Action = HistoryItem.ActionEnum.StatusChange;
            hi.Data = oldStatus.Name + "|" + CurrentStatus.Name;
            hi.Save();

            return false;
        }
    }
}
