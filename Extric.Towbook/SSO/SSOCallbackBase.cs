using System;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.SSO
{
    public abstract class SSOCallbackBase : ISSOCallback
    {
        public const string UserKeySSO = "sso";
        public const string UserReferenceSSO = "Single Sign On";

        protected const string SSO_KEY_FORMAT = "ssoUser:{0}";
        protected const string RETURN_MOBILE_FORMAT = "towbook://sso/{0}";

        protected Guid _guid;

        protected Guid Guid => _guid;
        public string SsoKey => string.Format(SSO_KEY_FORMAT, _guid);

        public abstract SSOTokens SsoTokens { get; }

        public abstract bool IsMobileRequest { get; }

        public abstract string SSO_TYPE { get; }

        protected async Task<User> CheckAndGetUserAsync(string userName, string email, string fullName, int companyId, User.TypeEnum type)
        {
            // TODO: include companyId in lookup, so that we don't get a user from another company
            var user = User.GetByUsername(userName);

            if (user != null && !await user.HasAccessToCompanyAsync(companyId))
                return null;

            if (user == null || user.Id <= 0)
            {
                user = new User
                {
                    Username = userName,
                    FullName = fullName,
                    Email = email,
                    CompanyId = companyId,
                    Type = type,
                    Password = GenerateCode(16)
                };

                await user.Save(true);
                user.AddKey(UserKeySSO, SSO_TYPE, UserReferenceSSO);
            }
            return user;
        }

        public abstract Task<User> GetUserAsync();

        public abstract DateTimeOffset ValidTo();

        public abstract string GetRedirectUrl();

        public abstract void RegisterTokenHint(string uniqueKey);

        protected static string GenerateCode(int size)
        {
            var builder = new StringBuilder();
            var random = new Random();
            char ch;
            for (int i = 1; i < size + 1; i++)
            {
                ch = Convert.ToChar(Convert.ToInt32(Math.Floor(26 * random.NextDouble() + 65)));
                builder.Append(ch);
            }
            return builder.ToString();
        }

    }
}
