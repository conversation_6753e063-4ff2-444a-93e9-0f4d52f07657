using Extric.Towbook.Accounts;
using Extric.Towbook.ActivityLogging;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch
{

    public interface IDigitalContractor
    {
        public int CompanyId { get; set; }
        public int AccountId { get; set; }
        public string ContractorId { get; }
        void Save();
    }
    public interface IDigitalContractorAsync
    {
        public int CompanyId { get; set; }
        public int AccountId { get; set; }
        public string ContractorId { get; }
        Task Save();
    }

    public class CallRequestExtraModel
    {
        public decimal Amount { get; set; }
        public string Type { get; set; }
    }


    public class DistanceModel
    {
        public decimal? ClosestDriver { get; set; }
        public decimal? CompanyDistance { get; set; }
        
        public decimal? LoadedMileage { get; set; }
        public decimal? UnloadedMileage { get; set; }
        public decimal? DestinationToCompanyDistance { get; set; }

        public decimal? OfferAmount { get; set; }
        public decimal? GoaAmount { get; set; }
    }

    /// <summary>
    /// Represents a Call Request from a third-party such as Agero, Geico, Allstate, Road America, or Towsquare.
    /// </summary>
    [Table("DispatchEntryRequests")]
    public class CallRequest : TrackableObject, IUsesSqlKey
    {
        private CallRequestStatus status;
        private int? dispatchEntryId;

        [Key]
        public int CallRequestId { get; set; }
        public int CompanyId { get; set; }
        public int AccountId { get; set; }

        [Write(false)]
        [Ignore(true)]
        public string AccountName { get; set; }

        [Write(false)]
        [Ignore(true)]
        public int MasterAccountId { get; set; }

        [Write(false)]
        [Ignore(true)]
        public decimal OfferAmount { get; set; }
        
        [Write(false)]
        [Ignore(true)]
        public string OfferTypeText { get; set; }


        [Write(false)]
        [Ignore(true)]
        public int? DefaultEta { get; set; }

        public string StartingLocation { get; set; }
        public string Reason { get; set; }
        public string ServiceNeeded { get; set; }
        public string Vehicle { get; set; }
        public int? OwnerUserId { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string TowDestination { get; set; }
        public string ProviderId { get; set; }

        /// <summary>
        /// Comma delimitered DriverId's that were picked by the MC to assign this job to.
        /// </summary>
        [Newtonsoft.Json.JsonConverter(typeof(StringToIntArrayConverter))]
        public string Drivers { get; set; }
        [Newtonsoft.Json.JsonConverter(typeof(StringToIntArrayConverter))]
        public string Trucks { get; set; }

        /// <summary>
        /// If the ETA the user responds with is above this number, specify the ETA reason when responding.
        /// </summary>
        public int MaxEta { get; set; }

        [Write(false)]
        public int[] SupportedEtas { get; set; } = new int[] {
            5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 90, 99,
            100,110, 120, 150, 180, 210, 240, 300, 360, 420, 480, 540, 600, 1000, 1200
        };

        /// <summary>
        /// Available actions for the call. 
        /// ACCEPT, REJECT, REQUEST_CALL
        /// </summary>
        [Write(false)]
        public string[] AvailableActions
        {
            get
            {
                if (MasterAccountId == 0)
                    return Array.Empty<string>();

                var blocked = new int[] {
                    MasterAccountTypes.RoadAmerica,
                    MasterAccountTypes.Allstate,
                    MasterAccountTypes.Urgently,
                    MasterAccountTypes.Swoop,
                    MasterAccountTypes.Gerber,
                    MasterAccountTypes.Honk,
                    MasterAccountTypes.Towbook,
                    MasterAccountTypes.OonAgero,
                    MasterAccountTypes.OonUrgently,
                    MasterAccountTypes.OonSwoop,
                    MasterAccountTypes.OonQuest,
                    MasterAccountTypes.OonAllstate,
                    MasterAccountTypes.OonRoadsideProtect,
                    MasterAccountTypes.OonTrx,
                    MasterAccountTypes.AaaAce,
                    MasterAccountTypes.AaaAcg,
                    MasterAccountTypes.AaaNationalFsl,
                    MasterAccountTypes.AaaNational,
                    MasterAccountTypes.AaaWashington,
                    MasterAccountTypes.AaaNewYork,
                    MasterAccountTypes.StackThreeAtlas,
                    MasterAccountTypes.DrivenSolutions
                };

                if (blocked.Contains(MasterAccountId))
                    return new string[] { "ACCEPT", "REJECT" };
                else
                    return new string[] { "ACCEPT", "REJECT", "REQUEST_CALL" };
            }
        }

        /// <summary>
        /// The time that the call MUST be accepted/rejected. If it isn't, the requester may call you, or send it to another towing company.
        /// </summary>
        public DateTime? ExpirationDate { get; set; }
        public DateTime RequestDate { get; set; }

        [Write(false)]
        public DateTime? ExpirationDateUtc { get; set; }
        [Write(false)]
        public DateTime RequestDateUtc { get; set; }

        /// <summary>
        /// If this request results in a DispatchEntryId being created, record it in this property.
        /// </summary>
        public int? DispatchEntryId
        {
            get { return dispatchEntryId; }
            set { SetField(ref dispatchEntryId, value, "DispatchEntryId"); }
        }

        /// <summary>
        /// Status of the request.. 0-Default, 1-Accepted, 2-Rejected, 3-Deferred.
        /// </summary>
        [Write(false)]
        [Ignore(true)]

        public CallRequestStatus Status
        {
            get { return status; }
            private set { status = value; }
        }


        public bool HasAlreadyRespondedTo()
        {
            if (Status == CallRequestStatus.Accepted ||
                Status == CallRequestStatus.Rejected ||
                Status == CallRequestStatus.Expired ||
                Status == CallRequestStatus.PhoneCallRequested ||
                Status == CallRequestStatus.Cancelled)
            {
                return true;
            }
            return false;
        }

        public int? ResponseReasonId { get; set; }

        /// <summary>
        /// Distance from your home-base to the incident location.
        /// </summary>
        public double? Distance { get; set; }

        /// <summary>
        /// Distance from the incident location to tow destination.
        /// </summary>
        public double? LoadedDistance { get; set; }

        /// <summary>
        /// The ETA provided by the user to send back to the motor club. 
        /// </summary>
        public int? Eta { get; set; }

        /// <summary>
        /// The latitude of the starting location.
        /// </summary>
        public decimal? StartLocationLatitude { get; set; }
        /// <summary>
        /// The longitude of the starting location.
        /// </summary>
        public decimal? StartLocationLongitude { get; set; }

        public CallRequest()
        {
            status = CallRequestStatus.None;
        }

        public static CallRequest GetById(int id)
        {
            if (id == 0)
                return null;

            var sqlRes = SqlMapper
                .Query<CallRequest>("SELECT * FROM DispatchEntryRequests WHERE CallRequestId = @Id", new { Id = id })
                .FirstOrDefault();

            var s = Map(sqlRes);
            s?.MarkAsClean();

            return s;
        }

        public static async Task<CallRequest> GetByIdAsync(int id)
        {
            if (id == 0)
                return null;

            var sqlRes = (await SqlMapper
                .QueryAsync<CallRequest>("SELECT * FROM DispatchEntryRequests WHERE CallRequestId = @Id", new { Id = id }))
                .FirstOrDefault();

            var s = Map(sqlRes);
            s?.MarkAsClean();

            return s;
        }

        /// <summary>
        /// Retrieves a call request by the specified account and the purchase order number field.. 
        /// </summary>
        /// <param name="dispatchId">PurchaseOrderNumber</param>
        /// <returns></returns>
        public static CallRequest GetByForeignId(int accountId, string foreignId)
        {
            var s = Map(SqlMapper.Query<CallRequest>(
                "SELECT TOP 1 * FROM DispatchEntryRequests WITH (NOLOCK) WHERE CallRequestId > ********* AND " +
                "AccountId=@AccountId AND PurchaseOrderNumber=@ForeignId ORDER BY 1 DESC",
                new { AccountId = accountId, ForeignId = foreignId }).FirstOrDefault());

            s?.MarkAsClean();

            return s;
        }

        private static CallRequest Map(CallRequest s)
        {
            if (s == null)
                return null;

            s.RequestDateUtc = s.RequestDate.ToUniversalTime();

            if (s.ExpirationDate != null)
                s.ExpirationDateUtc = s.ExpirationDate.Value.ToUniversalTime();

            return s;
        }

        public static CallRequest GetByDispatchId(int dispatchEntryId)
        {
            var s = Map(SqlMapper.Query<CallRequest>("SELECT * FROM DispatchEntryRequests WITH (nolock) WHERE DispatchEntryId = @Id", 
                new { Id = dispatchEntryId }).FirstOrDefault());

            if (s != null)
                s.MarkAsClean();

            return s;
        }

        public static async Task<CallRequest> GetByDispatchEntryId(int dispatchEntryId)
        {
            var s = Map((await SqlMapper.QueryAsync<CallRequest>("SELECT * FROM DispatchEntryRequests WITH (nolock) WHERE DispatchEntryId = @Id",
                new { Id = dispatchEntryId })).FirstOrDefault());

            if (s != null)
                s.MarkAsClean();

            return s;
        }

        private async Task ValidateSaveAsync()
        {
            var acc = await Account.GetByIdAsync(AccountId);

            if (acc == null)
                throw new TowbookException("Account #" + AccountId + " does not exist");

            var ma = await MasterAccount.GetByIdAsync(acc.MasterAccountId);

            if (ma == null)
            {
                acc = await Account.GetByIdWithoutCacheAsync(AccountId);
                ma = await MasterAccount.GetByIdAsync(acc.MasterAccountId);

                if (ma == null)
                    throw new TowbookException("MasterAccount is null for account # " + AccountId);
                else
                {

                    Cache.Instance.PartitionSet(acc);
                    Cache.Instance.Set(acc);
                }
            }
        }

        private async Task HandleDistance()
        {
            try
            {
                if (Distance != null && Distance != 0)
                    return;

                var incident = this.StartingLocation;
                var incidentGc = await GeocodeHelper.Geocode(incident);

                var c = Company.Company.GetById(CompanyId);

                var homeBase = c.GetComposedAddress();

                if (AccountId > 0)
                {
                    var abe = await Company.AddressBookEntry.GetByAccountId(AccountId, "MapFrom");

                    if (abe != null)
                        homeBase = abe.ToString();
                }

                var homeBaseGc = await GeocodeHelper.Geocode(homeBase);
                if (homeBaseGc != null)
                {
                    StartLocationLatitude = homeBaseGc.Latitude;
                    StartLocationLongitude = homeBaseGc.Longitude;
                }
                if (incidentGc != null && homeBaseGc != null)
                {
                    var rc = await DistanceMatrixUtility.GetMatrixAsync(
                        $"{homeBaseGc.Latitude},{homeBaseGc.Longitude}",
                        $"{incidentGc.Latitude},{incidentGc.Longitude}");

                    if (rc != null)
                        Distance = Math.Round((double)rc.Miles, 1);
                }

                if (!string.IsNullOrWhiteSpace(TowDestination))
                {
                    string destinationFormatted = GeocodeHelper.FormatAddressForGeocoding(TowDestination);
                    var destinationGc = await GeocodeHelper.Geocode(destinationFormatted);
                    var destinationMat = await DistanceMatrixUtility.GetMatrixAsync(
                        $"{incidentGc.Latitude},{incidentGc.Longitude}",
                        $"{destinationGc.Latitude},{destinationGc.Longitude}");

                    if (destinationMat != null)
                        LoadedDistance = Math.Round((double)destinationMat.Miles, 1);
                }

                // todo: log this metric.
                // Console.WriteLine(sw.ElapsedMilliseconds + "ms");
            }
            catch
            {
                // todo: record. 
            }
        }

        public async Task Save()
        {
            await HandleDistance();
            await ValidateSaveAsync();

            if (Distance != null)
            {
                if (Distance < 0)
                    Distance = null;

                Distance = Math.Round(Distance.Value, 1);
            }

            if (this.CallRequestId == 0)
            {
                this.CallRequestId = Convert.ToInt32(SqlMapper.Insert(this));
            }
            else
            {
                SqlMapper.Update(this);
            }
        }

        public async Task SaveAsync()
        {
            await HandleDistance();
            await ValidateSaveAsync();

            if (this.CallRequestId == 0)
            {
                this.CallRequestId = Convert.ToInt32(SqlMapper.Insert(this));
                var x = await Deliver();
            }
            else
            {
                SqlMapper.Update(this);
            }
        }

        public async Task<bool> UpdateStatus(CallRequestStatus newStatus, int? ownerUserId = null, string po = null, int? eta = null, string title = null, string message = null)
        {
            if (this.CallRequestId == 0)
                return false;

            if (newStatus == this.Status && this.PurchaseOrderNumber == po)
                return true;

            if (ownerUserId == null)
                ownerUserId = this.OwnerUserId;

            var r = SqlMapper.Query<dynamic>(
                @"UPDATE DispatchEntryRequests 
                    SET Status=@Status, OwnerUserId=@OwnerUserId, ResponseReasonId=@ResponseReasonId " +
                    (!string.IsNullOrWhiteSpace(po) ? ", PurchaseOrderNumber=@PoNumber " : "") +
                    (eta != null ? ", Eta=@Eta " : "") +
                    @"OUTPUT inserted.Status WHERE CallRequestId=@CallRequestId AND (Status IS NULL OR Status=@OriginalStatus)",
                 new
                 {
                     CallRequestId = this.CallRequestId,
                     OriginalStatus = this.Status,
                     OwnerUserId = ownerUserId,
                     Status = newStatus,
                     ResponseReasonId = this.ResponseReasonId,
                     PoNumber = po,
                     Eta = eta
                 }).FirstOrDefault();

            if (newStatus == this.Status)
                return true;

            if (r != null)
            {
                this.Status = newStatus;
                this.OwnerUserId = ownerUserId;

                await PushNotificationProvider.UpdateCallRequestStatus(this.CompanyId, this.CallRequestId, this.Status);
                try
                {

                    var c = Company.Company.GetById(this.CompanyId);
                    var a = await Account.GetByIdAsync(this.AccountId);

                    if (c.HasFeature(Features.MotorClubIntegration_MobileNotifications))
                    {
                        var usersToNotify = User.GetByCompanyId(c.Id).Where(o => o.Disabled == false && o.Deleted == false &&
                            (o.Type == User.TypeEnum.Manager || o.Type == User.TypeEnum.Dispatcher) &&
                            (!(o.Notes ?? "").Contains("DisableMobileNotifications")));

                        if (newStatus == CallRequestStatus.RejectedByMotorClub)
                        {
                            string rmessage = "MC Rejected your accept response - ETA Not Approved";

                            if (a.MasterAccountId == 4)
                                rmessage = "Quest rejected your ETA. Job not accepted.";
                            else if (a.MasterAccountId == 2)
                                rmessage = "Allstate rejected your ETA. Job not accepted";
                            else
                                return true;


                            if (message != null)
                                rmessage = message;

                            var tasks = new Collection<Task>();

                            foreach (var u in usersToNotify)
                            {
                                var values = new Dictionary<string, string>();

                                values.Add("Message", rmessage);
                                values.Add("Received Time", Core.OffsetDateTime(c, DateTime.Now).ToShortTowbookTimeString());

                                var keys = u.GetKeys();

                                if (keys.Any(o => o.Key == "notificationhub_registration_id"))
                                {
                                    tasks.Add(NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(u,
                                        "Digital Dispatch Response Rejected",
                                        values, true, "Digital Dispatch Response"));
                                }
                            }
                            await Task.WhenAll(tasks);
                        }
                    }

                    if (a.MasterAccountId == MasterAccountTypes.Honk)
                    {
                        if (newStatus == CallRequestStatus.AnotherProviderResponded && this.OwnerUserId.GetValueOrDefault() > 1)
                        {
                            var userToNotify = await User.GetByIdAsync(this.OwnerUserId.Value);
                            if (userToNotify != null)
                            {
                                var tasks = new Collection<Task>();

                                var values = new Dictionary<string, string>();

                                values.Add("Message", message ?? "The job you tried to accept was accepted by another provider.");
                                values.Add("Received Time", Core.OffsetDateTime(c, DateTime.Now).ToShortTowbookTimeString());

                                var keys = userToNotify.GetKeys();

                                if (keys.Any(o => o.Key == "notificationhub_registration_id"))
                                {
                                    tasks.Add(NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(userToNotify,
                                        title ?? "Digital Dispatch was accepted by another provider.",
                                        values, true, "Digital Dispatch Response"));
                                }

                                await Task.WhenAll(tasks);
                            }
                        }
                    }

                    // Trigger Event notification
                    if (c.HasFeature(Features.Notifications_StandardEventNotifications))
                    {
                        var item = new EventNotifications.DigitalDispatchQueueItem();
                        if (newStatus == CallRequestStatus.Expired)
                            item.Type = EventNotifications.DigitalDispatchTriggerType.DigitalOfferMissed;
                        else if (newStatus == CallRequestStatus.Rejected)
                            item.Type = EventNotifications.DigitalDispatchTriggerType.DigitalDispatchRejectedByUser;
                        else if (newStatus == CallRequestStatus.Accepted)
                            item.Type = EventNotifications.DigitalDispatchTriggerType.DigitalDispatchAwarded;
                        else if (newStatus == CallRequestStatus.RejectedByMotorClub)
                            item.Type = EventNotifications.DigitalDispatchTriggerType.DigitalDispatchRejectedByMotorClub;
                        else if (newStatus == CallRequestStatus.Cancelled)
                            item.Type = EventNotifications.DigitalDispatchTriggerType.CallCancelledByMotorClub;
                        //else
                        //    item.Type = EventNotifications.DigitalDispatchTriggerType.CallModifiedByMotorClub;

                        if (item.Type != EventNotifications.DigitalDispatchTriggerType.Unspecified)
                        {
                            item.CallRequestId = CallRequestId;
                            await item.TriggerEvent();
                        }
                    }
                }
                catch
                {

                }
                this.status = newStatus;

                return true;
            }
            else
            {
                return false;
            }
        }

        public static async Task<bool> ShouldReceiveCallRequestsAsync(User u, bool includeDrivers)
        {
            if (u == null)
                return false;

            if (u.Type != User.TypeEnum.Driver || includeDrivers)
            {
                var kv = (await UserKeyValue.GetByUserAsync(u.CompanyId, u.Id, Provider.Towbook.ProviderId, "DisableAcceptReject")).FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            else
            {
                // drivers cannot receive call requests under any circumstance so dont bother checking.
                return false;
            }
        }

        public async Task<bool> Deliver() => await Deliver(false);

        public async Task<bool> Deliver(bool includeDrivers, string extra = null)
        {
            if (ExpirationDate != null &&
                ExpirationDate.Value < DateTime.Now.AddMinutes(-5))
                return false;

            var a = await Account.GetByIdAsync(this.AccountId);

            var title = "Digital Dispatch Offer from " + a.Company;

            if (!string.IsNullOrWhiteSpace(extra))
                title += " " + extra;

            // TODO: make it send a Push notification to all signed in dispatchers too.
            await PushNotificationProvider.Push(CompanyId, "call_request", 
                new
                {
                    id = this.CallRequestId,
                    title = title,
                });

            var users = User.GetByCompanyId(CompanyId)
                .Where(o => o.Deleted == false && o.Disabled == false &&
                    (o.Type == User.TypeEnum.Dispatcher || 
                    o.Type == User.TypeEnum.Manager ||
                    (includeDrivers && o.Type == User.TypeEnum.Driver)));

            if (Core.GetAppSetting("Towbook:CallRequests:Disable") == "1")
                return true;

            if (Core.GetAppSetting("PushNotifications:Disable") == "1")
                return true;


            foreach (var user in users)
            {
                if ((user.Notes ?? "").Contains("DisableLiveDispatch"))
                    continue;

                if (!await ShouldReceiveCallRequestsAsync(user, includeDrivers))
                    continue;

                if (user.GetKeys().Any(o => o.Key == "notificationhub_registration_id"))
                {
                    await NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(user,
                        title,
                        new Dictionary<string, string> {
                            { "callRequestId", this.CallRequestId.ToString() },
                            { "accountId", this.AccountId.ToString() },
                            { "masterAccountId",  a.MasterAccountId.ToString() }
                        }, false, "Digital Dispatch Request");
                }
            }

            return true;
        }

        public static async Task<IEnumerable<CallRequest>> GetCurrentByCompanyId(
            int companyId)
        {
            return (await SqlMapper
               .QueryAsync<CallRequest>("SELECT * FROM DispatchEntryRequests WITH (nolock) WHERE CompanyId=@Id AND Status=0", new { Id = companyId }))
               .Select(o => Map(o))
               .ToList();
        }

        public static List<CallRequest> GetMissedByCompanyId(int companyId)
        {
            return SqlMapper.Query<CallRequest>(
                "SELECT TOP 50 DER.* FROM DispatchEntryRequests DER INNER JOIN Accounts A on A.AccountId=DER.AccountID AND A.MasterAccountId=5 WHERE DER.CompanyId=@Id AND DER.[Status] IN (5,3,9,6) ORDER BY RequestDate DESC",
                new { Id = companyId })
                .Select(o => Map(o))
                .ToList();
        }
    }

    public enum CallRequestStatus
    {
        None = 0,
        Accepted = 1,
        Rejected = 2,
        PhoneCallRequested = 3,
        Cancelled = 4,
        Expired = 5,
        Accepting = 6,
        Rejecting = 7,

        RequestingPhoneCall = 8,
        AutomatedPhoneCallRequested = 9,
        
        AcceptSent = 10,
        AcceptFailed = 21,
        RejectFailed = 22,
        PhoneCallRequestFailed = 23,

        AcceptFailedExpired = 31,
        RejectFailedExpired = 32,
        PhoneCallRequestExpired = 33,

        AnotherDispatcherResponded = 50,
        UnknownError = 51,

        RejectedByMotorClub = 40,
        ServiceNoLongerNeeded = 41,

        GoaRequested = 70,
        GoaApprovedByMotorClub = 71,
        GoaRejectedByMotorClubServiceStillNeeded = 72,
        GoaRejectedByMotorClubNotEligible = 73,

        AnotherProviderResponded = 80,

        ServiceFailureConfirmed = 90
    }
}
