using System;
using System.Collections.Generic;
using System.Data.SqlClient;

using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System.Linq;
using System.Collections.ObjectModel;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using System.ComponentModel;

using Async = System.Threading.Tasks;
using System.Threading.Tasks;

namespace Extric.Towbook
{
    [Serializable]
	[CacheKey("_trucks")]
	[ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Truck")]
	public class Truck : Company.ITruck
	{
        private const int CacheTimeout = 120;

		public enum TruckType
		{
            Unspecified = 0,
			Wrecker = 1,
			[Description("Flat Bed")]
			FlatBed = 2,
            Other = 3,
			[Description("Service Vehicle")]
			ServiceVehicle = 4,
			Rotator = 5,
			Tractor = 6,
			Trailer = 7,
			Container = 8,
			Hazmat = 9
		}

        public enum DutyType
        {
            Unspecified = 0,
            Medium = 1,
            Heavy = 2,
            Other = 3,
			Light = 4
        }

		public static Truck None
		{
			get
			{
				var mn = new Truck();
				mn._id = 1;
				mn._name = "(none)";

				return mn;
			}
		}

		private int _id;
		private int _companyId;
		private string _name;
		private string _description;

		private string _vin;
		private int _year;
		private TruckType _type;
        private DutyType _duty;
		private string _manufacturer;
		private string _model;
		private int _odometer;

        private string _insuranceCompany;
		private string _insurancePolicyNumber;
		private DateTime? _insuranceExpirationDate;

		private string      _plateNumber;
		private DateTime?   _plateExpirationDate;

        private string _notes;

		private bool _active;


		private DateTime _createDate;

        public int[] Companies { get; private set; }

        public string ReferenceNumber { get; set; }

		public Truck()
		{

		}

        [Obsolete("Prefer using GetById method instead")]        
		public Truck(int id)
		{
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "TrucksGetById", new SqlParameter("@Id", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    throw new TowbookException("Truck doesn't exist!");
                }
            }
		}

		protected Truck(SqlDataReader reader)
		{
			InitializeFromDataReader(reader);
            AppServices.Cache.Add("truck:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
		}

        public static Truck GetById(int id)
        {
            if (id < 1)
                return null;

            return AppServices.Cache.Get("truck:" + id, TimeSpan.FromMinutes(CacheTimeout), () =>
			{
				var truck = Cache.Instance.Get(id, (int truckId) => GetByIdWithoutCache(truckId));
				
				// needed to fix a bug that was introduced when we first started storing trucks in Redis. 
				// they would get saved without Companies set.
				if (truck != null && truck.Companies == null)
				{
					var td = GetByIdWithoutCache(id);
					if (td.Companies != null)
					{
						Cache.Instance.PartitionSet(td);
						Cache.Instance.Set(td);
					}

					return td;
				}

				return truck;
			});
        }

        public static async Task<Truck> GetByIdAsync(int id)
        {
            if (id < 1)
                return null;

            return await AppServices.Cache.GetAsync("truck:" + id, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                var truck = await Cache.Instance.GetAsync(id, async (int truckId) => await GetByIdWithoutCacheAsync(truckId));
                // needed to fix a bug that was introduced when we first started storing trucks in Redis. 
                // they would get saved without Companies set.
                if (truck != null && truck.Companies == null)
                {
                    var td = await GetByIdWithoutCacheAsync(id);
                    if (td.Companies != null)
                    {
                        Cache.Instance.PartitionSet(td);
                        Cache.Instance.Set(td);
                    }

                    return td;
                }

                return truck;
            });
        }

        public static Truck GetByIdWithoutCache(int id)
        {
            if (id < 1)
                return null;
            //Method async already exist
            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "TrucksGetById", new SqlParameter("@TruckId", id)))
            {
                if (dr.Read())
                {
                    var t = new Truck(dr);

                    AppServices.Cache.Add("truck:" + id, TimeSpan.FromMinutes(CacheTimeout), t);

                    return t;
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<Truck> GetByIdWithoutCacheAsync(int id)
        {
            if (id < 1)
                return null;

            using (var dr = await SqlHelper.ExecuteReaderAsync(
                Core.ConnectionString, 
                System.Data.CommandType.StoredProcedure, 
                "TrucksGetById", 
                new SqlParameter("@TruckId", id)))
            {
                while (await dr.ReadAsync())
                {
                    var t = new Truck(dr);
                    AppServices.Cache.Add("truck:" + id, TimeSpan.FromMinutes(CacheTimeout), t);
                    return t;
                }

                return null;
            }
        }

        private void InitializeFromDataReader(SqlDataReader reader)
		{
			_id = Convert.ToInt32(reader["TruckId"]);
			_companyId = Convert.ToInt32(reader["CompanyId"]);

			if (reader["Description"] != DBNull.Value)
				_description = Convert.ToString(reader["Description"]);

			if (reader["VIN"] != DBNull.Value)
				_vin = Convert.ToString(reader["VIN"]);

			if (reader["TruckYear"] != DBNull.Value)
				_year = Convert.ToInt32(reader["TruckYear"]);

			if (reader["TruckType"] != DBNull.Value) 
				_type = (TruckType)Convert.ToInt32(reader["TruckType"]);

            if (reader["Duty"] != DBNull.Value)
                _duty = (DutyType)Convert.ToInt32(reader["Duty"]);

			if (reader["TruckManufacturer"] != DBNull.Value)
				_manufacturer = reader["TruckManufacturer"].ToString();

			if (reader["TruckModel"] != DBNull.Value) 
				_model = reader["TruckModel"].ToString();

			if (reader["Odometer"] != DBNull.Value) 
				_odometer = Convert.ToInt32(reader["Odometer"]);

            if (reader["Deleted"] != DBNull.Value)
                Deleted = Convert.ToBoolean(reader["Deleted"]);

            if (reader["InsuranceCompany"] != DBNull.Value) 
				_insuranceCompany = Convert.ToString(reader["InsuranceCompany"]);

			if (reader["InsurancePolicyNumber"] != DBNull.Value) 
				_insurancePolicyNumber = Convert.ToString(reader["InsurancePolicyNumber"]);
            
			if (reader["InsuranceExpirationDate"] != DBNull.Value) 
				_insuranceExpirationDate = Convert.ToDateTime(reader["InsuranceExpirationDate"]);

			if (reader["PlateNumber"] != DBNull.Value) 
				_plateNumber = Convert.ToString(reader["PlateNumber"]);

            if (reader["Notes"] != DBNull.Value)
                _notes = Convert.ToString(reader["Notes"]);


			if (reader["PlateExpirationDate"] != DBNull.Value) 
				_plateExpirationDate = Convert.ToDateTime(reader["PlateExpirationDate"]);

			if (reader["Active"] != DBNull.Value)
				_active = Convert.ToBoolean(reader["Active"]);

			_createDate = Convert.ToDateTime(reader["CreateDate"]);

			if (reader["Name"] != DBNull.Value)
				_name = Convert.ToString(reader["Name"]);


            try
            {
                this.Companies = reader["Companies"].ToString().Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => Convert.ToInt32(o))
                    .Union(new[] { this.CompanyId }).ToArray();
            }
            catch { }

            if (__referenceNumberExists)
            {
                try
                {
                    ReferenceNumber = reader.GetValue<string>("ReferenceNumber");
                }
                catch
                {
                    __referenceNumberExists = false;
                }
            }

		}
        private static bool __referenceNumberExists = true;


        [ProtoContract]
        internal class InternalTruckList
        {
            [ProtoMember(1)]
            public Collection<Truck> Trucks { get; set; }

            public InternalTruckList()
            {

            }
        }

        public static void ClearCacheByCompany(Company.Company company)
        {
            AppServices.Cache.InvalidateCacheItem("truck:c" + company.Id);
        }

		public static List<Truck> GetByCompany(Company.Company company, bool? includeDeleted = false)
		{
            var trucks = includeDeleted == true
                ? GetByCompanyIdWithoutCache(company.Id, true).Trucks
				: AppServices.Cache.Get("truck:c" + company.Id, 
	                TimeSpan.FromMinutes(CacheTimeout), 
	                () => GetByCompanyIdWithoutCache(company.Id, false)).Trucks;
  
            return trucks != null ? trucks.ToList() : new List<Truck>();
		}
        
        public static async Task<List<Truck>> GetByCompanyAsync(Company.Company company, bool? includeDeleted = false)
        {
            var trucks = includeDeleted == true
                ? (await GetByCompanyIdWithoutCacheAsync(company.Id, true)).Trucks
                : (await AppServices.Cache.GetAsync("truck:c" + company.Id, 
                    TimeSpan.FromMinutes(CacheTimeout), 
                    async () => await GetByCompanyIdWithoutCacheAsync(company.Id, false))).Trucks;

            return trucks != null ? trucks.ToList() : new List<Truck>();
        }

        private static InternalTruckList GetByCompanyIdWithoutCache(int companyId, bool includeDeleted)
        {
            var tl = new InternalTruckList();
            tl.Trucks = new Collection<Truck>();
            //Method with GetByCompanyIdWithoutCacheAsync with SqlHelper.ExecuteReaderAsync already exist
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "TrucksGetByCompanyId",
                new SqlParameter("@CompanyId", companyId),
                new SqlParameter("@IncludeDeleted", includeDeleted)))
            {
                while (dr.Read())
                {
                    tl.Trucks.Add(new Truck(dr));
                }
            }
            // sorting here vs database is much faster due to our scale.
            tl.Trucks = tl.Trucks.OrderBy(l => l.Name).ToCollection();

            return tl;
        }
        
        private static async Task<InternalTruckList> GetByCompanyIdWithoutCacheAsync(int companyId, bool includeDeleted)
        {
            var tl = new InternalTruckList();
            tl.Trucks = new Collection<Truck>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                       "TrucksGetByCompanyId",
                       new SqlParameter("@CompanyId", companyId),
                       new SqlParameter("@IncludeDeleted", includeDeleted)))
            {
                while (await dr.ReadAsync())
                {
                    tl.Trucks.Add(new Truck(dr));
                }
            }
            // sorting here vs database is much faster due to our scale.
            tl.Trucks = tl.Trucks.OrderBy(l => l.Name).ToCollection();

            return tl;
        }

        public override string ToString()
        {
            return _name;
        }

        [Key]
        public int Id
        {
            get => _id; 
			set => _id = value;
		}

		public DateTime CreateDate => _createDate;

		[PartitionKey]
		public int CompanyId
		{
			get => _companyId;
			set => _companyId = value;
		}

		public string Name
		{
			get
			{
				return _name;
			}
			set
			{
				_name = value;
			}
		}

		public string Description
		{
			get
			{
				return _description;
			}
			set
			{
				_description = value;
			}
		}

		public string VIN
		{
			get
			{
				return _vin;
			}
			set
			{
				_vin = value;
			}
		}

		public int Year
		{
			get
			{
				return _year;
			}
			set
			{
				_year = value;
			}
		}
		public TruckType Type
		{
			get
			{
				return _type;
			}
			set
			{
				_type = value;
			}
		}

        public DutyType Duty
        {
            get
            {
                return _duty;
            }
            set
            {
                _duty = value;
            }
        }

		public string Manufacturer
		{
			get
			{
				return _manufacturer;
			}
			set
			{
				_manufacturer = value;
			}
		}

		public string Model
		{
			get
			{
				return _model;
			}
			set
			{
				_model = value;
			}
		}

		public int Odometer
		{
			get
			{
				return _odometer;
			}
			set
			{
				_odometer = value;
			}
		}

		public string InsuranceCompany
		{
			get
			{
				return _insuranceCompany;
			}
			set
			{
				_insuranceCompany = value;
			}
		}
		public string InsurancePolicyNumber
		{
			get
			{
				return _insurancePolicyNumber;
			}
			set
			{
				_insurancePolicyNumber = value;
			}
		
		}
		public Nullable<DateTime> InsuranceExpirationDate
		{
			get
			{
				return _insuranceExpirationDate;
			}
			set
			{
				_insuranceExpirationDate = value;
			}
		}

		public string PlateNumber
		{
			get
			{
				return _plateNumber;
			}
			set
			{
				_plateNumber = value;
			}
		}

		public Nullable<DateTime>	PlateExpirationDate
		{
            get => _plateExpirationDate;
            set => _plateExpirationDate = value;
        }

        public string Notes
        {
            get => _notes;
            set => _notes = value;
        }

        public bool IsActive
        {
            get => _active;
            set => _active = value;
        }

        public bool Deleted { private set; get; }

        public async Async.Task Delete()
        {
            try
            {
				Deleted = true;
                SqlMapper.ExecuteSP("TrucksDeleteById", new { @TruckId = _id });
            }
            finally
            {
                AppServices.Cache.InvalidateCacheItem("truck:" + Id);
				UpdateCache();
                await PushNotificationProvider.Push(_companyId, "trucks_update", new { truckId = _id, type = "delete" });
                await Caching.CacheWorkerUtility.DeleteTruck(this);
            }
        }

		public async Async.Task Undelete()
		{
			try
			{
				Deleted = false;
				SqlMapper.ExecuteSP("TrucksUndeleteById", new { @TruckId = _id });
			}
			finally
			{
				AppServices.Cache.InvalidateCacheItem("truck:" + Id);
				await PushNotificationProvider.Push(_companyId, "trucks_update", new { truckId = _id, type = "update" });

				UpdateCache();
				await Caching.CacheWorkerUtility.UpdateTruck(this);
			}
		}

		/// <summary>
		/// Saves the Truck object to the data store.
		/// </summary>
		public async Async.Task Save()
		{
            try
            {
                if (_id < 1)
                {
                    DbInsert();
                    await PushNotificationProvider.Push(_companyId, "trucks_update", new { truckId = _id, type = "add" });
                }
                else
                {
                    DbUpdate();
                    AppServices.Cache.InvalidateCacheItem("truck:" + Id);
                    await PushNotificationProvider.Push(_companyId, "trucks_update", new { truckId = _id, type = "update" });
                }
            }
            finally
            {
				var tempTruck = GetByIdWithoutCache(Id);

				if (tempTruck != null)
				{
					_createDate = tempTruck.CreateDate;
					Companies = tempTruck.Companies;
				}
				UpdateCache();
				await Caching.CacheWorkerUtility.UpdateTruck(this);
            }
		}

		private void UpdateCache() {
			AppServices.Cache.InvalidateCacheItem("truck:c" + _companyId);
			Cache.Instance.PartitionSet(this);
			Cache.Instance.Set(this);
		}

		public void InvalidateCache() {
			Cache.Instance.PartitionDelete(this);
			Cache.Instance.Delete(this);
            AppServices.Cache.InvalidateCacheItem("truck:" + Id);
			AppServices.Cache.InvalidateCacheItem("truck:c" + _companyId);
		}

		private void DbInsert()
		{
			_id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
				"TrucksInsert",
				new SqlParameter("@CompanyId", _companyId),
				new SqlParameter("@Name", _name),
				new SqlParameter("@Description", _description),
				new SqlParameter("@VIN", _vin),
				new SqlParameter("@TruckYear", _year),
				new SqlParameter("@TruckType", _type),
                new SqlParameter("@Duty", _duty),
				new SqlParameter("@TruckManufacturer", _manufacturer),
				new SqlParameter("@TruckModel", _model),
				new SqlParameter("@Odometer", _odometer),
				new SqlParameter("@InsuranceCompany", _insuranceCompany),
				new SqlParameter("@InsurancePolicyNumber", _insurancePolicyNumber),
				new SqlParameter("@InsuranceExpirationDate", _insuranceExpirationDate),
				new SqlParameter("@PlateNumber", _plateNumber),
				new SqlParameter("@PlateExpirationDate", _plateExpirationDate),
				new SqlParameter("@Active", _active),
                new SqlParameter("@Notes", _notes)));
		}

		private void DbUpdate()
		{
			SqlHelper.ExecuteNonQuery(Core.ConnectionString,
				"TrucksUpdateById",
				new SqlParameter("@TruckId", _id),
				new SqlParameter("@CompanyId", _companyId),
				new SqlParameter("@Name", _name),
				new SqlParameter("@Description", _description),
				new SqlParameter("@VIN", _vin),
				new SqlParameter("@TruckYear", _year),
				new SqlParameter("@TruckType", _type),
                new SqlParameter("@Duty", _duty),
				new SqlParameter("@TruckManufacturer", _manufacturer),
				new SqlParameter("@TruckModel", _model),
				new SqlParameter("@Odometer", _odometer),
				new SqlParameter("@InsuranceCompany", _insuranceCompany),
				new SqlParameter("@InsurancePolicyNumber", _insurancePolicyNumber),
				new SqlParameter("@InsuranceExpirationDate", _insuranceExpirationDate),
				new SqlParameter("@PlateNumber", _plateNumber),
				new SqlParameter("@PlateExpirationDate", _plateExpirationDate),
				new SqlParameter("@Active", _active),
                new SqlParameter("@Notes", _notes));
		}

		#region Comparable
		// when this object is compared, we only want to compare based on the Id.. nothing else!
		public override int GetHashCode()
		{
			return _id.GetHashCode();
		}

		public override bool Equals(System.Object obj)
		{
			if (obj == null)
				return false;

			Truck hp = obj as Truck;
			if ((System.Object)hp == null)
				return false;

			return (hp.Id == this.Id);
		}

		public bool Equals(Truck l)
		{
			if (l == null)
				return false;

			return (l.Id == this.Id);
		}

		public static bool operator ==(Truck a, Truck b)
		{
			if (System.Object.ReferenceEquals(a, b))
				return true;

			if (((object)a == null) || ((object)b == null))
				return false;

			return (a.Id == b.Id);
		}

		public static bool operator !=(Truck a, Truck b)
		{
			return !(a == b);
		}
		#endregion

        public static async Task<Truck> GetByIdAsync(User requestee, int id)
        {
            var x = await GetByIdAsync(id);
            if (requestee == null) throw new Exception("invalid user");

            if (!await requestee.HasAccessToCompanyAsync(x.CompanyId))
            {
                throw new TowbookException("Access Denied");
            }

            return x;
        }
    }
}
