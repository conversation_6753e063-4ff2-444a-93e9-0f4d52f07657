using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Text;
using System.Data.SqlClient;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System.Linq;
using System.Text.RegularExpressions;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Async = System.Threading.Tasks;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Extric.Towbook
{

    public class FakeRateItem : IRateItem
    {
        public int RateItemId { get; set; }

        public decimal Cost { get; set; }

        public decimal BaseCost { get; set; }

        public string Name { get; set; }

        public DateTime CreateDate { get; set; }

        public bool Taxable { get; set; }
        public bool LockQuantity { get; set; }
        public bool LockCost { get; set; }

        public Dictionary<int, IExtendedRateItem> ExtendedRateItems { get; set; }

        public PredefinedRateItem Predefined { get; set; }

        public int? CategoryId { get; set; }

        public int? MinimumQuantity { get; set; }

        public int? MaximumQuantity { get; set; }

        public int? DefaultQuantity { get; set; }

        public decimal FreeQuantity { get; set; }

        public bool DiscountExempt { get; set; }

        public int ParentRateItemId { get; set; }

        public int[] Companies { get; set; }

        public int DefaultClassId { get; set; }

        public RateItem.TimeRoundEnum? TimeRound { get; set; }

        public bool Deleted { get; set; }
        public int? LedgerAccountId { get; set; }
    }

    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "RateItem")]
    [CacheKey("_rateItem")]
    public class RateItem : IRateItem
    {
        private const int CacheTimeout = 120;

		public const int BUILTIN_FUEL_SURCHARGE = 1;
        public const int BUILTIN_DISCOUNT = 2;

        public enum RateTypeEnum
        {
            FixedRate = 1,
            FixedCalculatedRate = 2
        }

        public enum InclusionTypeEnum
        {
            // Always include
            Default = 0,
            // Used with the limit to account pricing option to force include for all accounts (by pass filter)
            Corporate = 1
        }


        public enum MeasurementEnum
        {
            Hours = 0, 
            Minutes = 1, 
            Miles = 2, 
            Pounds = 3,
            Units = 4
        }

        public enum TimeRoundEnum
        {
            Actual = 1,
            NearestQuarterHour = 2,
            NearestHalfHour = 3,
            NearestHour = 4
        }

        private int _id = -1;
        private int _companyId;
        private RateTypeEnum _rateTypeId = RateTypeEnum.FixedRate;
        private MeasurementEnum _measurementId;
        private InclusionTypeEnum _inclusionTypeId = InclusionTypeEnum.Default;


        private Nullable<int> _minimumQuantity;
        private Nullable<int> _maximumQuantity;
		private Nullable<int> _defaultQuantity;
        private int _defaultClassId; 
		private decimal _freeQuantity;
        private string _name;
        private string _description;
        private decimal _cost;
		private decimal _baseCost;
		private bool _lockCost;
		private bool _lockQuantity;

        private bool _taxable;
        private bool _payCommission;

        private bool _deleted;
        private DateTime _createDate;
		private PredefinedRateItem _predefined;
        private bool _discountExempt;

        [JsonProperty]
        public int[] Companies { get; private set; }

		public PredefinedRateItem Predefined
		{
			get { return _predefined; }
			set { _predefined = value; }
		}

        public int? CategoryId { get; set; }

        public TimeRoundEnum? TimeRound { get; set; }
        public int? TimeStartAtStatusId { get; set; }
        public int? TimeStopAtStatusId { get; set; }

        /// <summary>
        /// Set to True to prevent the RateItem from being impacted by an Account Discount on ANY account. once set to false here,
        /// it cannot be overriden at the account level.
        /// </summary>
        public bool DiscountExempt
        {
            get
            {
                return _discountExempt;
            }
            set
            {
                _discountExempt = value;
            }
        }

        public int? LedgerAccountId { get; set; }
        public RateItem()
        {
            _id = -1;
        }

        public RateItem(int id)
        {
            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "RateItemsGetById", new SqlParameter("@RateItemId", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    _id = 0;
                    throw new ApplicationException("RateItem doesn't exist!" + id);
                }
            }
        }

        public static RateItem GetById(int id)
        {
            if (id == 0)
                return null;

            if (id == -1)
                return new RateItem() { Name = "Default Rate" };

            return AppServices.Cache.Get("ri:" + id, DateTime.Now.AddHours(CacheTimeout),
                () => {

                    var ret = Cache.Instance.Get(id, (int rateItemId) => GetByIdWithoutCache(rateItemId));

                    if (ret != null && ret.Companies == null)
                    {
                        var td = GetByIdWithoutCache(id);
                        if (td.Companies != null)
                        {
                            Cache.Instance.PartitionSet(td);
                            Cache.Instance.Set(td);
                        }

                        return td;
                    }

                    return ret;

                });
        }

        public static RateItem GetByIdWithoutCache(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "RateItemsGetById", new SqlParameter("@RateItemId", id)))
            {
                if (dr.Read())
                {
                    return new RateItem(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<RateItem> GetByIdAsync(int id)
        {
            if (id == 0)
                return null;

            if (id == -1)
                return new RateItem() { Name = "Default Rate" };

            return await AppServices.Cache.GetAsync("ri:" + id, DateTime.Now.AddHours(CacheTimeout),
                async() => {

                    var ret = await Cache.Instance.GetAsync(id, async(int rateItemId) => await GetByIdWithoutCacheAsync(rateItemId));

                    if (ret != null && ret.Companies == null)
                    {
                        var td = await GetByIdWithoutCacheAsync(id);
                        if (td.Companies != null)
                        {
                            await Cache.Instance.PartitionSetAsync(td);
                            await Cache.Instance.SetAsync(td);
                        }

                        return td;
                    }

                    return ret;

                });
        }

        public static async Task<RateItem> GetByIdWithoutCacheAsync(int id)
        {
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    System.Data.CommandType.StoredProcedure,
                    "RateItemsGetById", new SqlParameter("@RateItemId", id)))
            {
                if (await dr.ReadAsync())
                {
                    return new RateItem(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public RateItem(Company.Company company, PredefinedRateItem rate)
		{
			if (company == null)
				throw new Towbook.TowbookException("company is null!");

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "RateItemsGetByPredefinedId", new SqlParameter("@RateItemPredefinedId", rate.Id),
                new SqlParameter("@CompanyId", company.Id)))
            {
                if (dr.HasRows)
                {
                    dr.Read();
                    InitializeFromDataReader(dr);
                }
                else
                {
                    _id = -1;
                    _predefined = rate;
                    _companyId = company.Id;
                }
            }
		}


        public RateItem(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
            AppServices.Cache.Add("ri:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        /// <summary>
        /// Returns a list of all active user accounts (doesn't return deleted ones)
        /// </summary>
		public static List<IRateItem> GetByCompanyId(int companyId)
		{
			return GetByCompanyId(companyId, false);
		}

        [ProtoContract]
        internal class InternalIRateItemList
        {
            [ProtoMember(1)]
            public List<RateItem> RateItems { get; set; }

            public InternalIRateItemList()
            {
                RateItems = new List<RateItem>();
            }
        }

        public static List<IRateItem> GetByCompanyId(int companyId, bool includeHiddenStorage, bool? includeDeleted = false)
        {
            var x = (includeDeleted == true
                ? GetByCompanyIdFromDb(companyId, true)
                : AppServices.Cache.Get("ri_c:" + companyId, DateTime.Now.AddHours(CacheTimeout),
                    () => GetByCompanyIdFromDb(companyId, includeDeleted))).RateItems ?? new List<RateItem>();
                
            var tempCopy = new List<IRateItem>();
            tempCopy.AddRange(x);

            return tempCopy.Where(o => o.Predefined != null &&
                (o.Predefined.Hidden && includeHiddenStorage || o.Predefined.Hidden == false) || o.Predefined == null)
                .OrderBy(o => o.Name)
                .ToList();
        }

        private static InternalIRateItemList GetByCompanyIdFromDb(int companyId, bool? includeDeleted = false)
        {
            var rateItems = new InternalIRateItemList();

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "RateItemsGetByCompanyId", new SqlParameter("@CompanyId", companyId), new SqlParameter("@includeDeleted", includeDeleted)))
            {
                while (dr.Read())
                {
                    rateItems.RateItems.Add(new RateItem(dr));
                }

                return rateItems;
            }

        }

        public static List<IRateItem> GetByParentId(int id)
        {
            var x = AppServices.Cache.Get("ri_pr:" + id, DateTime.Now.AddHours(CacheTimeout), () =>
            {
                try
                {
                    var raw_rates = Core.GetRedisValueAsByteArray("ri_pr:" + id);
                
                    var t1 = Cache.FromByteArray<List<RateItem>>(raw_rates);
                    if (t1 != null)
                        return t1.ToList();
                }
                catch (ProtoException)
                {
                    // silently ignore proto buf error so we re-serialize it.
                }

                var list = GetByParentIdWithoutCache(id);

                Core.SetRedisValue("ri_pr:" + id, Cache.ToByteArray(list), TimeSpan.FromHours(24));

                return list;
            });

            return x.Select(o => (IRateItem)o).ToList();
        }

        /// <summary>
        /// Retrieve all RateItem's where ParentRateItemId == <paramref name="id"/>
        /// </summary>
        /// <param name="id"></param>
        /// <returns>List containing children. Returns an empty list of none exist.</returns>
        public static List<RateItem> GetByParentIdWithoutCache(int id)
        {
            if (id == 0)
                return new List<RateItem>();

            var rateItems = new InternalIRateItemList();

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "RateItemsGetByParentRateItemId", id))
            {
                while (dr.Read())
                {
                    rateItems.RateItems.Add(new RateItem(dr));
                }

                return rateItems.RateItems;
            }
        }


		public static List<IRateItem> GetPredefinedByCompany(Company.Company company)
		{
			return GetPredefinedByCompany(company, true, true);
		}

		/// <summary>
		/// Returns a list of RateItem's associated with PredefinedRateItem's for the specified Company.
		/// </summary>
        public static List<IRateItem> GetPredefinedByCompany(Company.Company company, bool showLocked, bool showUnlocked)
        {
            List<IRateItem> rateItems = new List<IRateItem>();

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "RateItemsGetPredefinedByCompanyId", company.Id,
                showLocked, showUnlocked))
            {
                while (dr.Read())
                {
                    rateItems.Add(new RateItem(dr));
                }
            }

            return rateItems;
        }

		/// <summary>
		/// Returns a list of RateItem's associated with PredefinedRateItem's for the specified Company.
		/// </summary>
        public static List<IRateItem> GetPredefinedByCompany(Company.Company company, Accounts.Account account)
        {
            Dictionary<int, IRateItem> xl = new Dictionary<int, IRateItem>();

            foreach (IRateItem xy in Accounts.RateItem.GetPredefinedByAccount(account))
            {
                xl.Add(xy.Predefined.Id, xy);
            }

            if (xl == null || xl.Count == 0)
                return GetPredefinedByCompany(company);
            else
            {
                foreach (IRateItem ix in GetPredefinedByCompany(company))
                {
                    if (!xl.ContainsKey(ix.Predefined.Id))
                        xl.Add(ix.Predefined.Id, ix);
                }
            }

            // that method only returned ones for the account.. get the rest from the company
            return new List<IRateItem>(xl.Values);
        }


        /// <summary>
        /// Returns a list of all active rate items (doesn't return deleted ones) for the specified company
		/// and account.
        /// </summary>
        public static List<IRateItem> GetByCompanyId(int companyId, Accounts.Account account)
        {
            var rateItems = new List<IRateItem>();

            if (account != null)
            {
                rateItems.AddRange(Accounts.RateItem.GetByAccount(account));
                rateItems.AddRange(RateItem.GetByNotInAccount(account));
            }
            else
            {
                return GetByCompanyId(companyId);
            }

            return rateItems.OrderBy(o => o.Name).ToList();
        }

		public static Collection<IRateItem> GetByNotInAccount(Accounts.Account account)
		{
			Collection<IRateItem> rateItems = new Collection<IRateItem>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "RateItemsGetByCompanyIdNotInAccountId", account.CompanyId,
                account.Id))
            {
                while (dr.Read())
                {
                    RateItem r = new RateItem(dr);
                    string ex = "c=" + r.Cost.ToString();
                    r.Cost = Accounts.RateItem.GetTransforemdCost(account, r);

                    if (account.DiscountRate > 0)
                    {
                        int changedId = 0;
                        foreach (ExtendedRateItem x in r.ExtendedRateItems.Values)
                        {
                            x.Amount = x.Amount - (x.Amount * account.DiscountRate);
                            changedId = x.BodyTypeId;
                        }
                    }

                    rateItems.Add(r);
                }

                return rateItems;
            }    
		}


        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = Convert.ToInt32(reader["RateItemId"]);
            _companyId = Convert.ToInt32(reader["CompanyId"]);

            if (reader["RateTypeId"] != DBNull.Value)
                _rateTypeId = (RateTypeEnum)Convert.ToInt32(reader["RateTypeId"]);

            if (reader["MeasurementId"] != DBNull.Value)
                _measurementId = (MeasurementEnum)Convert.ToInt32(reader["MeasurementId"]);

            if (reader["InclusionTypeId"] != DBNull.Value)
                _inclusionTypeId = (InclusionTypeEnum)Convert.ToInt32(reader["InclusionTypeId"]);

            if (reader["RateItemPredefinedId"] != DBNull.Value)
                _predefined = PredefinedRateItem.GetById(Convert.ToInt32(reader["RateItemPredefinedId"]));

            if (reader["Name"] != DBNull.Value)
                _name = Convert.ToString(reader["Name"]);

            if (reader["Description"] != DBNull.Value)
                _description = Convert.ToString(reader["Description"]);

            _baseCost = _cost = reader.GetValue<decimal>("Cost");


            if (reader["LockCost"] != DBNull.Value)
                _lockCost = Convert.ToBoolean(reader["LockCost"]);

            if (reader["LockQuantity"] != DBNull.Value)
                _lockQuantity = Convert.ToBoolean(reader["LockQuantity"]);

            if (reader["MinimumQuantity"] != DBNull.Value)
                _minimumQuantity = Convert.ToInt32(reader["MinimumQuantity"]);

            if (reader["MaximumQuantity"] != DBNull.Value)
                _maximumQuantity = Convert.ToInt32(reader["MaximumQuantity"]);

            if (reader["DefaultQuantity"] != DBNull.Value)
                _defaultQuantity = Convert.ToInt32(reader["DefaultQuantity"]);

            if (reader["Deleted"] != DBNull.Value)
                _deleted = Convert.ToBoolean(reader["Deleted"]);

            if (reader["Taxable"] != DBNull.Value)
                _taxable = Convert.ToBoolean(reader["Taxable"]);

            if (reader["PayCommission"] != DBNull.Value)
                _payCommission = Convert.ToBoolean(reader["PayCommission"]);

            _freeQuantity = Convert.ToInt32(reader["FreeQuantity"]);
            _createDate = Convert.ToDateTime(reader["CreateDate"]);

            if (reader["RateItemCategoryId"] != DBNull.Value)
                CategoryId = reader.GetValue<int>("RateItemCategoryId");

            ParentRateItemId = reader.GetValue<int>("ParentRateItemId");

            DiscountExempt = reader.GetValue<bool>("DiscountExempt");

            if (reader["ClassId"] != DBNull.Value)
                _defaultClassId = Convert.ToInt32(reader["ClassId"]);


            TimeStartAtStatusId = reader.GetValueOrNull<int>("TimeStartAtStatusId", true);
            TimeStopAtStatusId = reader.GetValueOrNull<int>("TimeStopAtStatusId", true);
            TimeRound = (TimeRoundEnum?)reader.GetValueOrNull<int>("TimeRound", true);
            
            LedgerAccountId = reader.GetValueOrNull<int>("LedgerAccountId");

            try
            {
                this.Companies = (reader.GetValue<string>("Companies") ?? "").Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => Convert.ToInt32(o))
                    .Union(new[] { this.CompanyId }).ToArray();
            }
            catch { }
        }

        /// <summary>
        /// Saves the User object to the data store.
        /// </summary>
        public async Task Save(Action callback = null)
        {
            if (_id == 0)
            {
                throw new Extric.Towbook.TowbookException("No such RateItem. Can't save " +
                    "object! (this object should have already been discarded!)");
            }
            if(_predefined == null && string.IsNullOrWhiteSpace(_name))
            {
                throw new Extric.Towbook.TowbookException("Name is null or empty. Can't save " +
                    "a rate item without a name!");
            }

            if (_predefined?.Id == PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE)
            {
                // force after hour release fee rate items to be category 2 (storage)
                CategoryId = 2;
            }

            // Names with whitespace at start or end causes issue with tax not being applied in editor
            if(char.IsWhiteSpace(Name, 0) || char.IsWhiteSpace(Name, Name.Length - 1) || Name.Contains("  "))
            {
                Name = Regex.Replace(Name, @"\s+", " ").Trim();
            }

            try
            {
                if (_id == -1)
                {
                    DbInsert();
                    await PushNotificationProvider.Push(_companyId, "rateItems_update", new { rateItemId = _id, type = "add" });
                }
                else
                {
                    DbUpdate();
                    await PushNotificationProvider.Push(_companyId, "rateItems_update", new { rateItemId = _id, type = "update" });
                }
            }
            finally
            {
                if (callback != null)
                    callback();

                var temp = await GetByIdWithoutCacheAsync(Id);

                if (temp != null)
                {
                    this.CreateDate = temp.CreateDate;
                    this.Companies = temp.Companies;
                }

                await InvalidateCacheAsync(_id, _companyId);
                await UpdateRedisAsync();

                if (callback != null)
                {
                    // this needs to be called after the extended rates are saved.
                    await Caching.CacheWorkerUtility.UpdateRateItem(this);
                }
            }
        }

        public static void InvalidateCache(int rateItemId)
        {
            var r = RateItem.GetById(rateItemId);
            if (r != null)
                InvalidateCache(rateItemId, r.CompanyId);
        }

        protected async Task UpdateRedisAsync()
        {
            await Cache.Instance.PartitionSetAsync(this);
            await Cache.Instance.SetAsync(this);
        }

        public static void InvalidateCache(int rateItemId, int companyId)
        {
            // by parentRate
            AppServices.Cache.InvalidateCacheItem("ri_pr:" + rateItemId);

            Core.DeleteRedisKey("ri_pr:" + rateItemId);

            // by company
            AppServices.Cache.InvalidateCacheItem("ri_c:" + companyId);

            // individual
            AppServices.Cache.InvalidateCacheItem("ri:" + rateItemId);


            // extended items
            AppServices.Cache.InvalidateCacheItem("ri_ext:" + rateItemId);

        }
        public static async Task InvalidateCacheAsync(int rateItemId, int companyId)
        {
            // by parentRate
            AppServices.Cache.InvalidateCacheItem("ri_pr:" + rateItemId);

            await Core.DeleteRedisKeyAsync("ri_pr:" + rateItemId);

            // by company
            AppServices.Cache.InvalidateCacheItem("ri_c:" + companyId);

            // individual
            AppServices.Cache.InvalidateCacheItem("ri:" + rateItemId);


            // extended items
            AppServices.Cache.InvalidateCacheItem("ri_ext:" + rateItemId);

        }

        public async Async.Task Delete(User deleter)
        {
            try
            {
                SqlMapper.ExecuteSP("RateItemsDeleteById", new { @RateItemId = _id });
                _deleted = true;
            }
            finally
            {
                if (this.ParentRateItemId > 0)
                {
                    await InvalidateCacheAsync(this.ParentRateItemId, _companyId);
                }
                await InvalidateCacheAsync(_id, _companyId);
                await UpdateRedisAsync();
                await PushNotificationProvider.Push(_companyId, "rateItems_update", new { rateItemId = _id, type = "delete" });
                await Caching.CacheWorkerUtility.DeleteRateItem(this);

                var relatedRules = Accounts.AccountRateItemRule.GetByCompanyId(_companyId).Where((rule) => rule.RateItemId == _id);
                foreach (var rule in relatedRules)
                {
                    await rule.Delete();
                }
            }
        }

        public async Task Undelete(User deleter)
        {
            try
            {
                SqlMapper.ExecuteSP("RateItemsUndeleteById", new { @RateItemId = _id });
                _deleted = false;
            }
            finally
            {
                await InvalidateCacheAsync(_id, _companyId);
                await UpdateRedisAsync();
                await PushNotificationProvider.Push(_companyId, "rateItems_update", new { rateItemId = _id, type = "update" });
                await Caching.CacheWorkerUtility.UpdateRateItem(this);
            }
        }

        private void DbInsert()
        {
            _id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "RateItemsInsert",
                new SqlParameter("@CompanyId", _companyId),
				new SqlParameter("@RateTypeId", _rateTypeId),
				new SqlParameter("@MeasurementId", _measurementId),
				new SqlParameter("@Name", _name),
				new SqlParameter("@Description", _description),
				new SqlParameter("@Cost", _cost),
				new SqlParameter("@MinimumQuantity", _minimumQuantity),
				new SqlParameter("@MaximumQuantity", _maximumQuantity),
				new SqlParameter("@DefaultQuantity", _defaultQuantity),
				new SqlParameter("@FreeQuantity", _freeQuantity),
				new SqlParameter("@LockCost", _lockCost),
				new SqlParameter("@LockQuantity", _lockQuantity),
				new SqlParameter("@RateItemPredefinedId", (_predefined != null ? (int?)_predefined.Id : null)),
				new SqlParameter("@Taxable", _taxable),
				new SqlParameter("@PayCommission", _payCommission),
                new SqlParameter("@RateItemCategoryId", CategoryId),
                new SqlParameter("@ParentRateItemId", (ParentRateItemId > 0 ? (int?) ParentRateItemId : null)),
                new SqlParameter("@ClassId", _defaultClassId),
                new SqlParameter("@TimeStartAtStatusId", TimeStartAtStatusId),
                new SqlParameter("@TimeStopAtStatusId", TimeStopAtStatusId),
                new SqlParameter("@TimeRound", (int?)TimeRound),
                new SqlParameter("@InclusionTypeId", (int)InclusionType),
                new SqlParameter("@LedgerAccountId", LedgerAccountId)
                ));
        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "RateItemsUpdateById",
                new SqlParameter("@RateItemId", _id),
				new SqlParameter("@CompanyId", _companyId),
				new SqlParameter("@RateTypeId", _rateTypeId),
				new SqlParameter("@MeasurementId", _measurementId),
				new SqlParameter("@Name", _name),
				new SqlParameter("@Description", _description),
				new SqlParameter("@Cost", _cost),
				new SqlParameter("@MinimumQuantity", _minimumQuantity),
				new SqlParameter("@MaximumQuantity", _maximumQuantity),
				new SqlParameter("@DefaultQuantity", _defaultQuantity),
				new SqlParameter("@FreeQuantity", _freeQuantity),
				new SqlParameter("@LockCost", _lockCost),
				new SqlParameter("@LockQuantity", _lockQuantity),
				new SqlParameter("@RateItemPredefinedId", (_predefined != null ? (int?)_predefined.Id : null)),
				new SqlParameter("@Taxable", _taxable),
				new SqlParameter("@PayCommission", _payCommission),
                new SqlParameter("@RateItemCategoryId", CategoryId),
                new SqlParameter("@ParentRateItemId", (ParentRateItemId > 0 ? (int?)ParentRateItemId : null)),
                new SqlParameter("@ClassId", _defaultClassId),
                new SqlParameter("@TimeStartAtStatusId", TimeStartAtStatusId),
                new SqlParameter("@TimeStopAtStatusId", TimeStopAtStatusId),
                new SqlParameter("@TimeRound", (int?)TimeRound),
                new SqlParameter("@InclusionTypeId", (int)InclusionType),
                new SqlParameter("@LedgerAccountId", LedgerAccountId)
                );
		}

		#region Public Properties
        [Key]
		public int Id
        {
            get { return _id; }
        }
		public int RateItemId
		{
			get { return _id; }
		}

        public string Name
        {
            get
            {
                if (_predefined != null && string.IsNullOrWhiteSpace(_name))
                {
                    // only return the predefined name if the _name is null/empty.
                    return _predefined.Name;
                }
                else
                {
                    return _name;
                }
            }
            set
            {
                if (_predefined != null)
                    throw new ArgumentException("Can't set name; this rate item is associated with a predefined type.");

                _name = value;
            }
        }

        public string Description
        {
            get
			{
				if (_predefined != null)
				{
					return _predefined.Description;
				}
				else
				{
					return _description;
				}
			}
            set
			{
				if (_predefined != null)
					throw new ArgumentException("Can't set Description; this rate item is associated with a predefined type.");

				_description = value;
			}
        }

        public decimal Cost
        {
            get { return _cost; }
            set { _cost = value; }
        }

		public decimal BaseCost
		{
			get { return _baseCost; }
		}

		public bool LockCost
		{
			get
			{
                if (Predefined != null)
                {
                    if (Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE)
                        return false;
                }

				return _lockCost; 
			}
			set { _lockCost = value; }
		}

		public bool LockQuantity
		{
			get { return _lockQuantity; }
			set { _lockQuantity = value; }
		}

        [PartitionKey]
        public int CompanyId
        {
            get { return _companyId; }
            set { _companyId = value; }
        }

        public RateTypeEnum RateType
        {
            get { return _rateTypeId; }
            set { _rateTypeId = value; }
        }

        public MeasurementEnum Measurement
        {
            get { return _measurementId; }
            set { _measurementId = value; }
        }

        public InclusionTypeEnum InclusionType
        {
            get { return _inclusionTypeId; }
            set { _inclusionTypeId = value; }
        }

        public int DefaultClassId
        {
            get { return _defaultClassId; }
            set { _defaultClassId = value; }
        }


        public bool Taxable
        {
            get { return _taxable; }
            set { _taxable = value; }
        }

        public bool PayCommission
        {
            get { return _payCommission; }
            set { _payCommission = value; }
        }

        public bool Deleted
        {
            get { return _deleted; }
            set { _deleted = value; }
        }

        public DateTime CreateDate
        {
            get { return _createDate; }
            private set { _createDate = value; }
        }

        public Nullable<int> MinimumQuantity
        {
            get { return _minimumQuantity; }
            set { _minimumQuantity = value; }
        }

        public Nullable<int> MaximumQuantity
        {
            get { return _maximumQuantity; }
            set { _maximumQuantity = value; }
        }

		public Nullable<int> DefaultQuantity
		{
			get { return _defaultQuantity; }
			set { _defaultQuantity = value; }
		}

		public decimal FreeQuantity
		{
			get { return _freeQuantity; }
			set { _freeQuantity = value; }
		}

		#endregion

        [NonSerialized]
        private Dictionary<int, IExtendedRateItem> _extendedRateItems;
        
		public Dictionary<int, IExtendedRateItem> ExtendedRateItems
		{
            get
            {
                if (_extendedRateItems == null)
                {
                    _extendedRateItems = ExtendedRateItem.GetByRateItem(this);
                }

                return _extendedRateItems;
            }
		}

        public int ParentRateItemId { get; set; }
        public string SurchargeLabel { get; set; }
        public string OutputExtendedPrices { get; set; }

        private static int? GetTimeBasedMinutes(Dispatch.Entry e, int? startAtId, int? stopAtId, DateTime? startDate = null, DateTime? endDate = null)
        {
            DateTime? startAt = startDate;
            DateTime? stopAt = endDate;

            if (e != null)
            {
                if (startAtId == 0)
                    startAt = e.CreateDate;
                if (startAtId == 1)
                    startAt = e.DispatchTime;
                if (startAtId == 2)
                    startAt = e.EnrouteTime;
                if (startAtId == 3)
                    startAt = e.ArrivalTime;
                if (startAtId == 4)
                    startAt = e.TowTime;
                if (startAtId == 5)
                    startAt = e.CompletionTime;
                if (startAtId == 7)
                    startAt = e.DestinationArrivalTime;

                if (stopAtId == 0)
                    stopAt = e.CreateDate;
                if (stopAtId == 1)
                    stopAt = e.DispatchTime;
                if (stopAtId == 2)
                    stopAt = e.EnrouteTime;
                if (stopAtId == 3)
                    stopAt = e.ArrivalTime;
                if (stopAtId == 4)
                    stopAt = e.TowTime;
                if (stopAtId == 5)
                    stopAt = e.CompletionTime;
                if (stopAtId == 7)
                    stopAt = e.DestinationArrivalTime;
            }

            if (startAt != null && stopAt != null)
            {
                var ts = (TimeSpan)(stopAt - startAt);

                if ((int)Math.Ceiling(ts.TotalMinutes) < 0)
                    return null;
                else
                    return (int)Math.Ceiling(ts.TotalMinutes);
            }

            return (int?)null;
        }
        
        public static decimal GetTimedBasedTransformedCost(RateItem rate, Dispatch.Entry entry, DateTime? startDate = null, DateTime? endDate = null)
        {
            if (rate == null)
                return 0.0M;

            if (rate.TimeStartAtStatusId == null && rate.TimeStopAtStatusId == null && startDate == null && endDate == null)
                return 0.0M;

            decimal? qty = null;

            var mins = GetTimeBasedMinutes(entry, rate.TimeStartAtStatusId, rate.TimeStopAtStatusId, startDate, endDate);
            if (mins != null)
            {
                var units = 0.0M;

                switch (rate.TimeRound.Value)
                {
                    case TimeRoundEnum.NearestQuarterHour:
                        units = Math.Ceiling((mins.Value - rate.FreeQuantity) / 60 * 4);
                        qty = units * (decimal)0.25;
                        break;

                    case TimeRoundEnum.NearestHalfHour:
                        units = Math.Ceiling((mins.Value - rate.FreeQuantity) / 60 * 2);
                        qty = units * (decimal)0.5;
                        break;

                    case TimeRoundEnum.NearestHour:
                        units = Math.Ceiling((mins.Value - rate.FreeQuantity) / 60 * 1);
                        qty = units;
                        break;

                    case TimeRoundEnum.Actual:
                        units = Math.Ceiling(mins.Value - rate.FreeQuantity);
                        qty = units / 60;
                        break;
                }
            }

            if (qty.HasValue && qty.Value > 0)
            {
                if(rate.Measurement == MeasurementEnum.Minutes)
                    return Decimal.Round(qty.Value / 60, 4, MidpointRounding.AwayFromZero);

                return Decimal.Round(qty.Value, 4, MidpointRounding.AwayFromZero);
            }

            return 0.0M;
        }

        public static async Task<decimal?> GetAccountFreeQuantityAsync(IRateItem rateItem, int accountId)
        {
            if (rateItem == null)
                return null;

            var accountRateItem = await Accounts.RateItem.GetByRateItemAsync(accountId, rateItem.RateItemId);
            if (accountRateItem != null)
                return (decimal?)accountRateItem.FreeQuantity;

            return rateItem.FreeQuantity;
        }
    }


    [ProtoContract]
    internal class InternalExtendedRateItemCollection
    {
        [ProtoMember(1)]
        public Collection<ExtendedRateItem> ExtendedRateItems { get; set; }

        public InternalExtendedRateItemCollection() { }
        public InternalExtendedRateItemCollection(Collection<ExtendedRateItem> rates)
        {
            ExtendedRateItems = rates;
        }
    }


    [ProtoContract]
	public class ExtendedRateItem : Extric.Towbook.IExtendedRateItem
	{
		public ExtendedRateItem()
		{
            Id = -1;
		}

		public ExtendedRateItem(int id)
		{
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "RateItemsExtendedGetById", new SqlParameter("@RateItemId", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    Id = 0;
                    throw new ApplicationException("ExtendedRateItem doesn't exist!" + id);
                }
            }
		}

		public ExtendedRateItem(SqlDataReader reader)
		{
			InitializeFromDataReader(reader);
		}
		
		public static Dictionary<int, IExtendedRateItem> GetByRateItem(RateItem ri)
		{
			return GetByRateItem(ri.Id);
		}

        public static Dictionary<int, IExtendedRateItem> GetByRateItem(int rateItemId)
        {
            var extended = AppServices.Cache.Get<InternalExtendedRateItemCollection>("ri_ext:" + rateItemId,
                TimeSpan.FromMinutes(60), () =>
            {
                Collection<ExtendedRateItem> collection = new Collection<ExtendedRateItem>();

                using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "RateItemsExtendedGetByRateItemId", rateItemId))
                {
                    while (dr.Read())
                    {
                        collection.Add(new ExtendedRateItem(dr));
                    }
                }

                return new InternalExtendedRateItemCollection(collection);
            }).ExtendedRateItems ?? new Collection<ExtendedRateItem>();

            Dictionary<int, IExtendedRateItem> rateItems = new Dictionary<int, IExtendedRateItem>();

            foreach (var x in extended)
            {
                rateItems.Add(x.BodyTypeId, x);
            }

            return rateItems;
        }

        public static Dictionary<int, IExtendedRateItem> GetByRateItem(RateItem ri, int accountId)
        {
            Dictionary<int, IExtendedRateItem> rateItems = new Dictionary<int, IExtendedRateItem>();

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "AccountRateItemsExtendedGetByRateItemId", ri.Id, accountId))
            {
                while (dr.Read())
                {
                    var x = new Accounts.ExtendedRateItem(dr);
                    rateItems.Add(x.BodyTypeId, x);
                }
            }

            return rateItems;
        }

		private void InitializeFromDataReader(SqlDataReader reader)
		{
			Id = Convert.ToInt32(reader["RateItemExtendedId"]);
			RateItemId = Convert.ToInt32(reader["RateItemId"]);
			BodyTypeId = Convert.ToInt32(reader["BodyTypeId"]);
			BaseAmount = Amount = Convert.ToDecimal(reader["Amount"]);
            FreeQuantity = reader.GetValueOrDefault<int>("FreeQuantity");
			CreateDate = Convert.ToDateTime(reader["CreateDate"]);
		}

		#region Insert/Update/Delete methods
		/// <summary>
		/// Saves the ExtendedRateItem object to the data store.
		/// </summary>
		public void Save()
		{
			if (Id == 0)
			{
				throw new Extric.Towbook.TowbookException("No such ExtendedRateItem. Can't save " +
					"object! (this object should have already been discarded!)" + Id);
			}

            if (BodyTypeId < 1)
                throw new Extric.Towbook.TowbookException("BodyTypeId wasn't set. Cannot save without this.");

			if (Id == -1)
			{
				DbInsert();
			}
			else
			{
				DbUpdate();
			}

            RateItem.InvalidateCache(this.RateItemId);
        }

		public void Delete(User deleter)
		{
            try
            {
                SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                    "RateItemsExtendedDeleteByid",
                    new SqlParameter("@RateItemExtendedId", Id));
                Id = 0;
            }
            finally
            {
                RateItem.InvalidateCache(this.RateItemId);
            }

		}

		private void DbInsert()
		{
			Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
				"RateItemsExtendedInsert",
				new SqlParameter("@RateItemId", RateItemId),
				new SqlParameter("@BodyTypeId", BodyTypeId),
				new SqlParameter("@Amount", Amount),
                new SqlParameter("@FreeQuantity", FreeQuantity)));
		}

		private void DbUpdate()
		{
			SqlHelper.ExecuteNonQuery(Core.ConnectionString,
				"RateItemsExtendedUpdateById",
				new SqlParameter("@RateItemExtendedId", Id),
				new SqlParameter("@RateItemId", RateItemId),
				new SqlParameter("@BodyTypeId", BodyTypeId),
				new SqlParameter("@Amount", Amount),
                new SqlParameter("@FreeQuantity", FreeQuantity)
				);
		}

		#endregion

		#region Public Properties

        [ProtoMember(1)]
        public int Id { get; private set; }
        [ProtoMember(2)]
        public int BodyTypeId { get; set; }
        [ProtoMember(3)]
        public int RateItemId { get; set; }
        [ProtoMember(4)]
        public decimal BaseAmount { get; private set; }

        [ProtoMember(5)]
        public decimal Amount { get; set; }

        [ProtoMember(6)]
        public DateTime CreateDate { get; private set; }

        [ProtoMember(7)]
        public decimal FreeQuantity { get; set; }

        #endregion

    }

}
