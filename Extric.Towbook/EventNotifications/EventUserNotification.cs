using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.EventNotifications
{
    [Table("EventUserNotifications")]
    public class EventUserNotification
    {
        [Key("EventUserNotificationId")]
        public int Id { get; set; }
        public int EventNotificationId { get; set; }
        public int UserId { get; set; }
        public int CompanyId { get; set; }
        public bool? RequireEmail { get; set; }
        public bool? RequireText { get; set; }
        public bool? RequirePushNotification { get; set; }
        public bool? RequireWebNotification { get; set; }
        public int OwnerUserId { get; set; }
        public DateTime CreateDate { get; private set; }
        public bool Deleted { get; private set; }
        public int? DeletedByUserId { get; set; }
        public DateTime? DeleteDate { get; private set; }

        public static EventUserNotification GetById(int id)
        {
            return SqlMapper.Query<EventUserNotification>(
                "SELECT * FROM EventUserNotifications WHERE EventUserNotificationId=@Id and Deleted=0", new { Id = id }).FirstOrDefault();
        }

        public static Collection<EventUserNotification> GetByCompanyId(int companyId)
        {
            var rv = SqlMapper.Query<EventUserNotification>(
                "SELECT * FROM EventUserNotifications WHERE CompanyId=@Id and Deleted=0", new { Id = companyId })
                .ToCollection();

            return rv;
        }

        public static async Task<Collection<EventUserNotification>> GetByCompanyIdAsync(int companyId)
        {
            var rv = (await SqlMapper.QueryAsync<EventUserNotification>(
                "SELECT * FROM EventUserNotifications WHERE CompanyId=@Id and Deleted=0", new { Id = companyId }))
                .ToCollection();

            return rv;
        }

        public static Collection<EventUserNotification> GetByUserId(int companyId, int userId)
        {
            var rv = SqlMapper.Query<EventUserNotification>(
                "SELECT * FROM EventUserNotifications WHERE CompanyId=@Id and UserId=@UserId and Deleted=0", new { Id = companyId, UserId = userId })
                .ToCollection();

            return rv;
        }

        public static async Task<Collection<EventUserNotification>> GetByUserIdAsync(int companyId, int userId)
        {
            var rv = (await SqlMapper.QueryAsync<EventUserNotification>(
                "SELECT * FROM EventUserNotifications WHERE CompanyId=@Id and UserId=@UserId and Deleted=0", new { Id = companyId, UserId = userId }))
                .ToCollection();

            return rv;
        }

        public static Collection<EventUserNotification> GetByCompanyId(int companyId, int notificationItemId)
        {
            var rv = SqlMapper.Query<EventUserNotification>(
                "SELECT * FROM EventUserNotifications WHERE CompanyId=@Id AND EventNotificationId=@NotificationItemId AND Deleted=0", new { Id = companyId, NotificationItemId = notificationItemId })
                .ToCollection();

            return rv;
        }

        public static IEnumerable<EventUserNotification> GetByCompanyId(int companyId, int notificationItemId, int userId)
        {
            return SqlMapper.Query<EventUserNotification>(
                $"SELECT * FROM EventUserNotifications WHERE " +
                $"  CompanyId=@CompanyId AND EventNotificationId=@Id AND UserId=@UserId AND Deleted=0",
                new
                {
                    CompanyId = companyId,
                    Id = notificationItemId,
                    UserId = userId
                });
        }

        public EventUserNotification Save(int? userId = null)
        {
            if (Id < 1)
            {
                this.CreateDate = DateTime.Now;
                this.OwnerUserId = userId ?? 0;

                Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);
            }

            return this;
        }

        public EventUserNotification Delete(int userId)
        {
            Deleted = true;
            DeleteDate = DateTime.Now;
            DeletedByUserId = userId;
            return Save();
        }

        public static void Revert(int id, int userId, int companyId, int performerId)
        {
            var items = GetByCompanyId(companyId, id, userId);
            foreach (var item in items)
                item.Delete(performerId);
        }
    }
}
