using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Utility;

namespace Extric.Towbook.EventNotifications
{
    [Table("EventNotificationAlertSounds")]
    public class EventNotificationAlertSound
    {
        [Key("EventNotificationAlertSoundId")]
        public int Id { get; set; }
        public int EventNotificationId { get; set; }
        public int CompanyId { get; set; }
        public int? UserId  { get; set; }
        public int EventNotificationSoundId { get; set; }
        public bool isDeleted { get; set; }

        public static EventNotificationAlertSound GetByUserId(int companyId, int itemId, int userId)
        {
            var items = GetByCompanyIds(new[] { companyId });

            return items?.FirstOrDefault(f => f.EventNotificationId == itemId && f.UserId == userId) 
                ?? items?.FirstOrDefault(f => f.EventNotificationId == itemId && f.UserId.GetValueOrDefault() == 0);

        }

        public static EventNotificationAlertSound GetByItemId(int companyId, int itemId)
        {
            var items = GetByCompanyIds(new[] { companyId });
            
            return items?.FirstOrDefault(f => f.EventNotificationId == itemId && f.UserId.GetValueOrDefault() == 0);
        }

        public static IEnumerable<EventNotificationAlertSound> GetByCompanyIds(int[] companyIds)
        {
            var sql = "SELECT * FROM EventNotificationAlertSounds WHERE CompanyId IN @CompanyIds AND isDeleted = 0";

            List<EventNotificationAlertSound> items = new List<EventNotificationAlertSound>();

            foreach (var ids in companyIds.Batch(500))
            {
                items.AddRange(SqlMapper.Query<EventNotificationAlertSound>(sql, new
                {
                    CompanyIds = companyIds
                }));
            }

            return items;
        }

        public static async Task<IEnumerable<EventNotificationAlertSound>> GetByCompanyIdsAsync(int[] companyIds)
        {
            var sql = "SELECT * FROM EventNotificationAlertSounds WHERE CompanyId IN @CompanyIds AND isDeleted = 0";

            List<EventNotificationAlertSound> items = new List<EventNotificationAlertSound>();

            foreach (var ids in companyIds.Batch(500))
            {
                items.AddRange(await SqlMapper.QueryAsync<EventNotificationAlertSound>(sql, new
                {
                    CompanyIds = companyIds
                }));
            }

            return items;
        }

        public async Task Save()
        {
            if(Id == 0)
            {
                Id = (int)await SqlMapper.InsertAsync(this);
            }
            else
            {
                await SqlMapper.UpdateAsync(this);
            }
        }

        public async Task Delete()
        {
            isDeleted = true;
            await Save();
        }
    }
}
