using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.SqlClient;
using System.Data;
using Extric.Towbook.Dispatch;
using System.Collections.ObjectModel;
using ProtoBuf;
using Glav.CacheAdapter.Core.DependencyInjection;
using Extric.Towbook.Utility;
using Extric.Towbook.Integration;
using Extric.Towbook.ActivityLogging;
using Newtonsoft.Json;
using System.Dynamic;
using Async = System.Threading.Tasks;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Extric.Towbook.Company;
using Extric.Towbook.Company.Accounting;

namespace Extric.Towbook
{
    /// <summary>
    /// An invoice is a fully contained object that is able to return:
    ///     - Subtotal
    ///     - Taxes
    ///     - GrandTotal 
    ///     - Payments
    ///     - Current Amount Due
    /// </summary>
    [Serializable]
    [ProtoContract]
    public class Invoice : TrackableObject
    {
        [ProtoMember(1)]
        public int Id { get; internal set; }
        [ProtoMember(2)]
        public int CompanyId { get; set; }

        [ProtoMember(3)]
        public DateTime CreateDate { get; private set; }

        [ProtoMember(4)]
        private int _dispatchEntryId;
        [ProtoMember(5)]
        private int _impoundId;

        [ProtoMember(6)]
        private Collection<InvoiceItem> _invoiceItems;

        [ProtoMember(7)]
        private decimal _subTotal = decimal.MinValue;

        [ProtoMember(8)]
        private decimal _subTotalOriginal = decimal.MinValue;

        [ProtoMember(9)]
        private decimal _tax = decimal.MinValue;
        [ProtoMember(10)]
        private decimal _taxOriginal = decimal.MinValue;

        [ProtoMember(11)]
        private decimal _paymentsTotal = decimal.MinValue;




        [ProtoMember(12)]
        private int? _accountId;

        [ProtoMember(13)]
        private bool _isTaxExempt;

        [ProtoMember(14)]
        private decimal _hiddenTotal = decimal.MinValue;

        [ProtoMember(15)]
        private decimal _taxableTotal = decimal.MinValue;

        [ProtoMember(16)]
        private decimal _ticketValue = decimal.MinValue;

        [ProtoMember(17)]
        private DateTime? _nextScheduledRecalculate;

        [ProtoMember(18)]
        private DateTime? _lastUpdated;

        public decimal PaymentsTotal
        {
            get => _paymentsTotal;
            internal set => _paymentsTotal = value;
        }

        /// <summary>
        /// Represents the Bill To Account for this invoice instance.
        /// </summary>
        public int? AccountId
        {
            get => _accountId;
            set => SetField(ref _accountId, value, "BillToAccountId");
        }


        public bool IsTaxExempt
        {
            get => _isTaxExempt;
            set => SetField(ref _isTaxExempt, value, "IsTaxExempt");
        }

        private Entry _dispatchEntry;

        public int DispatchEntryId
        {
            get => _dispatchEntryId;

        }

        [JsonIgnore]
        public Entry DispatchEntry
        {
            get
            {
                if (_dispatchEntry == null)
                {

                    if (_dispatchEntryId < 1)
                    {
                        if (Impound != null)
                        {
                            _dispatchEntry = Impound.DispatchEntry;
                            if (_dispatchEntry != null)
                                _dispatchEntryId = _dispatchEntry.Id;
                        }
                    }
                    if (_dispatchEntryId > 0)
                        _dispatchEntry = Entry.GetById(_dispatchEntryId);

                }

                return _dispatchEntry;
            }
            set
            {
                if (_dispatchEntryId != value.Id)
                {
                    _dispatchEntry = value;
                    _dispatchEntryId = value.Id;
                    SetField(ref _dispatchEntryId, value.Id, "DispatchEntryId");
                }
            }
        }

        internal bool InternalBlockWrites
        {
            get; set;
        }

        private Impounds.Impound _impound;
        [JsonIgnore]
        public Impounds.Impound Impound
        {
            get
            {
                if (_impoundId > 0)
                {
                    if (_impound == null || _impound.Id != _impoundId)
                        _impound = Impounds.Impound.GetById(_impoundId);

                    if (_impound != null)
                        _impound.Invoice = this;

                    return _impound;
                }

                if (_impoundId == 0)
                {
                    if (_dispatchEntryId > 0)
                    {
                        if (DispatchEntry != null && DispatchEntry.Impound == true)
                        {
                            var impound = Extric.Towbook.Impounds.Impound.GetByDispatchEntry(DispatchEntry);
                            if (impound != null)
                            {
                                _impoundId = impound.Id;
                                _impound = impound;
                                _impound.Invoice = this;

                                return _impound;
                            }
                        }
                    }
                }

                return null;
            }
            set
            {
                SetField(ref _impoundId, value != null ? value.Id : 0, "ImpoundId");
            }
        }

        public class ClassBalanceSummary
        {
            public int Id { get; internal set; }
            public decimal SubTotal { get; internal set; }
            public decimal Total { get; internal set; }
            public decimal Payments { get; internal set; }
            public decimal Balance { get; internal set; }

            public ClassBalanceSummary(int id, decimal total, decimal payments, decimal subTotal)
            {
                Id = id;
                Total = total;
                SubTotal = subTotal;
                Payments = payments;
                Balance = total - payments;
            }

            public ClassBalanceSummary(int id)
            {
                Id = id;
            }
        }

        private Collection<InvoicePayment> _payments;
        public Collection<InvoicePayment> Payments
        {
            get
            {
                return _payments?.ToCollection() ?? new Collection<InvoicePayment>();
            }
        }

        public void SetPayments(Collection<InvoicePayment> payments)
        {
            _payments = payments;
        }

        private Collection<ClassBalanceSummary> _classBalances;
        public IEnumerable<ClassBalanceSummary> ClassBalances
        {
            get
            {
                if (_classBalances != null)
                    return _classBalances;

                var rv = new Collection<ClassBalanceSummary>();

                if (_payments == null)
                    _payments = InvoicePayment.GetByDispatchEntryId(this.DispatchEntryId, false);

                var taxRate = DispatchEntry.Company.TaxRates.Take(1).FirstOrDefault();

                if (DispatchEntry.Attributes != null)
                {
                    var a = DispatchEntry.Attributes.Where(w => w.Key == AttributeValue.BUILTIN_TAXRATE_OVERRIDE).FirstOrDefault();
                    if (a.Value != null)
                    {
                        int av = 0;
                        if (int.TryParse(a.Value.Value, out av))
                            taxRate = DispatchEntry.Company.TaxRates.Where(w => w.Id == av).FirstOrDefault();
                    }

                    if (DispatchEntry.Invoice.IsTaxExempt)
                        taxRate = null;
                }

                if (InvoiceItems.All(o => o.ClassId == 0))
                {
                    // if payment is recorded with classId but, no invoice items have classId, then 
                    // get rid of it for now, otherwise it'll cause stuff to get doubled below.
                    foreach (var payment in _payments)
                    {
                        payment.ClassId = 0;
                    }
                }


                foreach (var x in InvoiceItems.GroupBy(o => o.ClassId))
                {
                    decimal subtotal = 0.0M;
                    decimal taxableSubtotal = 0.0M;

                    // Collect taxable subtotal from all invoice items
                    foreach (var ii in x)
                    {
                        if (taxRate != null)
                        {
                            if (ii.Taxable)
                                taxableSubtotal += ii.Total;
                        }

                        subtotal += ii.Total;
                    }

                    var tax = taxRate != null ? Math.Round(taxableSubtotal * (taxRate.Rate / 100), 2, MidpointRounding.AwayFromZero) : 0;
                    var total = subtotal + tax;

                    var cbm = new ClassBalanceSummary(x.Key,
                        total,
                        _payments.Where(o => o.ClassId == x.Key).Sum(o => o.Amount),
                        subtotal);

                    rv.Add(cbm);
                }

                foreach (var x in _payments.Where(o =>
                     !InvoiceItems.Where(ii => ii.ClassId == o.ClassId).Any()).GroupBy(z => z.ClassId))
                {
                    rv.Add(new ClassBalanceSummary(
                        x.Key,
                        0,
                        _payments.Where(o => o.ClassId == x.Key)
                            .Sum(o => o.Amount),
                        0));
                }

                _classBalances = rv;

                return rv;
            }
            internal set
            {
                _classBalances = value.ToCollection();
            }
        }

        public Invoice()
        {
            MarkAsDirty();
        }

        public Invoice(IDataReader reader)
        {
            Id = reader.GetValue<int>("InvoiceId");
            CompanyId = reader.GetValue<int>("CompanyId");

            _dispatchEntryId = reader.GetValue<int>("DispatchEntryId");
            _impoundId = reader.GetValue<int>("ImpoundId");

            CreateDate = reader.GetValue<DateTime>("CreateDate");

            _subTotalOriginal = _subTotal = (reader["SubTotal"] == DBNull.Value ? decimal.MinValue : (decimal)reader["SubTotal"]);

            _taxOriginal = _tax = (reader["Tax"] == DBNull.Value ? decimal.MinValue : (decimal)reader["Tax"]);
            _paymentsTotal = reader.GetValue<decimal>("PaymentsTotal");
            _accountId = reader.GetValueOrNull<int>("AccountId");
            _isTaxExempt = reader.GetValue<bool>("IsTaxExempt");

            _hiddenTotal = (reader["HiddenTotal"] == DBNull.Value ? decimal.MinValue : (decimal)reader["HiddenTotal"]);
            _taxableTotal = (reader["TaxableTotal"] == DBNull.Value ? decimal.MinValue : (decimal)reader["TaxableTotal"]);
            _ticketValue = (reader["TicketValue"] == DBNull.Value ? decimal.MinValue : (decimal)reader["TicketValue"]);

            _nextScheduledRecalculate = (reader["NextScheduledRecalculate"] == DBNull.Value ? null : (DateTime?)reader.GetValue<DateTime>("NextScheduledRecalculate"));
            _lastUpdated = (reader["LastUpdated"] == DBNull.Value ? null : (DateTime?)reader.GetValue<DateTime>("LastUpdated"));
        }

        public static Invoice Map(dynamic row)
        {
            var get = new Invoice();

            get.Id = row.InvoiceId;
            get.CompanyId = row.CompanyId;

            get._dispatchEntryId = row.DispatchEntryId ?? 0;
            get._impoundId = row.ImpoundId ?? 0;
            get.CreateDate = row.CreateDate;


            get._subTotalOriginal = get._subTotal = Decimal.Round(row.SubTotal ?? decimal.MinValue, 2, MidpointRounding.AwayFromZero);
            get._taxOriginal = get._tax = Decimal.Round(row.Tax ?? decimal.MinValue, 2, MidpointRounding.AwayFromZero);
            get._paymentsTotal = Decimal.Round(row.PaymentsTotal ?? 0, 2, MidpointRounding.AwayFromZero);
            get._accountId = row.AccountId;
            get._isTaxExempt = row.IsTaxExempt;

            get._hiddenTotal = row.HiddenTotal ?? decimal.MinValue;
            get._taxableTotal = row.TaxableTotal ?? decimal.MinValue;
            get._ticketValue = row.TicketValue ?? decimal.MinValue;

            if (get._dispatchEntryId > 0)
                AppServices.Cache.AddToPerRequestCache("inv_d:" + get._dispatchEntryId, get);

            if (get._nextScheduledRecalculate != null && get._nextScheduledRecalculate < DateTime.Now)
            {
                get.ForceRecalculate(true);
            }

            return get;
        }

        public static IEnumerable<Invoice> GetByIds(int[] invoiceIds)
        {
            var list = new Collection<Invoice>();

            foreach (var batch in invoiceIds.OrderBy(o => o).Batch(500))
            {
                string ids = string.Join(",", batch);

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "InvoicesGetByInvoiceIds", new SqlParameter("@InvoiceIds", String.Join(", ", ids))))
                {
                    while (dr.Read())
                    {
                        list.Add(new Invoice(dr));
                    }
                }
            }

            return list;
        }

        public static async Task<IEnumerable<Invoice>> GetByIdsAsync(int[] invoiceIds)
        {
            var list = new Collection<Invoice>();

            foreach (var batch in invoiceIds.OrderBy(o => o).Batch(500))
            {
                string ids = string.Join(",", batch);

                using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "InvoicesGetByInvoiceIds", new SqlParameter("@InvoiceIds", String.Join(", ", ids))))
                {
                    while (await dr.ReadAsync())
                    {
                        list.Add(new Invoice(dr));
                    }
                }
            }

            return list;
        }

        //TODO: propagate async method
        public static Invoice GetById(int invoiceId)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "InvoicesGetByInvoiceId", new SqlParameter("@InvoiceId", invoiceId)))
            {
                if (dr.Read())
                {
                    var get = new Invoice(dr);

                    if (dr.NextResult())
                    {
                        if (dr.HasRows)
                        {
                            get._invoiceItems = InvoiceItem.GetFromExistingReader(dr, get);
                            get.ApplyAutomaticRules();
                        }
                        else
                            get._invoiceItems = new Collection<InvoiceItem>();
                    }

                    if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                    {
                        get.ForceRecalculate(true);
                    }

                    return get;
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<Invoice> GetByIdAsync(int invoiceId)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "InvoicesGetByInvoiceId", new SqlParameter("@InvoiceId", invoiceId)))
            {
                if (await dr.ReadAsync())
                {
                    var get = new Invoice(dr);

                    if (await dr.NextResultAsync())
                    {
                        if (dr.HasRows)
                        {
                            get._invoiceItems = InvoiceItem.GetFromExistingReader(dr, get);
                            get.ApplyAutomaticRules();
                        }
                        else
                            get._invoiceItems = new Collection<InvoiceItem>();
                    }

                    if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                    {
                        get.ForceRecalculate(true);
                    }

                    return get;
                }
                else
                {
                    return null;
                }
            }
        }

        public static Invoice GetByImpound(int impoundId)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "InvoicesGetByImpoundId", new SqlParameter("@ImpoundId", impoundId)))
            {

                if (dr.Read())
                {
                    var get = new Invoice(dr);

                    if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                    {
                        get.ForceRecalculate(true);
                    }

                    return get;
                }
                else
                {
                    return null;
                }
            }
        }

        //TODO: propagate the async method
        public static Invoice GetByDispatchEntry(int dispatchEntryId)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "InvoicesGetByDispatchEntryId",
                new SqlParameter("@DispatchEntryId", dispatchEntryId)))
            {
                if (dr.Read())
                {
                    var get = new Invoice(dr);    

                    if (get._dispatchEntryId == 0)
                    {
                        get._dispatchEntryId = dispatchEntryId;
                    }

                    if (dr.NextResult())
                    {
                        if (dr.HasRows)
                        {
                            get._invoiceItems = InvoiceItem.GetFromExistingReader(dr, get);
                            get.ApplyAutomaticRules();
                        }
                        else
                            get._invoiceItems = new Collection<InvoiceItem>();
                    }
                    if (dr.NextResult())
                    {

                    }

                    if (dr.NextResult())
                    {
                        if (dr.HasRows)
                        {
                            var items = InvoiceItem.InvoiceItemDriverCommission.GetFromExistingReader(dr);
                            foreach (var cc in items.GroupBy(o => o.InvoiceItemId))
                            {
                                var item = get._invoiceItems.FirstOrDefault(o => o.Id == cc.Key);
                                if (item != null)
                                {
                                    item.DriverCommissions = cc.ToCollection();
                                }
                            }
                        }
                    }
                    // this will never happen - the code above sets NextScheduled. 
                    if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                    {
                        get.ForceRecalculate(true);
                    }

                    return get;

                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<Invoice> GetByDispatchEntryAsync(int dispatchEntryId)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "InvoicesGetByDispatchEntryId",
                new SqlParameter("@DispatchEntryId", dispatchEntryId)))
            {
                if (await dr.ReadAsync())
                {
                    var get = new Invoice(dr);    

                    if (get._dispatchEntryId == 0)
                    {
                        get._dispatchEntryId = dispatchEntryId;
                    }

                    if (await dr.NextResultAsync())
                    {
                        if (dr.HasRows)
                        {
                            get._invoiceItems = InvoiceItem.GetFromExistingReader(dr, get);
                            get.ApplyAutomaticRules();
                        }
                        else
                            get._invoiceItems = new Collection<InvoiceItem>();
                    }
                    if (await dr.NextResultAsync())
                    {

                    }

                    if (await dr.NextResultAsync())
                    {
                        if (dr.HasRows)
                        {
                            var items = InvoiceItem.InvoiceItemDriverCommission.GetFromExistingReader(dr);
                            foreach (var cc in items.GroupBy(o => o.InvoiceItemId))
                            {
                                var item = get._invoiceItems.FirstOrDefault(o => o.Id == cc.Key);
                                if (item != null)
                                {
                                    item.DriverCommissions = cc.ToCollection();
                                }
                            }
                        }
                    }
                    // this will never happen - the code above sets NextScheduled. 
                    if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                    {
                        get.ForceRecalculate(true);
                    }

                    return get;

                }
                else
                {
                    return null;
                }
            }
        }

        public class TempStorageItem
        {
            public string Name { get; set; }
            public decimal Quantity { get; set; }
            public decimal Price { get; set; }
            public IRateItem RateItem { get; set; }
        }

        public void UpdateHourlyStorageItem(InvoiceItem i, Impounds.Impound imp)
        {
            if (imp?.DispatchEntry?.Company == null)
                return;

            if (i?.RateItem == null ||
                !i.RateItem.IsStorageItem() ||
                imp?.DispatchEntry == null ||
                !imp.DispatchEntry.Impound ||
                !imp.DispatchEntry.Company.HasFeature(Generated.Features.Impounds_HourlyStorage) ||
                imp.DispatchEntry.Account == null ||
                imp.DispatchEntry.Account.Id <= 1)
                return;

            var asset = imp.DispatchEntry.Assets.FirstOrDefault();
            if (asset == null)
                return;

            var bodyType = Vehicle.BodyType.GetById(asset.BodyTypeId);
            if (bodyType == null)
                return;

            var ri = RateItem.GetById(i.RateItem.RateItemId);
            if (i.RateItem?.Predefined?.Id != PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
                ri = (RateItem)RateItem.GetPredefinedByCompany(imp.DispatchEntry.Company).FirstOrDefault(f => f.Predefined?.Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE);

            var tsi = Impounds.Impound.CalculateHourlyStorageItem(imp.DispatchEntry.Account, ri, imp.ImpoundDate ?? DateTime.Now, imp.ReleaseDate ?? DateTime.Now, bodyType);
            if (tsi != null)
            {
                // not null, we have an to swap to an hourly storage item
                i.Quantity = tsi.Quantity;
                i.CustomPrice = tsi.Price;
                i.RateItem = tsi.RateItem; // force to hourly rate item
                i.ClassId = tsi.RateItem.DefaultClassId;
            }
            else
            {
                // Not within the hourly storage item...check if we need to change to default storage item
                if (i.RateItem?.Predefined?.Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
                {
                    IRateItem r = RateItem.GetById(imp.DispatchEntry.Account.DefaultStorageRateItemId.GetValueOrDefault());

                    Accounts.RateItem ari = Towbook.Accounts.RateItem.GetByRateItem(imp.DispatchEntry.Account, r);
                    if (ari != null)
                        r = ari;

                    var defaultRateItem = r ?? imp.DispatchEntry.Account.StorageRate;
                    if (defaultRateItem != null)
                    {
                        //TODO can be async
                        var sr = Extric.Towbook.Company.StorageRate.GetByAccountId(imp.DispatchEntry.CompanyId, imp.DispatchEntry.AccountId) ??
                                    Extric.Towbook.Company.StorageRate.GetByCompanyId(imp.DispatchEntry.CompanyId);

                        i.RateItem = defaultRateItem;
                        i.ClassId = defaultRateItem.DefaultClassId;
                        i.CustomPrice = Towbook.Accounts.RateItem.GetTransforemdCost(imp.DispatchEntry.Account, defaultRateItem, bodyType);
                        i.Quantity = imp.GetDaysHeldBillable(sr, imp.ImpoundDate ?? DateTime.Now, imp.ReleaseDate ?? DateTime.Now, defaultRateItem);
                    }
                }
            }
        }

        public async Task UpdateHourlyStorageItemAsync(InvoiceItem i, Impounds.Impound imp)
        {
            if (imp?.DispatchEntry?.Company == null)
                return;

            if (i?.RateItem == null ||
                !i.RateItem.IsStorageItem() ||
                imp?.DispatchEntry == null ||
                !imp.DispatchEntry.Impound ||
                !await imp.DispatchEntry.Company.HasFeatureAsync(Generated.Features.Impounds_HourlyStorage) ||
                imp.DispatchEntry.Account == null ||
                imp.DispatchEntry.Account.Id <= 1)
                return;

            var asset = imp.DispatchEntry.Assets.FirstOrDefault();
            if (asset == null)
                return;

            var bodyType = await Vehicle.BodyType.GetByIdAsync(asset.BodyTypeId);
            if (bodyType == null)
                return;

            var ri = await RateItem.GetByIdAsync(i.RateItem.RateItemId);
            if (i.RateItem?.Predefined?.Id != PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
                ri = (RateItem)RateItem.GetPredefinedByCompany(imp.DispatchEntry.Company).FirstOrDefault(f => f.Predefined?.Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE);

            var tsi = Impounds.Impound.CalculateHourlyStorageItem(imp.DispatchEntry.Account, ri, imp.ImpoundDate ?? DateTime.Now, imp.ReleaseDate ?? DateTime.Now, bodyType);
            if (tsi != null)
            {
                // not null, we have an to swap to an hourly storage item
                i.Quantity = tsi.Quantity;
                i.CustomPrice = tsi.Price;
                i.RateItem = tsi.RateItem; // force to hourly rate item
                i.ClassId = tsi.RateItem.DefaultClassId;
            }
            else
            {
                // Not within the hourly storage item...check if we need to change to default storage item
                if (i.RateItem?.Predefined?.Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
                {
                    IRateItem r = await RateItem.GetByIdAsync(imp.DispatchEntry.Account.DefaultStorageRateItemId.GetValueOrDefault());

                    Accounts.RateItem ari = await Towbook.Accounts.RateItem.GetByRateItemAsync(imp.DispatchEntry.Account, r);
                    if (ari != null)
                        r = ari;

                    var defaultRateItem = r ?? imp.DispatchEntry.Account.StorageRate;
                    if (defaultRateItem != null)
                    {
                        var sr = await Extric.Towbook.Company.StorageRate.GetByAccountIdAsync(imp.DispatchEntry.CompanyId, imp.DispatchEntry.AccountId) ??
                                    await Extric.Towbook.Company.StorageRate.GetByCompanyIdAsync(imp.DispatchEntry.CompanyId);

                        i.RateItem = defaultRateItem;
                        i.ClassId = defaultRateItem.DefaultClassId;
                        i.CustomPrice = Towbook.Accounts.RateItem.GetTransforemdCost(imp.DispatchEntry.Account, defaultRateItem, bodyType);
                        i.Quantity = imp.GetDaysHeldBillable(sr, imp.ImpoundDate ?? DateTime.Now, imp.ReleaseDate ?? DateTime.Now, defaultRateItem);
                    }
                }
            }
        }


        public void UpdateTieredItem(InvoiceItem i, bool force = false, Company.StorageRate sr = null)
        {
            if (InternalBlockWrites)
                return;

            using (var c = Core.GetConnection())
            {
                // doesn't apply to items that aren't associated with a RateItem.
                if (i.RateItem == null)
                    return;

                if (i.RateItem.ParentRateItemId > 0)
                    return;

                // only apply to rates that are in the storage category
                if (i.RateItem.CategoryId != 5)
                    return;

                // don't waste resources calculating if this isn't for an impound
                if (this.Impound == null)
                    return;

                // don't bother trying to calculate tiered items on a released vehicle
                if (this.Impound.ReleaseDate != null && force == false)
                    return;

                var children = RateItem.GetByParentId(i.RateItem.RateItemId);

                // first, see if we have all of them already added.
                int foundCount = 0;

                if (_invoiceItems == null)
                {
                    var tempII = InvoiceItems;
                }

                #region Find existing children rate items

                foreach (var x in children)
                {
                    var query = _invoiceItems.Where(o => o.RateItem?.RateItemId == x.RateItemId).DefaultIfEmpty().ToList();

                    foreach (var existingChild in query)
                    {
                        if (existingChild != null)
                            foundCount++;
                    }
                }

                var existingChildren = _invoiceItems.Where(o => o.RateItem != null &&
                    children.Select(z => z.RateItemId).Contains(o.RateItem.RateItemId)).ToList();

                if (children.Count > 0)
                {
                    // enforce root tiered item CANNOT have a price.
                    if (i.CustomPrice != 0)
                    {
                        i.CustomPrice = 0;

                        if (i.Quantity == -1)
                            i.Quantity = Impound.GetDaysHeldBillable(sr, null, null, i.RateItem);

                        using (var sc = Core.GetConnection())
                        {
                            i.Save(sc);
                        }
                    }

                    // [updated 7/29/16 - AC]
                    // Valid tiered children found for tiered storage.  We need to do a check here to delete any 
                    // child tiered invoice item(s) that are now marked as deleted in the database (user deleted)
                    // so they don't hang around on the invoice.  This was brought to our attention by 
                    // Collinsville (8526) - https://app.asana.com/0/87771349317454/157521440687448
                    var removeChildren = _invoiceItems.Where(w => w.RateItem != null
                            && w.RateItem.ParentRateItemId > 0  // must be tiered
                            && !children.Any(r => r.RateItemId == w.RateItem.RateItemId) // ignore valid items gathered already
                        ).ToCollection();

                    if (removeChildren.Any())
                    {
                        foreach (var childInvoiceItem in removeChildren)
                        {
                            // get the original rate item to make sure this is deleted (is this check necessary?)
                            var ri = RateItem.GetById(childInvoiceItem.RateItem.RateItemId);
                            if (ri != null)
                            {
                                // Remove the invoice item if the rateItem is marked as deleted or
                                // the parent rateItem is not the "now" tiered rate item
                                if (ri.Deleted || (ri.RateItemId != i.RateItem.RateItemId
                                            && childInvoiceItem.Quantity == 0
                                            && childInvoiceItem.Price == 0))
                                    childInvoiceItem.Delete(c);
                            }
                        }
                    }
                }

                // are there multiple items of this type?
                #region Remove any duplicate parents
                var duplicatesToRemove = _invoiceItems.Where(o => o.RateItem != null && o.RateItem.RateItemId == i.RateItem.RateItemId && o.Id != i.Id).ToList();

                foreach (var toDelete in duplicatesToRemove)
                {
                    if (toDelete != null)
                    {
                        toDelete.Delete(c);
                        _invoiceItems.Remove(toDelete);
                    }
                }
                #endregion

                #endregion

                // now lets add them.
                decimal totalDays = i.Quantity;
                decimal daysLeft = totalDays;

                // find the day ranges.
                decimal totalQuantity = 0;

                var rates = children.OrderBy(o => o.FreeQuantity).ToArray();

                //System.Diagnostics.Debug.WriteLine("[TIERED] Generate Tiered Items, Days to generate for: " + i.Quantity);

                // step 1: start out with the total quantity, example: 15.5
                // step 2: sort the list of child rate items by the freeQuantity, so that if we have 1:$25, 2:$35, 11:$35, 13:$100
                // step 3: run through the list of child rates and add them to the invoiceItems list.


                var newItems = new List<TempStorageItem>();

                // dont add any items if the days is 0.
                if (totalDays > 0)
                {
                    for (int fi = 0; fi < rates.Length; fi++)
                    {
                        var subRateItem = rates[fi];
                        InvoiceItem dailyItem = null;

                        // get the existing item, or create a new one.
                        dailyItem = _invoiceItems.FirstOrDefault(o => o.RateItem?.RateItemId == subRateItem.RateItemId);

                        if (dailyItem == null)
                        {
                            dailyItem = new InvoiceItem()
                            {
                                InvoiceId = i.InvoiceId,
                                RateItem = subRateItem,
                                ClassId = subRateItem.DefaultClassId,
                                Taxable = i.RateItem.Taxable,
                                Locked = InvoiceItem.InvoiceItemLock.LockedByUser
                            };
                        }

                        #region set price

                        var bodyType = _impound.DispatchEntry.BodyType;
                        if (bodyType != null)
                        {
                            var extended = subRateItem.ExtendedRateItems.Where(o => o.Key == bodyType.Id).FirstOrDefault().Value;
                            if (extended != null)
                                dailyItem.CustomPrice = extended.Amount;
                            else
                                dailyItem.CustomPrice = subRateItem.Cost;
                        }
                        else
                            dailyItem.CustomPrice = subRateItem.Cost;
                        #endregion

                        #region set quantity
                        dailyItem.Quantity = totalDays - totalQuantity;

                        if (rates.Length > fi + 1)
                        {
                            var next = rates[fi + 1];
                            if (dailyItem.Quantity > next.FreeQuantity - subRateItem.FreeQuantity - 1)
                                dailyItem.Quantity = next.FreeQuantity - subRateItem.FreeQuantity;
                        }
                        #endregion

                        dailyItem.CustomName = dailyItem.Quantity + " days storage";

                        totalQuantity += dailyItem.Quantity;

                        //System.Diagnostics.Debug.WriteLine("[TIERED] Add: " + dailyItem.Quantity + " @ " + dailyItem.Price);

                        if (i.Id < 1)
                        {
                            // the parent object has to be saved before we can reference it here.
                            i.Save(c);
                        }
                        //else
                        //    System.Diagnostics.Debug.WriteLine("[TIERED] Parent is already saved. " + i.Id);

                        dailyItem.RelatedInvoiceItemId = i.Id;

                        //System.Diagnostics.Debug.WriteLine("[TIERED] Set RelatedId to " + i.Id);


                        newItems.Add(new TempStorageItem()
                        {
                            Name = dailyItem.Name,
                            Price = dailyItem.Price,
                            Quantity = dailyItem.Quantity,
                            RateItem = dailyItem.RateItem
                        });

                        if (totalQuantity == totalDays)
                            break;
                    }
                }

                // existingChildren
                // newItems

                for (int ei = 0; ei < existingChildren.Count; ei++)
                {
                    if (ei < newItems.Count)
                    {
                        existingChildren[ei].CustomName = newItems[ei].Name;
                        existingChildren[ei].CustomPrice = newItems[ei].Price;
                        existingChildren[ei].Quantity = newItems[ei].Quantity;
                    }
                    else
                    {
                        existingChildren[ei].CustomName = "-deleted-";
                        existingChildren[ei].Quantity = 0;
                        existingChildren[ei].CustomPrice = 0;
                        existingChildren[ei] = null;
                    }
                }

                existingChildren = existingChildren.Where(o => o != null).ToList();

                if (newItems.Count != existingChildren.Count)
                {
                    // take the remainder and add them
                    for (int ni = existingChildren.Count; ni < newItems.Count; ni++)
                    {
                        existingChildren.Add(new InvoiceItem()
                        {
                            InvoiceId = i.InvoiceId,
                            RateItem = newItems[ni].RateItem,
                            ClassId = newItems[ni].RateItem.DefaultClassId,
                            Taxable = i.RateItem.Taxable,
                            Locked = InvoiceItem.InvoiceItemLock.LockedByUser,
                            CustomPrice = newItems[ni].Price,
                            Quantity = newItems[ni].Quantity,
                            CustomName = newItems[ni].Name,
                            RelatedInvoiceItemId = i.Id
                        });
                    }
                }

                foreach (var newItem in existingChildren)
                {
                    if (!_invoiceItems.Contains(newItem))
                        _invoiceItems.Add(newItem);
                }
            }
        }

        private void ApplyAutomaticRules(bool saveToDatabase = true)
        {
            if (InternalBlockWrites)
                return;
            if (DispatchEntry == null)
            {
                if (_dispatchEntryId > 0)
                {
                    // the entry is probably deleted.
                    //throw new TowbookException("DispatchEntry is null... InvoiceId=" + this.Id + ", DispatchEntryId=" + this._dispatchEntryId);
                }
                return;
            }

            UpdateStorageQuantities();
            AddItemsFromAccountRules(saveToDatabase);
        }

        private async Task ApplyAutomaticRulesAsync(bool saveToDatabase = true)
        {
            if (InternalBlockWrites)
                return;
            if (DispatchEntry == null)
            {
                if (_dispatchEntryId > 0)
                {
                    // the entry is probably deleted.
                    //throw new TowbookException("DispatchEntry is null... InvoiceId=" + this.Id + ", DispatchEntryId=" + this._dispatchEntryId);
                }
                return;
            }

            await UpdateStorageQuantitiesAsync();
            AddItemsFromAccountRules(saveToDatabase);
        }

        /// <summary>
        /// Applies pricing rules to the current invoice.
        /// </summary>
        private void AddItemsFromAccountRules(bool saveToDatabase = true)
        {
            if (InternalBlockWrites || this.DispatchEntry?.InternalBlockWrites == true)
                return;

            // dont apply if this invoice hasnt been saved yet.
            if (this.Id == 0)
                return;

            if (DispatchEntry == null ||
                DispatchEntry.AccountId == 0)
            {
                return;
            }

            if (DispatchEntry.Account.MasterAccountId == Accounts.MasterAccountTypes.OonAgero ||
                DispatchEntry.Account.MasterAccountId == Accounts.MasterAccountTypes.OonAllstate ||
                DispatchEntry.Account.MasterAccountId == Accounts.MasterAccountTypes.OonUrgently ||
                DispatchEntry.Account.MasterAccountId == Accounts.MasterAccountTypes.OonSwoop ||
                DispatchEntry.Account.MasterAccountId == Accounts.MasterAccountTypes.OonQuest)
                return;

            var allRules = Accounts.AccountRateItemRule.GetByAccountId(DispatchEntry.AccountId);

            IEnumerable<Accounts.AccountRateItemRule> rulesToRemove = Array.Empty<Accounts.AccountRateItemRule>();

            if (Impound != null)
            {
                var dh = Impound.DaysHeldBillable;
                rulesToRemove = allRules.Where(o => o.ImpoundAddAfterThisManyDaysHeld > 0 && dh < o.ImpoundAddAfterThisManyDaysHeld);

                // Nov 20, 2018 - AC
                // Note: "rulesToRemove" has not factored in a rule that matches purely on the reason of the entry.  That should not apply in this case.
                // Make sure to keep a rateItem that has a valid pricing rule condition. There may be some pricing rules that share a common
                // rateitem after x amount of days impounded but for an entirely different reason.  We need to keep any invoice items that have a rateitem
                // that matches a valid pricing rule based on the entry.reasonId.
                var validRulesByMatchedReason = allRules.Where(o => o.ImpoundAddAfterThisManyDaysHeld == 0 && o.ReasonId != null && o.ReasonId.Value == DispatchEntry.ReasonId).ToList();
                rulesToRemove = rulesToRemove.Where(w => !validRulesByMatchedReason.Select(s => s.RateItemId).Contains(w.RateItemId));
            }

            foreach (var rule in rulesToRemove)
            {
                var itemsToRemove = this._invoiceItems.Where(o => o.RateItem != null && o.RateItem.RateItemId == rule.RateItemId).ToList();
                if (itemsToRemove.Any())
                {
                    foreach (var x in itemsToRemove)
                    {
                        using (var c = Core.GetConnection())
                            x.Delete(c);
                        this._invoiceItems.Remove(x);
                    }

                    InvoiceItemRuleApplied.DeleteByAccountRateItemRuleId(rule.Id, _dispatchEntryId);
                }
            }

            // No rules to apply, return now (avoids querying the db for applied pricing rules per dispatchEntryId)
            if (allRules != null && allRules.Count() == 0)
                return;

            var applied = InvoiceItemRuleApplied.GetByDispatchEntryId(this.DispatchEntryId);

            var itemsChanged = false;
            foreach (var rule in allRules)
            {
                // Only apply rule once for the life of the invoice item

                var iira = applied.Where(w => w.AccountRateItemRuleId == rule.Id).FirstOrDefault();

                if (iira == null) // if the rule has NOT been applied:
                {
                    if (!_invoiceItems.Any(o => o.RateItem != null && o.RateItem.RateItemId == rule.RateItemId))
                    {
                        // the item to be added by the rule does not exist. 
                        if (rule.ImpoundAddAfterThisManyDaysHeld > 0 &&
                            (rule.ReasonId.GetValueOrDefault() == 0 ||
                            this.DispatchEntry.ReasonId == rule.ReasonId.GetValueOrDefault()) &&
                            Impound != null &&
                            Impound.ReleaseDate == null &&  // dont apply it if the vehicle is already released (we dont want to screw with released vehicles)
                            Impound.DaysHeldBillable >= rule.ImpoundAddAfterThisManyDaysHeld)
                        {
                            // only apply daysHeld based rules here... other ones are applied by the client (web/android/etc)
                            IRateItem r = RateItem.GetById(rule.RateItemId);

                            if (r?.Companies != null && !r.Companies.Any(o => o == this.CompanyId))
                                continue;

                            Extric.Towbook.Accounts.RateItem ari = Extric.Towbook.Accounts.RateItem.GetByRateItem(this.DispatchEntry.Account, r);
                            if (ari != null)
                                r = ari;

                            if (r != null)
                            {
                                decimal price = Accounts.RateItem.GetTransforemdCost(this.DispatchEntry.Account, r, this.DispatchEntry.BodyType);

                                var xi = new InvoiceItem()
                                {
                                    RateItem = r,
                                    ClassId = r.DefaultClassId,
                                    Quantity = 1,
                                    CustomPrice = price,
                                    InvoiceId = this.Id,
                                };

                                xi.AppliedPricingRuleId = rule.Id;
                                this._invoiceItems.Add(xi);
                                itemsChanged = true;
                            }
                        }
                        else if (rule.ImpoundAddAfterThisManyDaysHeld == 0)
                        {
                            if (!new int[] { Status.Completed.Id, Status.Cancelled.Id }.Contains(this.DispatchEntry.Status.Id))
                            {
                                if ((rule.ReasonId.GetValueOrDefault() == 0 || this.DispatchEntry.ReasonId == rule.ReasonId.GetValueOrDefault()) &&
                                    (rule.BodyTypeId.GetValueOrDefault() == 0 || (this.DispatchEntry.BodyType != null && this.DispatchEntry.BodyType.Id == rule.BodyTypeId.GetValueOrDefault())) &&
                                    (rule.AccountId.GetValueOrDefault() == 0 || rule.AccountId == this.DispatchEntry.AccountId))
                                {

                                    IRateItem r = RateItem.GetById(rule.RateItemId);

                                    if (r?.Companies != null && !r.Companies.Any(o => o == this.CompanyId))
                                        continue;

                                    var ari = Accounts.RateItem.GetByRateItem(this.DispatchEntry.Account, r);
                                    if (ari != null)
                                        r = ari;

                                    if (r != null)
                                    {
                                        decimal price = Accounts.RateItem.GetTransforemdCost(this.DispatchEntry.Account, r, this.DispatchEntry.BodyType);

                                        var xi = new InvoiceItem()
                                        {
                                            RateItem = r,
                                            ClassId = r.DefaultClassId,
                                            Quantity = 1,
                                            CustomPrice = price,
                                            InvoiceId = this.Id,
                                        };

                                        xi.AppliedPricingRuleId = rule.Id;
                                        this._invoiceItems.Add(xi);
                                        itemsChanged = true;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        // this is to save the RuleAPplied if the pricing rule was applied by the client (not in this method)
                        if (!rulesToRemove.Where(o => o.Id == rule.Id).Any())
                        {
                            foreach (var ii in this._invoiceItems.Where(o => o.RateItem != null && o.RateItem.RateItemId == rule.RateItemId))
                            {
                                if (ii.Id < 1)
                                {
                                    ii.AppliedPricingRuleId = rule.Id;
                                    itemsChanged = true;
                                }
                                else
                                {
                                    // Indicate the the rule has been applied to this invoice item
                                    iira = new InvoiceItemRuleApplied(rule.Id, _dispatchEntryId, ii.Id);
                                    iira.Save();
                                }
                            }
                        }
                    }
                }
            }

            if (itemsChanged && saveToDatabase)
            {
                // force an invoice save - pricing rules were applied.
                this.Save(null, null);
            }
        }

        public void UpdateStorageQuantities(bool? forceRecalc = false)
        {
            if (InternalBlockWrites)
                return;

            if (DispatchEntry == null)
                return;

            if (DispatchEntry.Impound == false ||
                DispatchEntry.Released == true ||
                (this.NextScheduledRecalculate.GetValueOrDefault() > DateTime.Now && forceRecalc.GetValueOrDefault() == false &&
                _invoiceItems == null))
            {
                // no need to calculate storage right now...
                return;
            }
            else
            {
                if (Impound == null || Impound.Deleted)
                    return;

                #region Check if the item exists, if it does, update the quantity and return.
                foreach (var ii in this.InvoiceItems)
                {
                    if (ii.IsStorageItem() && ii.RateItem.ParentRateItemId == 0)
                    {
                        // make sure its quantity is correct
                        if (ii.Quantity == -1 ||
                            (ii.Locked == InvoiceItem.InvoiceItemLock.Unlocked && Impound.ReleaseDate == null))
                        {
                            if (!ii.IsFieldDirty("Quantity"))
                            {
                                var daysHeldBillable = Impound.DaysHeldBillable;
                                if (ii.Quantity != daysHeldBillable)
                                {
                                    ii.Quantity = Impound.DaysHeldBillable;
                                    this.NextScheduledRecalculate = DateTime.Now.AddHours(-1);
                                }
                            }

                            #region Hourly Storage rate condition
                            UpdateHourlyStorageItem(ii, Impound);
                            #endregion
                        }

                        if (forceRecalc.GetValueOrDefault() || this.NextScheduledRecalculate.GetValueOrDefault() < DateTime.Now)
                            UpdateTieredItem(ii,
                                forceRecalc.GetValueOrDefault() || this.NextScheduledRecalculate.GetValueOrDefault() < DateTime.Now);

                        return;
                    }
                }

                /*if (this.InvoiceItems.Where(o => o.IsStorageItem() &&
                    o.Locked == InvoiceItem.InvoiceItemLock.Unlocked).Any())
                    NextScheduledRecalculate = this.Impound.NextStorageDayIs();
                else if (this.InvoiceItems.Where(o => o.IsStorageItem() &&
                    o.Locked == InvoiceItem.InvoiceItemLock.LockedByUser).Any())
                    NextScheduledRecalculate = null; */

                #endregion
            }
        }

        public async Task UpdateStorageQuantitiesAsync(bool? forceRecalc = false)
        {
            if (InternalBlockWrites)
                return;

            if (DispatchEntry == null)
                return;

            if (DispatchEntry.Impound == false ||
                DispatchEntry.Released == true ||
                (this.NextScheduledRecalculate.GetValueOrDefault() > DateTime.Now && forceRecalc.GetValueOrDefault() == false &&
                _invoiceItems == null))
            {
                // no need to calculate storage right now...
                return;
            }
            else
            {
                if (Impound == null || Impound.Deleted)
                    return;

                #region Check if the item exists, if it does, update the quantity and return.
                foreach (var ii in await this.GetInvoiceItemsAsync())
                {
                    if (ii.IsStorageItem() && ii.RateItem.ParentRateItemId == 0)
                    {
                        // make sure its quantity is correct
                        if (ii.Quantity == -1 ||
                            (ii.Locked == InvoiceItem.InvoiceItemLock.Unlocked && Impound.ReleaseDate == null))
                        {
                            if (!ii.IsFieldDirty("Quantity"))
                            {
                                var daysHeldBillable = Impound.DaysHeldBillable;
                                if (ii.Quantity != daysHeldBillable)
                                {
                                    ii.Quantity = Impound.DaysHeldBillable;
                                    this.NextScheduledRecalculate = DateTime.Now.AddHours(-1);
                                }
                            }

                            #region Hourly Storage rate condition
                            await UpdateHourlyStorageItemAsync(ii, Impound);
                            #endregion
                        }

                        if (forceRecalc.GetValueOrDefault() || this.NextScheduledRecalculate.GetValueOrDefault() < DateTime.Now)
                            UpdateTieredItem(ii,
                                forceRecalc.GetValueOrDefault() || this.NextScheduledRecalculate.GetValueOrDefault() < DateTime.Now);

                        return;
                    }
                }

                /*if (this.InvoiceItems.Where(o => o.IsStorageItem() &&
                    o.Locked == InvoiceItem.InvoiceItemLock.Unlocked).Any())
                    NextScheduledRecalculate = this.Impound.NextStorageDayIs();
                else if (this.InvoiceItems.Where(o => o.IsStorageItem() &&
                    o.Locked == InvoiceItem.InvoiceItemLock.LockedByUser).Any())
                    NextScheduledRecalculate = null; */

                #endregion
            }
        }

        public bool WillNeedToRetrieveInvoiceItemsFromDatabase
        {
            get
            {
                if (_invoiceItems == null && _subTotal == decimal.MinValue)
                    return true;
                else
                    return false;
            }
        }

        public Collection<InvoiceItem> InvoiceItems
        {
            get
            {
                if (_invoiceItems != null)
                    return _invoiceItems;
                else
                {
                    if (this.Id < 1)
                    {
                        _invoiceItems = new Collection<InvoiceItem>();
                    }
                    else
                    {
                        _invoiceItems = InvoiceItem.GetByInvoiceId(this.Id);
                    }
                }

                ApplyAutomaticRules();

                return _invoiceItems;
            }
            internal set
            {
                _invoiceItems = value;
            }
        }

        public async Task<Collection<InvoiceItem>> GetInvoiceItemsAsync()
        {
            if (_invoiceItems != null)
                return _invoiceItems;
            else
            {
                if (this.Id < 1)
                {
                    _invoiceItems = new Collection<InvoiceItem>();
                }
                else
                {
                    _invoiceItems = await InvoiceItem.GetByInvoiceIdAsync(this.Id);
                }
            }

            await ApplyAutomaticRulesAsync();

            return _invoiceItems;
        }

        public decimal TicketValue
        {
            get
            {
                if (_ticketValue == decimal.MinValue || (this._invoiceItems != null && !InternalBlockWrites))
                    SetField(ref _ticketValue, GetTicketValue(), "Ticket Value");

                return _ticketValue;
            }
        }

        public decimal HiddenTotal
        {
            get
            {
                if (_hiddenTotal == Decimal.MinValue || (this._invoiceItems != null && !InternalBlockWrites))
                    _hiddenTotal = RecalculateHiddenTotal();

                return _hiddenTotal;
            }
        }

        /// <summary>
        /// Returns the GrandTotal + HiddenTotal. Use this on Statements, and places that you want hidden items to show, such as reports.
        /// </summary>
        public decimal Total
        {
            get
            {
                return GrandTotal + HiddenTotal;
            }
        }

        public decimal GrandTotal
        {
            get
            {
                return Subtotal + Tax;
            }
        }

        public decimal BalanceDue
        {
            get
            {
                if (this.GrandTotal == decimal.MinValue)
                    return 0;

                if (_paymentsTotal == decimal.MinValue)
                    _paymentsTotal = 0;

                decimal r = this.GrandTotal - this.PaymentsTotal;
                if (r < 0 || r == decimal.MaxValue || r == decimal.MinValue)
                    return 0;
                else
                    return Decimal.Round(r, 2, MidpointRounding.AwayFromZero);
            }
        }

        public decimal TaxableTotal
        {
            get
            {
                if (_taxableTotal == Decimal.MinValue || (this._invoiceItems != null && !InternalBlockWrites))
                    SetField(ref _taxableTotal, RecalculateTaxableTotal(), "Taxable Total");

                return _taxableTotal;
            }
        }


        public decimal NonTaxableTotal
        {
            get
            {
                if (_taxableTotal == Decimal.MinValue || (this._invoiceItems != null && !InternalBlockWrites))
                    return RecalculateNonTaxableTotal();

                return 0;
            }
        }


        /// <summary>
        /// Returns the discount, if any, as a rate.  Discounts can be given in a percent value or a fixed amount.
        /// This method will translate the discount(s) into one value rate to be used for the calculating of the
        /// line item subtotal.  
        /// 
        /// Note: As of right now, Towbook discounts the line items BEFORE Tax is calculated 
        /// </summary>
        public decimal GetDiscountAsRate()
        {
            // Consider any discounts and calculate discount rate based on subtotal from above

            var discountItemList = InvoiceItems.Where(w => (w.Price < 0
                        && (w.CustomName == null || !w.CustomName.Contains("FreeQuantity Credit")))
                        || (w.CustomName != null && w.CustomName.ToLowerInvariant().StartsWith("discount:")));

            // Calculate a discount rate for pre-tax calcuation
            var subtotal = InvoiceItems.Where(w =>
                ((w.CustomName != null && w.CustomName.Contains("FreeQuantity Credit")) || w.Price > 0) &&
                (w.RateItem == null || w.RateItem.RateItemId != RateItem.BUILTIN_FUEL_SURCHARGE))
                .Sum(s => s.Total);

            // Consider any discounts and calculate discount rate based on subtotal from above
            decimal discountRate = 0.0m;

            if (discountItemList != null && subtotal > 0)
            {
                foreach (var item in discountItemList)
                {
                    // ignore case of default value being stored as a custom name
                    if (item.CustomName != null &&
                        item.CustomName.ToLowerInvariant().StartsWith("discount:") &&
                        !item.CustomName.Contains("Discount:% or $"))
                    {
                        // Now get the percentage or ad-hoc value and turn it into a rate
                        // replace discount:- with discount: to fix negative discount error
                        // TODO: change this so it doesn't require string parsing like this.. its dangeorus.
                        var name = item.CustomName.ToLowerInvariant().Replace("discount:-", "").Replace("discount:", "").Replace("$", "");

                        if (name.IndexOf("-") > 0)
                            name = name.Substring(0, name.IndexOf('-'));

                        if (name.Contains("%"))
                        {
                            name = name.Remove(name.IndexOf("%"));

                            // ignore the discount if it cant be parsed as a percentage.
                            if (decimal.TryParse(name, out discountRate))
                                discountRate = discountRate / 100;
                            else
                            {
                                using (var con = Core.GetConnection())
                                {
                                    // delete it 
                                    item.Delete(con);
                                }
                            }
                        }
                        else
                        {
                            decimal value = 0.0m;
                            if (decimal.TryParse(name, out value))
                                discountRate = discountRate + (value / subtotal);
                            else
                            {
                                using (var con = Core.GetConnection())
                                {
                                    item.Delete(con);
                                }
                            }
                        }
                    }
                    else if (item.RateItem == null && item.Price < 0)
                    {
                        discountRate = discountRate + Math.Abs(item.Price / subtotal);
                    }
                }
            }

            if (IgnoreDiscount)
                discountRate = 0;

            return discountRate;
        }

        private decimal RecalculateNonTaxableTotal()
        {
            decimal nonTaxTotal = 0;
            RecalculateTaxableTotal(out nonTaxTotal);
            
            return nonTaxTotal;
        }

        private decimal RecalculateTaxableTotal()
        {
            decimal nonTaxTotal = 0;
            return RecalculateTaxableTotal(out nonTaxTotal);
        }

        private decimal RecalculateTaxableTotal(out decimal nonTaxableTotal)
        {
            nonTaxableTotal = 0;

            if (this.IsTaxExempt)
                return 0;

            #region handle tax exempt
            Accounts.Account account = null;

            if (this.AccountId != null)
                account = Accounts.Account.GetById(this.AccountId.Value);

            if (account == null)
                account = DispatchEntry?.Account;

            if (account != null)
            {
                if (account.TaxExempt == true)
                {
                    // if account is billable, allow it to be tax exempt.
                    if (account.IsBillable())
                    {
                        return 0;
                    }
                    
                    // check for unbillable account types of PoliceDepartment or Municipalities that
                    // don't have a bill to account set, then respect tax exempt status until they do
                    // have a bill to account set.
                    if ((account.Type == Accounts.AccountType.PoliceDepartment) && AccountId == null)
                    {
                        return 0;
                    }
                }
            }

            if (Impound != null)
            {
                if (Impound.IsTaxExempt)
                {
                    return 0;
                }
            }

            #endregion

            decimal taxableAmount = 0;
            decimal nonTaxableAmount = 0;

            decimal discountRate = GetDiscountAsRate();

            // Find taxable items and calculate the tax
            foreach (InvoiceItem i in InvoiceItems)
            {
                if (i.Taxable)
                {
                    // do not discount fuel surcharge
                    if (i.RateItem?.RateItemId != RateItem.BUILTIN_FUEL_SURCHARGE)
                        taxableAmount += i.Total - (i.Total * discountRate);
                    else
                        taxableAmount += i.Total;
                }
                else
                {
                    if (i.RateItem?.RateItemId != RateItem.BUILTIN_FUEL_SURCHARGE)
                        nonTaxableAmount += i.Total - (i.Total * discountRate);
                    else
                        nonTaxableAmount += i.Total;
                }
            }

            nonTaxableTotal = decimal.Round(nonTaxableAmount, 2, MidpointRounding.AwayFromZero);

            return decimal.Round(taxableAmount, 2, MidpointRounding.AwayFromZero);
        }

        private bool ignoreDiscount;

        public bool IgnoreDiscount
        {
            get
            {
                return ignoreDiscount;
            }
            set
            {
                ignoreDiscount = value;

                if (ignoreDiscount)
                {
                    foreach (var i in InvoiceItems)
                    {
                        // find out if this is an account rate. 

                        var originalAmount = Accounts.RateItem.GetStandardAmount(
                            this.DispatchEntry.Account,
                            i.RateItem,
                            this.DispatchEntry.Assets.Where(o => o.Id == i.AssetId.GetValueOrDefault())
                            .FirstOrDefault()?.BodyTypeId ?? this.DispatchEntry.BodyType.Id);

                        if (i.CustomName != null && i.CustomName.Contains("FreeQuantity Credit"))
                            continue;

                        if (i.Price < 0 && originalAmount > 0)
                            originalAmount = -originalAmount;

                        if (i.RateItem?.RateItemId == RateItem.BUILTIN_DISCOUNT)
                        {
                            originalAmount = 0;
                            i.CustomPrice = 0;
                            i.Quantity = 0;
                            i.CustomName = "";
                        }

                        if (originalAmount != 0 && i.Price < originalAmount)
                        {
                            i.CustomPrice = originalAmount;

                            if (InvoiceItems.Where(o => o.RelatedInvoiceItemId.GetValueOrDefault() == i.Id &&
                            o.CustomName != null && o.CustomName.Contains("FreeQuantity Credit")).Any())
                            {
                                InvoiceItems.Where(o => o.RelatedInvoiceItemId.GetValueOrDefault() == i.Id).First().CustomPrice = -originalAmount;
                            }
                        }
                    }

                    InternalBlockWrites = true;
                }
                else
                {
                    _invoiceItems = null;
                    InternalBlockWrites = false;
                }

                ForceRecalculate(false);
            }
        }

        private decimal RecalculateSubTotal(bool saveToDatabase)
        {
            if (InvoiceItems == null || !InvoiceItems.Any())
            {
                _subTotal = 0;
                return 0;
            }



            decimal subTotal = 0;

            ApplyAutomaticRules(saveToDatabase);

            foreach (InvoiceItem i in InvoiceItems.Where(o => o.Hidden == false && o.ClassId != ChargeClass.Reimbursement))
            {
                if (ignoreDiscount && i.RateItem?.RateItemId == 2)
                    continue;

                if (ignoreDiscount)
                {
                    // find out if this is an account rate. 

                    var originalAmount = Accounts.RateItem.GetStandardAmount(this.DispatchEntry.Account,
                        i.RateItem,
                        this.DispatchEntry.Assets.Where(o => o.Id == i.AssetId.GetValueOrDefault())
                        .FirstOrDefault()?.BodyTypeId ?? this.DispatchEntry.BodyType.Id);

                    if (i.Price < 0 && originalAmount > 0)
                        originalAmount = -originalAmount;
                    else
                        originalAmount = i.Price;

                    subTotal += originalAmount * i.Quantity;
                    continue;  
                }
                 
                subTotal += i.Total;
            }

            if (Impound != null && !Impound.Deleted)
            {
                if (this.InvoiceItems.Where(o => o.IsStorageItem() && o.Locked == InvoiceItem.InvoiceItemLock.Unlocked).Any())
                    NextScheduledRecalculate = this.Impound.NextStorageDayIs();
                else
                {
                    if (this.Impound.ReleaseDate != null)
                    {
                        NextScheduledRecalculate = null;
                    }
                }
            }

            return subTotal;
        }

        public void ForceRecalculate(bool? saveToDatabase = false)
        {
            if (InternalBlockWrites)
                return;

            if (this.DispatchEntry?.Company == null)
                return;

            UpdateStorageQuantities(true);
            SetField(ref _subTotal, RecalculateSubTotal(saveToDatabase.GetValueOrDefault()), "Sub Total");
            _taxableTotal = RecalculateTaxableTotal();
            SetField(ref _tax, RecalculateTax(), "Tax");
            _hiddenTotal = RecalculateHiddenTotal();
            _ticketValue = GetTicketValue();


            var company = Company.Company.GetById(CompanyId);
            if (company.HasFeature(Generated.Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(CompanyId);
                if (DispatchEntry.IsWithinClosedAccountingPeriod(closedPeriodOptions)) return;
            }


            if (saveToDatabase.GetValueOrDefault())
                Save(null, null);
        }

        private void UpdateSubTotal(decimal subTotal)
        {
            if (InternalBlockWrites)
                return;

            if (IsFieldDirty("Sub Total"))
            {
                if (subTotal > (decimal)System.Data.SqlTypes.SqlMoney.MaxValue)
                    throw new TowbookException($"Subtotal value of {subTotal} is too large on Invoice {Id}.");

                var i = SqlMapper.QuerySP<dynamic>("InvoicesUpdateSubTotalById", new
                {
                    @InvoiceId = this.Id,
                    @SubTotal = subTotal
                }).FirstOrDefault();
            }
        }

        private async Task UpdateSubTotalAsync(decimal subTotal)
        {
            if (InternalBlockWrites)
                return;

            if (IsFieldDirty("Sub Total"))
            {
                if (subTotal > (decimal)System.Data.SqlTypes.SqlMoney.MaxValue)
                    throw new TowbookException($"Subtotal value of {subTotal} is too large on Invoice {Id}.");

                var i = (await SqlMapper.QuerySpAsync<dynamic>("InvoicesUpdateSubTotalById", new
                {
                    @InvoiceId = this.Id,
                    @SubTotal = subTotal
                })).FirstOrDefault();
            }
        }

        private void UpdateTax(decimal tax)
        {
            if (InternalBlockWrites)
                return;

            if (IsFieldDirty("Tax"))
            {
                SqlMapper.ExecuteSP("InvoicesUpdateTaxById", new
                {
                    InvoiceId = Id,
                    Tax = tax
                });
            }
        }

        private async Task UpdateTaxAsync(decimal tax)
        {
            if (InternalBlockWrites)
                return;

            if (IsFieldDirty("Tax"))
            {
                await SqlMapper.ExecuteSpAsync("InvoicesUpdateTaxById", new
                {
                    InvoiceId = Id,
                    Tax = tax
                });
            }
        }

        private decimal RecalculateHiddenTotal()
        {
            if (InvoiceItems == null || !InvoiceItems.Any())
            {
                _hiddenTotal = 0;
                return 0;
            }

            decimal hiddenTotal = 0;

            foreach (InvoiceItem i in InvoiceItems.Where(o => o.ClassId != ChargeClass.Reimbursement && o.Hidden == true))
            {
                if (ignoreDiscount && i.RateItem?.RateItemId == 2)
                    continue;

                hiddenTotal += i.Total;
            }

            return hiddenTotal;
        }



        private decimal RecalculateTax()
        {
            if (this._dispatchEntryId == 0)
                return 0;

            if (this.IsTaxExempt)
                return 0;

            if (_subTotal == 0)
                return 0;

            Accounts.Account account = null;

            if (this.AccountId != null)
            {
                account = Accounts.Account.GetById(this.AccountId.Value);
            }

            if (account == null)
                account = DispatchEntry?.Account;

            if (account != null)
            {
                // TODO: with this logic, we don't let the IsTaxExempt of the invoice be set.
                // the account will always override it. 

                if (account.TaxExempt == true)
                {
                    // if account is billable, allow it to be tax exempt.
                    if (account.IsBillable())
                    {
                        IsTaxExempt = true;
                        return 0;
                    }
                    else
                        IsTaxExempt = false;
                }
                else
                {
                    IsTaxExempt = false;
                }
            }

            if (Impound != null)
            {
                if (Impound.IsTaxExempt)
                {
                    IsTaxExempt = true;
                    return 0;
                }
            }

            decimal tax = 0;
            decimal taxRate = GetTaxRate()?.Rate ?? 0;


            if (taxRate == 0)
            {
                tax = 0;

                return tax;
            }

            var byLine = (Company.Company.GetById(CompanyId)?.Country == Company.Company.CompanyCountry.Canada &&
                this.Id > ********);

            var trate = (taxRate / 100);

            if (byLine)
            {
                // Find taxable items and calculate the tax
                foreach (var i in InvoiceItems.Where(o => o.ClassId != ChargeClass.Reimbursement))
                {
                    if (ignoreDiscount && (i.RateItem?.RateItemId == 2 ||
                        (i.CustomName != null && i.CustomName.ToLowerInvariant().StartsWith("discount:"))))
                        continue;

                    // treat discount as taxable if everything else on the invoice is taxable.
                    if ((i.RateItem?.RateItemId == 2 ||
                        (i.CustomName != null && i.CustomName.ToLowerInvariant().StartsWith("discount:"))))
                    {
                        if (InvoiceItems.Where(o => o.Id != i.Id).All(o => o.Taxable))
                            i.Taxable = true;
                    }

                    var lineName = i.CustomName?.ToLowerInvariant().Replace("-", "") ?? "";

                    if (i.Taxable)
                    {
                        tax += decimal.Round(i.Total * trate, 2, MidpointRounding.AwayFromZero);
                    }
                }
            }
            else
            {
                // this is the right way to do it.. the "American Way" ... round up and collect as much pennies as you can!  (But only on tax calculations)
                tax = decimal.Round(TaxableTotal * (taxRate / 100), 2, MidpointRounding.AwayFromZero);
            }

            if (tax < 0)
            {
                // do not allow negative taxes. 
                tax = 0;
            }

            return tax;
        }

        private async Task<decimal> RecalculateTaxAsync()
        {
            if (this._dispatchEntryId == 0)
                return 0;

            if (this.IsTaxExempt)
                return 0;

            if (_subTotal == 0)
                return 0;

            Accounts.Account account = null;

            if (this.AccountId != null)
            {
                account = await Accounts.Account.GetByIdAsync(this.AccountId.Value);
            }

            if (account == null)
                account = DispatchEntry?.Account;

            if (account != null)
            {
                // TODO: with this logic, we don't let the IsTaxExempt of the invoice be set.
                // the account will always override it. 

                if (account.TaxExempt == true)
                {
                    // if account is billable, allow it to be tax exempt.
                    if (account.IsBillable())
                    {
                        IsTaxExempt = true;
                        return 0;
                    }
                    else
                        IsTaxExempt = false;
                }
                else
                {
                    IsTaxExempt = false;
                }
            }

            if (Impound != null)
            {
                if (Impound.IsTaxExempt)
                {
                    IsTaxExempt = true;
                    return 0;
                }
            }

            decimal tax = 0;
            decimal taxRate = (await GetTaxRateAsync())?.Rate ?? 0;


            if (taxRate == 0)
            {
                tax = 0;

                return tax;
            }

            var byLine = ((await Company.Company.GetByIdAsync(CompanyId))?.Country == Company.Company.CompanyCountry.Canada &&
                this.Id > ********);

            var trate = (taxRate / 100);

            if (byLine)
            {
                // Find taxable items and calculate the tax
                foreach (var i in InvoiceItems.Where(o => o.ClassId != ChargeClass.Reimbursement))
                {
                    if (ignoreDiscount && (i.RateItem?.RateItemId == 2 ||
                        (i.CustomName != null && i.CustomName.ToLowerInvariant().StartsWith("discount:"))))
                        continue;

                    // treat discount as taxable if everything else on the invoice is taxable.
                    if ((i.RateItem?.RateItemId == 2 ||
                        (i.CustomName != null && i.CustomName.ToLowerInvariant().StartsWith("discount:"))))
                    {
                        if (InvoiceItems.Where(o => o.Id != i.Id).All(o => o.Taxable))
                            i.Taxable = true;
                    }

                    var lineName = i.CustomName?.ToLowerInvariant().Replace("-", "") ?? "";

                    if (i.Taxable)
                    {
                        tax += decimal.Round(i.Total * trate, 2, MidpointRounding.AwayFromZero);
                    }
                }
            }
            else
            {
                // this is the right way to do it.. the "American Way" ... round up and collect as much pennies as you can!  (But only on tax calculations)
                tax = decimal.Round(TaxableTotal * (taxRate / 100), 2, MidpointRounding.AwayFromZero);
            }

            if (tax < 0)
            {
                // do not allow negative taxes. 
                tax = 0;
            }

            return tax;
        }

        internal decimal GetTicketValue(bool autoSplit = true)
        {
            if (_dispatchEntryId == 0)
                return 0;

            var fuelSurchargeII = InvoiceItems.Where(w =>
                    w.ClassId != ChargeClass.Reimbursement &&
                    w.RateItem != null &&
                    w.RateItem.RateItemId == RateItem.BUILTIN_FUEL_SURCHARGE)
                    .FirstOrDefault();

            decimal? fuelSurcharge = fuelSurchargeII != null ? fuelSurchargeII.CustomPrice : null;
            decimal ticketAmount = Subtotal - (fuelSurcharge != null ? fuelSurcharge.Value : 0);
            decimal baseAmount = ticketAmount;

            if (DispatchEntry != null)
            {
                if (DispatchEntry.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_COMMISSION_TICKETVALUE))
                {
                    var value = DispatchEntry.Attributes[AttributeValue.BUILTIN_DISPATCH_COMMISSION_TICKETVALUE].Value;
                    bool isPercent = false;
                    if (value.Contains('%'))
                    {
                        value = value.Replace("%", "");
                        isPercent = true;
                    }
                    else if (value.Contains('$'))
                    {
                        value = value.Replace("$", "");
                    }

                    if (Decimal.TryParse(value, out baseAmount))
                    {
                        if (isPercent)
                            baseAmount = ticketAmount * (baseAmount / 100);
                    }
                }
                else
                {
                    // If no base is set, make it use the base divided by the number of drivers;
                    if (DispatchEntry.Drivers.Count > 0 && autoSplit)
                    {
                        baseAmount = baseAmount / DispatchEntry.Drivers.Count;
                    }
                }
            }

            return baseAmount;
        }

        public decimal Subtotal
        {
            get
            {
                var updateBecauseAnItemChanged = (this._invoiceItems != null && this._invoiceItems.Where(o => o.IsDirty && o.ClassId != ChargeClass.Reimbursement).Any());

                var notCachedOrNeedsRecalculate =
                    (_subTotal == decimal.MinValue || (NextScheduledRecalculate != null && NextScheduledRecalculate < DateTime.Now));

                if (notCachedOrNeedsRecalculate || updateBecauseAnItemChanged)
                {
                    SetField(ref _subTotal, RecalculateSubTotal(false), "Sub Total");
                    SetField(ref _tax, RecalculateTax(), "Tax");

                    if (notCachedOrNeedsRecalculate && !updateBecauseAnItemChanged)
                    {
                        UpdateSubTotal(_subTotal);
                        UpdateTax(_tax);
                    }
                }

                if (_subTotal == decimal.MinValue)
                    _subTotal = 0;

                return _subTotal;
            }
            internal set => _subTotal = value;
        }

        public async Task<decimal> GetSubtotalAsync()
        {
            var updateBecauseAnItemChanged = (this._invoiceItems != null && this._invoiceItems.Where(o => o.IsDirty && o.ClassId != ChargeClass.Reimbursement).Any());

            var notCachedOrNeedsRecalculate =
                (_subTotal == decimal.MinValue || (NextScheduledRecalculate != null && NextScheduledRecalculate < DateTime.Now));

            if (notCachedOrNeedsRecalculate || updateBecauseAnItemChanged)
            {
                //TO-DO ASYNC
                SetField(ref _subTotal, RecalculateSubTotal(false), "Sub Total");
                SetField(ref _tax, await RecalculateTaxAsync(), "Tax");

                if (notCachedOrNeedsRecalculate && !updateBecauseAnItemChanged)
                {
                    await UpdateSubTotalAsync(_subTotal);
                    await UpdateTaxAsync(_tax);
                }
            }

            if (_subTotal == decimal.MinValue)
                _subTotal = 0;

            return _subTotal;
        }

        public decimal Tax
        {
            get
            {
                if (_tax == decimal.MinValue)
                {
                    SetField(ref _tax, RecalculateTax(), "Tax");
                    UpdateTax(_tax);
                }

                return _tax;
            }
            internal set => _tax = value;
        }

        public async Task<decimal> GetTaxAsync()
        {
            if (_tax == decimal.MinValue)
            {
                SetField(ref _tax, (await RecalculateTaxAsync()), "Tax");
                await UpdateTaxAsync(_tax);
            }

            return _tax;
        }

        public DateTime? NextScheduledRecalculate
        {
            get
            {
                return _nextScheduledRecalculate;
            }
            set
            {
                if (_nextScheduledRecalculate != value)
                {
                    SetField(ref _nextScheduledRecalculate, value, "Next Scheduled Recalculate");
                }
            }
        }

        public DateTime? LastUpdated
        {
            get => _lastUpdated;
        }


        public void Save(AuthenticationToken token, string ipAddress) => Save(token, ipAddress, false);
        public void Save(AuthenticationToken token, string ipAddress, bool overrideClosedPeriod) => SaveInternal(token, ipAddress, false, overrideClosedPeriod).GetAwaiter().GetResult();

        public async Task SaveAsync(AuthenticationToken token, string ipAddress) => await SaveAsync(token, ipAddress, false);
        public async Task SaveAsync(AuthenticationToken token, string ipAddress, bool overrideClosedPeriods) => await SaveInternal(token, ipAddress, true, overrideClosedPeriods);

        private async Task SaveInternal(AuthenticationToken token, string ipAddress, bool async, bool overrideClosedPeriod)
        {
            if (IgnoreDiscount)
                throw new TowbookException("Cannot save Invoice while IgnoreDiscount is true. ");

            var company = Company.Company.GetById(CompanyId);
            if (!overrideClosedPeriod && company.HasFeature(Generated.Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(CompanyId);
                if (DispatchEntry.IsWithinClosedAccountingPeriod(closedPeriodOptions))
                {
                    throw new TowbookException($"Invoice {Id} for call {DispatchEntryId} is locked by closed period settings. An override is required to modify it.");
                }
            }
            // Handle clearing of cache
            // Handle calculating new subtotal/total.
            // save to database
            // use transactions

            try
            {
                if (!IsDirty && !this.InvoiceItems.Where(o => o.IsChanged == true).Any())
                {
                    //System.Diagnostics.Debug.WriteLine("Invoice.Save: Exiting without save -- no changes to save.");
                    return;
                }

                _lastUpdated = DateTime.Now;

                //System.Diagnostics.Debug.WriteLine("Saving because..." + this.ChangedFields.ToJson(true));
                using (var c = Core.GetConnection())
                {
                    // we need to get the ID.
                    if (Id == 0)
                        DbInsert(c);

                    #region First - Handle Tiered Storage Calculations
                    if (_impoundId > 0)
                    {
                        var isReleased = DispatchEntry.Impound == true && DispatchEntry.Released == true;

                        //TODO can be async
                        var sr = Company.StorageRate.GetByAccountId(CompanyId, DispatchEntry.AccountId) ??
                            Company.StorageRate.GetByCompanyId(CompanyId);

                        foreach (var x in this.InvoiceItems.Where(o => o.IsStorageItem()).ToCollection())
                        {
                            x.InvoiceId = Id;
                            x.AssetId = DispatchEntry.Assets?.FirstOrDefault()?.Id;

                            if (RateItem.GetByParentId(x.RateItem.RateItemId).Any())
                            {
                                UpdateTieredItem(x, !isReleased, sr);
                            }

                            
                        }
                    }
                    #endregion

                    List<int> deleted = new List<int>();

                    #region Save the individual invoice items first
                    foreach (var x in InvoiceItems)
                    {
                        x.InvoiceId = Id;

                        if (x.AssetId < 0)
                        {
                            var virtualAsset = this.DispatchEntry.Assets.FirstOrDefault(o => o.virtualId == x.AssetId);
                            x.AssetId = virtualAsset?.Id ?? null;
                        }

                        if (this.Impound != null &&
                           x.IsStorageItem())
                        {
                            this.NextScheduledRecalculate = DateTime.Now.AddHours(-1);

                            if ((x.Quantity == -1 || x.Quantity == Impound.DaysHeldBillable) && Impound.ReleaseDate == null)
                            {
                                x.Quantity = Impound.DaysHeldBillable;
                                x.Locked = InvoiceItem.InvoiceItemLock.Unlocked;
                            }
                            else
                            {
                                if (x.Quantity != Impound.DaysHeldBillable && x.Quantity != -1)
                                {
                                    x.Locked = InvoiceItem.InvoiceItemLock.LockedByUser;
                                }
                                else if (x.Quantity == -1)
                                {
                                    x.Quantity = Impound.DaysHeldBillable;
                                    x.Locked = InvoiceItem.InvoiceItemLock.Unlocked;
                                }
                                else
                                {
                                    if (Impound.ReleaseDate != null)
                                        x.Locked = InvoiceItem.InvoiceItemLock.LockedByUser;
                                }

                                //System.Diagnostics.Debug.WriteLine("Quantity: " + x.Quantity + ", " +
                                //    Impound.DaysHeldBillable + ", ReleaseDate = " + Impound.ReleaseDate + ": Allowing save to proceed with actual quantity.");
                            }

                            UpdateHourlyStorageItem(x, Impound);
                        }

                        x.Save(c);
                        if (x.Deleted)
                            deleted.Add(x.Id);
                    }
                    #endregion


                    if (deleted.Any())
                    {
                        foreach (var related in deleted)
                        {
                            var ii = InvoiceItems.FirstOrDefault(o => o.RelatedInvoiceItemId == related);
                            if (ii != null)
                            {
                                ii.Quantity = 0;
                                ii.CustomPrice = 0;
                                ii.Save(c);
                            }
                        }
                    }


                    #region Calculate FreeQuantity

                    var tempBucket = new List<InvoiceItem>();

                    foreach (var itemToHandle in 
                        InvoiceItems.Where(o => o.RateItem?.ParentRateItemId == 0 && 
                            (o.CustomName == null || !o.CustomName.Contains("FreeQuantity")) && 
                            o.RelatedInvoiceItemId == null))
                    {
                        decimal freeQuantity = itemToHandle.RateItem.FreeQuantity;

                        if (this.DispatchEntry?.Account != null)
                        {
                            var ari = await Accounts.RateItem.GetByRateItemAsync(this.DispatchEntry.Account, itemToHandle.RateItem);
                            if (ari != null)
                            {
                                if (ari.FreeQuantity > 0 ||
                                    new int[] { 80, 3738, 3011 }.Contains(this.DispatchEntry.CompanyId))
                                    freeQuantity = ari.FreeQuantity;
                            }
                        }

                        var allFreeItems = this.InvoiceItems.Where(o =>
                            o.RelatedInvoiceItemId == itemToHandle.Id &&
                            (o.RateItem != null && o.RateItem.ParentRateItemId == 0)
                        ).ToCollection();

                        var freeItem = allFreeItems.FirstOrDefault();

                        #region repair - remove any duplicate free items - unclear how it happens, but this clears them.
                        if (allFreeItems.Count > 1)
                        {
                            foreach (var x in allFreeItems)
                            {
                                if (x.Id != freeItem.Id)
                                {
                                    await x.DeleteAsync(c);
                                }
                            }
                        }
                        #endregion

                        if (freeItem == null)
                        {
                            if (freeQuantity > 0)
                            {
                                /* Don't add free credit for time based rate items (free minutes has been calculated into the quantity for rounding purposes) */
                                if (itemToHandle.RateItem?.TimeRound > 0)
                                    continue;

                                freeItem = new InvoiceItem()
                                {
                                    RelatedInvoiceItemId = itemToHandle.Id,
                                    InvoiceId = this.Id,
                                    CustomName = "FreeQuantity Credit " + itemToHandle.RateItem.RateItemId,
                                    RateItem = itemToHandle.RateItem,
                                    AssetId = itemToHandle.AssetId,
                                    ClassId = itemToHandle.ClassId
                                };

                                // don't allow two free items for the same RelatedInvoiceItemId to be added (see repair - remove any duplicates above,
                                // this should prevent that from happening)
                                if (!tempBucket.Any(o => o.RelatedInvoiceItemId == itemToHandle.Id))
                                    tempBucket.Add(freeItem);
                            }
                            else
                            {
                                continue;
                            }
                        }

                        freeItem.CustomPrice = -itemToHandle.Price;
                        // free=5, item=1
                        if (freeQuantity > itemToHandle.Quantity)
                            freeItem.Quantity = itemToHandle.Quantity;
                        else
                            freeItem.Quantity = freeQuantity;

                        freeItem.Taxable = itemToHandle.Taxable;
                    }

                    foreach (var x in tempBucket)
                    {
                        this.InvoiceItems.Add(x);
                        x.Save(c);
                    }

                    foreach (var x in this.InvoiceItems)
                    {
                        if (x.IsChanged)
                            x.Save(c);
                    }

                    #endregion

                    #region Remove any invoice items left over that were deleted (but don't delete storage!!!)

                    _invoiceItems = _invoiceItems.Where(o =>
                            o.RateItem.IsStorageItem() ||
                            (!o.RateItem.IsStorageItem() && (o.Price != 0 || o.Quantity != 0))
                        ).ToCollection();
                    #endregion

                    #region Recalculate the totals
                    ForceRecalculate(false);
                    #endregion
                    // save any items added by pricing rules.
                    foreach(var item in _invoiceItems)
                    {
                        if (item != null && item.IsDirty)
                            item.Save(c);
                    }
                    DbUpdate(false, c);
                }


                if (this.IsDirty && DispatchEntry != null)
                {
                    if (async)
                        await base.SaveAsync(token,
                           ActivityLogType.Invoice,
                           this.Id,
                           ActivityLogType.DispatchEntry,
                           DispatchEntry.Id,
                           (Id == 0 ? 1 : 2),
                           ipAddress);
                    else
                        base.Save(token,
                           ActivityLogType.Invoice,
                           this.Id,
                           ActivityLogType.DispatchEntry,
                           DispatchEntry.Id,
                           (Id == 0 ? 1 : 2),
                           ipAddress);
                }

                this.MarkAsClean();
            }
            finally
            {
                ResetCache();
            }
        }


        private void ResetCache()
        {
            AppServices.Cache.InvalidateCacheItem("inv:" + this.Id);
            AppServices.Cache.InvalidateCacheItem("inv_i:" + this._impoundId);
            AppServices.Cache.InvalidateCacheItem("inv_d:" + this._dispatchEntryId);
            AppServices.Cache.InvalidateCacheItem("inv:" + this.Id);

            if (this.DispatchEntry != null && this.DispatchEntry.AccountId > 0)
                Accounts.Account.UpdateBalance(this.DispatchEntry.AccountId, this.CompanyId);

            if (this.AccountId.GetValueOrDefault() > 0)
            {
                Accounts.Account.UpdateBalance(this.AccountId.Value, this.CompanyId);
            }
        }

        private void DbInsert(SqlConnection connection = null)
        {
            if (DispatchEntry == null && Impound == null && AccountId.GetValueOrDefault() == 0)
            {
                if (DispatchEntry == null)
                {
                    if (_dispatchEntryId > 0)
                        throw new Exception("DispatchEntry is null... but _dispatchEntryId == " + _dispatchEntryId +
                            ", chances are you are trying to save an Invoice against a DispatchEntry that is deleted. (operation not allowed)");

                    throw new Exception("DispatchEntryId on Invoice is null.. companyId=" + this.CompanyId + ", dispatchEntryId=" + _dispatchEntryId);
                }

                if (Impound == null)
                    throw new Exception("Impound on Invoice is null");
            }

            if (connection == null)
            {
                Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                    "InvoicesInsert",
                    new SqlParameter("@CompanyId", CompanyId),
                    new SqlParameter("@DispatchEntryId", (DispatchEntry != null ? (int?)DispatchEntry.Id : null)),
                    new SqlParameter("@ImpoundId", (Impound != null ? (int?)Impound.Id : null)),
                    new SqlParameter("@SubTotal", null), // subtotal/tax should be null on initial insert because there cannot be any tax/total calculated yet, since no invoiceItems could have been associated with this.
                    new SqlParameter("@Tax", null),
                    new SqlParameter("@AssetId", null),
                    new SqlParameter("@AccountId", AccountId),
                    new SqlParameter("@IsTaxExempt", IsTaxExempt)));
            }
            else
            {
                Id = Convert.ToInt32(SqlHelper.ExecuteScalar(connection,
                  "InvoicesInsert",
                  new SqlParameter("@CompanyId", CompanyId),
                  new SqlParameter("@DispatchEntryId", (DispatchEntry != null ? (int?)DispatchEntry.Id : null)),
                  new SqlParameter("@ImpoundId", (Impound != null ? (int?)Impound.Id : null)),
                  new SqlParameter("@SubTotal", 0), // subtotal/tax should be null on initial insert because there cannot be any tax/total calculated yet, since no invoiceItems could have been associated with this.
                  new SqlParameter("@Tax", 0),
                  new SqlParameter("@AssetId", null),
                  new SqlParameter("@AccountId", AccountId),
                  new SqlParameter("@IsTaxExempt", IsTaxExempt)));
            }
        }

        private void DbUpdate(bool clearCache, SqlConnection connection = null)
        {
            if (connection == null)
            {
                SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                    "InvoicesUpdateById",
                    new SqlParameter("@InvoiceId", Id),
                    new SqlParameter("@CompanyId", CompanyId),
                    new SqlParameter("@DispatchEntryId", (DispatchEntry != null ? (int?)DispatchEntry.Id : null)),
                    new SqlParameter("@ImpoundId", (Impound != null ? (int?)Impound.Id : null)),
                    new SqlParameter("@SubTotal", (clearCache ? null : (decimal?)Subtotal)),
                    new SqlParameter("@Tax", (clearCache ? null : (decimal?)Tax)),
                    new SqlParameter("@AssetId", null),
                    new SqlParameter("@AccountId", AccountId),
                    new SqlParameter("@IsTaxExempt", IsTaxExempt));
            }
            else
            {
                SqlHelper.ExecuteNonQuery(connection,
                    "InvoicesUpdateById",
                    new SqlParameter("@InvoiceId", Id),
                    new SqlParameter("@CompanyId", CompanyId),
                    new SqlParameter("@DispatchEntryId", (DispatchEntry != null ? (int?)DispatchEntry.Id : null)),
                    new SqlParameter("@ImpoundId", (Impound != null ? (int?)Impound.Id : null)),
                    new SqlParameter("@SubTotal", Subtotal),
                    new SqlParameter("@Tax", Tax),
                    new SqlParameter("@AssetId", null),
                    new SqlParameter("@AccountId", AccountId),
                    new SqlParameter("@IsTaxExempt", IsTaxExempt),
                    new SqlParameter("@HiddenTotal", HiddenTotal),
                    new SqlParameter("@TaxableTotal", TaxableTotal),
                    new SqlParameter("@TicketValue", TicketValue),
                    new SqlParameter("@NextScheduledRecalculate", NextScheduledRecalculate),
                    new SqlParameter("@LastUpdated", LastUpdated));
            }
        }

        public static async Task<Collection<Invoice>> GetByAccountIdForEntrylessInvoicesAsync(int accountId, 
            int[] companyIds, 
            bool ignorePaidInvoices = false, 
            DateTime? endDate = null, 
            bool ignoreInvoiesWithCalculatedTotals = false)
        {
            Collection<Invoice> invoices = new Collection<Invoice>();
            using (var c = Core.GetConnection())
            {
                var rs = c.QueryMultiple(
                    "InvoicesGetByAccountIdWithNullDispatchEntryIds", new
                    {
                        @AccountId = accountId,
                        @CompanyId = String.Join(", ", companyIds),
                        @IgnorePaidInvoices = ignorePaidInvoices,
                        @EndDate = endDate
                    }, commandType: CommandType.StoredProcedure);

                 invoices = rs.Read<dynamic>().Select<dynamic, Invoice>(o => Map(o)).ToCollection();
            }
            var invoiceItems = await InvoiceItem.GetByInvoiceIdAsync(invoices.Select(o => o.Id).ToArray());

            foreach (var invoice in invoices)
            {
                invoice._invoiceItems = invoiceItems.Where(o => o.InvoiceId == invoice.Id).ToCollection();
            }
            // To prime cache
            var entries = await Entry.GetByIdsAsync(invoices.Select(o => o._dispatchEntryId).ToArray(), invoices);

            return invoices;
        }



        /// <summary>
        /// Retrieve all invoices assigned to a specific account. 
        /// </summary>
        /// <param name="accountId">Account to return Invoices for</param>
        /// <param name="companyId">CompanyId to return invoices for; an account can be shared by multiple companies, thus this is required.</param>
        /// <returns></returns>

        //used in AccountStatementCreate.aspx
        public static Collection<Invoice> GetByAccountId(int accountId, int companyId, bool ignorePaidInvoices = false)
        {
            return GetByAccountId(accountId, new int[] { companyId }, ignorePaidInvoices);
        }

        //used in AccountStatementCreate.aspx
        public static Collection<Invoice> GetByAccountId(int accountId, int[] companyIds, bool ignorePaidInvoices = false, DateTime? endDate = null, bool ignoreInvoiesWithCalculatedTotals = false)
        {
            SqlMapper.GridReader rs = null;
            var invoices = new Collection<Invoice>();

            using (var conn = Core.GetConnection())
            {
                rs = conn.QueryMultiple(
                    "InvoicesGetByAccountId", new
                    {
                        @AccountId = accountId,
                        @CompanyId = string.Join(", ", companyIds),
                        @IgnorePaidInvoices = ignorePaidInvoices,
                        @EndDate = endDate
                    }, commandType: CommandType.StoredProcedure);

                invoices = rs.Read<dynamic>().Select<dynamic, Invoice>(o => Map(o)).ToCollection();
            }
            var invoiceItems = InvoiceItem.GetByInvoiceId(invoices.Select(o => o.Id).ToArray());

            foreach (var i in invoices)
            {
                i._invoiceItems = invoiceItems.Where(o => o.InvoiceId == i.Id).ToCollection();
            }

            var entries = Entry.GetByIds(invoices.Select(o => o._dispatchEntryId).ToArray(), invoices);

            return invoices;
        }

        public static async Task<Collection<Invoice>> GetByAccountIdAsync(int accountId, int[] companyIds, bool ignorePaidInvoices = false, DateTime? endDate = null, bool ignoreInvoiesWithCalculatedTotals = false)
        {
            SqlMapper.GridReader rs = null;
            var invoices = new Collection<Invoice>();

            using (var conn = Core.GetConnection())
            {
                rs = conn.QueryMultiple(
                    "InvoicesGetByAccountId", new
                    {
                        @AccountId = accountId,
                        @CompanyId = string.Join(", ", companyIds),
                        @IgnorePaidInvoices = ignorePaidInvoices,
                        @EndDate = endDate
                    }, commandType: CommandType.StoredProcedure);

                invoices = rs.Read<dynamic>().Select<dynamic, Invoice>(o => Map(o)).ToCollection();
            }
            var invoiceItems = await InvoiceItem.GetByInvoiceIdAsync(invoices.Select(o => o.Id).ToArray());

            foreach (var i in invoices)
            {
                i._invoiceItems = invoiceItems.Where(o => o.InvoiceId == i.Id).ToCollection();
            }

            var entries = await Entry.GetByIdsAsync(invoices.Select(o => o._dispatchEntryId).ToArray(), invoices);

            return invoices;
        }

        public static Collection<Invoice> AgingReportGetByAccountId(int[] accountId, int[] companyIds, bool ignorePaidInvoices = false, DateTime? endDate = null, bool ignoreInvoiesWithCalculatedTotals = false, int? accountManagerUserId = null)
        {
            var rv = new List<Invoice>();

            foreach (var accBatch in accountId.Batch(50))
            {
                var invoices = new Collection<Invoice>();
                using (var c = Core.GetConnection())
                {
                    var rs = c.QueryMultiple(
                        "InvoicesGetByAccountId2", new
                        {
                            @AccountId = string.Join(",", accBatch),
                            @CompanyId = String.Join(", ", companyIds),
                            @IgnorePaidInvoices = ignorePaidInvoices,
                            @EndDate = endDate,
                            @AccountManagerUserId = accountManagerUserId
                        }, commandType: CommandType.StoredProcedure, commandTimeout: 60 * 1000);

                     invoices = rs.Read<dynamic>().Select<dynamic, Invoice>(o => Map(o)).ToCollection();
                }
                var invoiceItems = InvoiceItem.GetByInvoiceId(invoices.Select(o => o.Id).ToArray());

                foreach (var invoice in invoices)
                {
                    invoice._invoiceItems = invoiceItems.Where(o => o.InvoiceId == invoice.Id).ToCollection();
                }
                rv.AddRange(invoices);
            }
            return rv.ToCollection();
        }


        /// <summary>
        /// used to get opening balances, invoices that arent linked to calls
        /// </summary>
        /// <param name="accountId"></param>
        /// <param name="companyIds"></param>
        /// <returns></returns>
        public static async Task<Collection<Invoice>> GetByAccountIdForNullDispatchEntriesAsync(int accountId, int[] companyIds)
        {
            var invoices = new Collection<Invoice>();
            using (var c = Core.GetConnection())
            {
                var rs = c.QueryMultiple(
                    "InvoicesGetByAccountIdWithNoDispatchEntryId", new
                    {
                        @AccountId = accountId,
                        @CompanyId = String.Join(", ", companyIds)
                    }, commandType: CommandType.StoredProcedure);

                invoices = rs.Read<dynamic>().Select<dynamic, Invoice>(o => Map(o)).ToCollection();
            }
            
            var invoiceItems = await InvoiceItem.GetByInvoiceIdAsync(invoices.Select(o => o.Id).ToArray());

            foreach (var invoice in invoices)
            {
                invoice._invoiceItems = invoiceItems.Where(o => o.InvoiceId == invoice.Id).ToCollection();
            }

            return invoices;
        }

        public async Task UpdatePaymentTotal()
        {
            if (this.Id > 0)
                await UpdatePaymentTotal(this.Id, this);
        }

        public static async Task UpdatePaymentTotal(int invoiceId, Invoice invoice = null, ConcurrentBag<Task> bulkTasks = null)
        {
            if (invoiceId < 1)
                return;

            if (invoice == null)
                invoice = await Invoice.GetByIdAsync(invoiceId);

            if (invoice != null)
            {
                invoice.ResetCache();

                SqlMapper.ExecuteSP("InvoicesUpdatePaymentTotalById", new
                {
                    @InvoiceId = invoiceId,
                });
                
                await Entry.UpdateInvoiceStatusIdInternal(invoice.Id, false, bulkTasks);
                await Entry.UpdateInAzure(Entry.GetByIdNoCache(invoice.DispatchEntryId), false, bulkTasks);
            }
        }

        public static Collection<Invoice> GetByPaymentRange(int[] companyIds, DateTime startDate, DateTime endDate, int? type = null)
        {
            Collection<Invoice> invoices = new Collection<Invoice>();
            Collection<InvoiceItem> invoiceItems = new Collection<InvoiceItem>();
            Collection<InvoiceItemTax> invoiceItemTax = new Collection<InvoiceItemTax>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "InvoicesGetByPaymentRange",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate),
                new SqlParameter("@Type", type)))
            {
                while (dr.Read())
                {
                    invoices.Add(new Invoice(dr));
                }
                dr.NextResult();

                while (dr.Read())
                {
                    invoiceItems.Add(new InvoiceItem(dr));
                }

                dr.NextResult();

                while (dr.Read())
                {
                    invoiceItemTax.Add(new InvoiceItemTax(dr));
                }
            }
            foreach (var ii in invoiceItems)
            {
                ii.TaxRates = invoiceItemTax.Where(w => w.InvoiceItemId == ii.Id).ToCollection();
            }

            foreach (var get in invoices)
            {
                get._invoiceItems = invoiceItems.Where(w => w.InvoiceId == get.Id).ToCollection();

                if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                {
                    get.ForceRecalculate();
                }
            }

           
            return invoices;
        }

        public static async Task<Collection<Invoice>> GetByPaymentRangeAsync(int[] companyIds, DateTime startDate, DateTime endDate, int? type = null)
        {
            Collection<Invoice> invoices = new Collection<Invoice>();
            Collection<InvoiceItem> invoiceItems = new Collection<InvoiceItem>();
            Collection<InvoiceItemTax> invoiceItemTax = new Collection<InvoiceItemTax>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "InvoicesGetByPaymentRange",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate),
                new SqlParameter("@Type", type)))
            {
                while (await dr.ReadAsync())
                {
                    invoices.Add(new Invoice(dr));
                }
                await dr.NextResultAsync();

                while (await dr.ReadAsync())
                {
                    invoiceItems.Add(new InvoiceItem(dr));
                }

                await dr.NextResultAsync();

                while (await dr.ReadAsync())
                {
                    invoiceItemTax.Add(new InvoiceItemTax(dr));
                }
            }
            foreach (var ii in invoiceItems)
            {
                ii.TaxRates = invoiceItemTax.Where(w => w.InvoiceItemId == ii.Id).ToCollection();
            }

            foreach (var get in invoices)
            {
                get._invoiceItems = invoiceItems.Where(w => w.InvoiceId == get.Id).ToCollection();

                if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                {
                    get.ForceRecalculate();
                }
            }

           
            return invoices;
        }

        public decimal GetTicketValue(int? driverId, EntryCommissionDriver ecd = null)
        {
            if (driverId == null)
                return TicketValue;

            // verify the driver is valid
            var driver = Driver.GetById(driverId.Value);
            if (driver == null)
                throw new TowbookException("DriverId " + driverId + " doesn't exist");

            #region Ensure that driver is associated with the Entry.
            var callDrivers = DispatchEntry.Drivers;

            if (!callDrivers.Contains(driverId.Value))
            {
                // if the driver isn't associated with this call in any way whatsoever, then throw exception.
                throw new TowbookException("DriverId " + driverId + " does not belong to invoice " + Id);
            }
            #endregion

            if (ecd == null)
                ecd = EntryCommissionDriver.GetByDispatchEntryId(DispatchEntry.Id).FirstOrDefault(s => s.DriverId == driverId);


            if (ecd != null)
            {
                if (ecd.Type == Commissions.CommissionType.FlatRate)
                    return ecd.FlatAmount;
                else if (ecd.Type == Commissions.CommissionType.Percentage)
                    return decimal.Round(TicketValue * (ecd.Percentage / 100), 2, MidpointRounding.AwayFromZero);
            }

            return TicketValue;
        }

        public TaxRate GetTaxRate()
        {
            var e = DispatchEntry;

            if (e == null || IsTaxExempt)
                return null;

            if (e.Attributes != null)
            {
                var a = e.Attributes.FirstOrDefault(w => w.Key == AttributeValue.BUILTIN_TAXRATE_OVERRIDE);
                if (a.Value != null)
                {
                    if (int.TryParse(a.Value.Value, out int av))
                    {
                        return TaxRate.GetById(av);
                    }
                }
            }

            var defaultTaxRateId = CompanyKeyValue.GetByCompanyId(e.CompanyId, Provider.Towbook.ProviderId, "DefaultTaxRateId").FirstOrDefault();
            if (defaultTaxRateId != null && int.TryParse(defaultTaxRateId.Value, out int nDefaultTaxRate))
            {
                var taxRateItem = e.Company?.TaxRates?.FirstOrDefault(f => f.Id == nDefaultTaxRate) ??
                    TaxRate.GetById(nDefaultTaxRate);

                if (taxRateItem != null)
                    return taxRateItem;
            }

            if (e.Impound && Impound?.Lot?.DefaultTaxRateId > 0)
            {
                var taxRateItem = e.Company?.TaxRates.FirstOrDefault(f => f.Id == Impound.Lot.DefaultTaxRateId) ??
                    TaxRate.GetById(Impound.Lot.DefaultTaxRateId);

                if (taxRateItem != null)
                    return taxRateItem;
            }

            if (e.Company?.TaxMode == Company.Company.TaxModeEnum.Single)
            {
                if (DispatchEntry.Company.TaxRates?.Count == 1)
                {
                    return e.Company.TaxRates[0];
                }
            }
            else if (e.Company?.TaxMode == Company.Company.TaxModeEnum.Multiple)
            {
                if (e?.Company?.TaxRates != null)
                {
                    return e.Company.TaxRates.OrderBy(o => o.Description).FirstOrDefault();
                }
            }

            return e.Company?.TaxRates.FirstOrDefault();
        }

        public async Task<TaxRate> GetTaxRateAsync()
        {
            var e = DispatchEntry;

            if (e == null || IsTaxExempt)
                return null;

            if (e.Attributes != null)
            {
                var a = e.Attributes.FirstOrDefault(w => w.Key == AttributeValue.BUILTIN_TAXRATE_OVERRIDE);
                if (a.Value != null)
                {
                    if (int.TryParse(a.Value.Value, out int av))
                    {
                        return await TaxRate.GetByIdAsync(av);
                    }
                }
            }

            var defaultTaxRateId = (await CompanyKeyValue.GetByCompanyIdAsync(e.CompanyId, Provider.Towbook.ProviderId, "DefaultTaxRateId")).FirstOrDefault();
            if (defaultTaxRateId != null && int.TryParse(defaultTaxRateId.Value, out int nDefaultTaxRate))
            {
                var taxRateItem = e.Company?.TaxRates?.FirstOrDefault(f => f.Id == nDefaultTaxRate) ??
                    await TaxRate.GetByIdAsync(nDefaultTaxRate);

                if (taxRateItem != null)
                    return taxRateItem;
            }

            if (e.Impound && Impound?.Lot?.DefaultTaxRateId > 0)
            {
                var taxRateItem = e.Company?.TaxRates.FirstOrDefault(f => f.Id == Impound.Lot.DefaultTaxRateId) ??
                    await TaxRate.GetByIdAsync(Impound.Lot.DefaultTaxRateId);

                if (taxRateItem != null)
                    return taxRateItem;
            }

            if (e.Company?.TaxMode == Company.Company.TaxModeEnum.Single)
            {
                if (DispatchEntry.Company.TaxRates?.Count == 1)
                {
                    return e.Company.TaxRates[0];
                }
            }
            else if (e.Company?.TaxMode == Company.Company.TaxModeEnum.Multiple)
            {
                if (e?.Company?.TaxRates != null)
                {
                    return e.Company.TaxRates.OrderBy(o => o.Description).FirstOrDefault();
                }
            }

            return e.Company?.TaxRates.FirstOrDefault();
        }

        public static async Task<object> ResolveBalanceToZero(int[] dispatchEntryIds, AuthenticationToken token, string ipAddress)
        {
            var invoices = await GetByDispatchEntriesAsync(dispatchEntryIds.ToCollection());
            Collection<object> resultData = new Collection<object>();
            var rateItem = await RateItem.GetByIdAsync(3); // the internal rateItem we share for everyone

            foreach (var inv in invoices)
            {
                var taxOptionString = inv.IsTaxExempt ? " (tax exempt)" 
                    : inv.InvoiceItems.Where(w => !w.RateItem.IsFuelSurchargeItem() 
                                                            && !w.RateItem.IsDiscountItem() 
                                                            && !w.RateItem.IsAdjustmentToZeroItem()
                                                        ).All(a => a.Taxable) ? " (tax)" 
                    : inv.InvoiceItems.Where(w => !w.RateItem.IsFuelSurchargeItem() 
                                                    && !w.RateItem.IsDiscountItem() 
                                                    && !w.RateItem.IsAdjustmentToZeroItem()
                                            ).Any(a => a.Taxable) ? " (tax exempt)" 
                    
                    : ""; 

                if(inv.BalanceDue > 0)
                {
                    var balanceDue = inv.BalanceDue;
                    var adjustedAmount = (0 - balanceDue);

                    // create new invoice item
                    var ii = new InvoiceItem();

                    // amount to adjust invoice to a balance due of zero
                    ii.CustomPrice = adjustedAmount;
                    ii.CustomName = rateItem.Name + taxOptionString;
                    ii.InvoiceId = inv.Id;
                    ii.Quantity = 1;
                    ii.Taxable = false;
                    ii.RateItem = rateItem;
                    ii.Hidden = false;
                    inv.InvoiceItems.Add(ii);
                    await inv.SaveAsync(token, ipAddress);

                    // form return object
                    dynamic result = new ExpandoObject();
                    result.InvoiceId = inv.Id;
                    result.InvoiceItem = ii;
                    result.AdjustedAmount = adjustedAmount;
                    result.BalanceDue = inv.BalanceDue;

                    // add it to the collection
                    resultData.Add(result);
                }
            }

            return resultData;
        }

        internal static IEnumerable<Invoice> GetByDispatchEntries(Collection<int> dispatchEntryIds)
        {
            var invoices = new Collection<Invoice>();
            var invoiceItems = new Collection<InvoiceItem>();
            var invoiceItemTax = new Collection<InvoiceItemTax>();
            var invoiceCommissionReturn = new Collection<InvoiceItem.InvoiceItemDriverCommission>();

            foreach (var batch in dispatchEntryIds.OrderBy(o => o).Batch(500))
            {
                string ids = string.Join(",", batch);

                using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "InvoicesGetByDispatchEntryIdArray",
                    new SqlParameter("@DispatchEntryIds", ids)))
                {
                    while (dr.Read())
                    {
                        invoices.Add(new Invoice(dr));
                    }
                    dr.NextResult();

                    while (dr.Read())
                    {
                        invoiceItems.Add(new InvoiceItem(dr));
                    }

                    dr.NextResult();

                    while (dr.Read())
                    {
                        invoiceItemTax.Add(new InvoiceItemTax(dr));
                    }

                    invoiceCommissionReturn = invoiceCommissionReturn.Union(
                        InvoiceItem.InvoiceItemDriverCommission.GetFromExistingReader(dr)).ToCollection();
                }
            }

            foreach (var ii in invoiceItems)
            {
                ii.TaxRates = invoiceItemTax.Where(w => w.InvoiceItemId == ii.Id).ToCollection();
                ii.MarkAsClean("Tax Rates");

                foreach (var cc in invoiceCommissionReturn.GroupBy(o => o.InvoiceItemId))
                {
                    if (cc.Key == ii.Id)
                    {
                        ii.DriverCommissions = cc.ToCollection();
                    }
                }
            }

            foreach (var get in invoices)
            {
                get._invoiceItems = invoiceItems.Where(w => w.InvoiceId == get.Id).ToCollection();

                if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                {
                    get.ForceRecalculate(true);
                }
            }
            
            return invoices;
        }

        internal static async Task<IEnumerable<Invoice>> GetByDispatchEntriesAsync(Collection<int> dispatchEntryIds)
        {
            var invoices = new Collection<Invoice>();
            var invoiceItems = new Collection<InvoiceItem>();
            var invoiceItemTax = new Collection<InvoiceItemTax>();
            var invoiceCommissionReturn = new Collection<InvoiceItem.InvoiceItemDriverCommission>();

            foreach (var batch in dispatchEntryIds.OrderBy(o => o).Batch(500))
            {
                string ids = string.Join(",", batch);

                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    "InvoicesGetByDispatchEntryIdArray",
                    new SqlParameter("@DispatchEntryIds", ids)))
                {
                    while (await dr.ReadAsync())
                    {
                        invoices.Add(new Invoice(dr));
                    }
                    await dr.NextResultAsync();

                    while (await dr.ReadAsync())
                    {
                        invoiceItems.Add(new InvoiceItem(dr));
                    }

                    await dr.NextResultAsync();

                    while (await dr.ReadAsync())
                    {
                        invoiceItemTax.Add(new InvoiceItemTax(dr));
                    }

                    invoiceCommissionReturn = invoiceCommissionReturn.Union(
                        InvoiceItem.InvoiceItemDriverCommission.GetFromExistingReader(dr)).ToCollection();
                }
            }

            foreach (var ii in invoiceItems)
            {
                ii.TaxRates = invoiceItemTax.Where(w => w.InvoiceItemId == ii.Id).ToCollection();
                ii.MarkAsClean("Tax Rates");

                foreach (var cc in invoiceCommissionReturn.GroupBy(o => o.InvoiceItemId))
                {
                    if (cc.Key == ii.Id)
                    {
                        ii.DriverCommissions = cc.ToCollection();
                    }
                }
            }

            foreach (var get in invoices)
            {
                get._invoiceItems = invoiceItems.Where(w => w.InvoiceId == get.Id).ToCollection();

                if (get.NextScheduledRecalculate != null && get.NextScheduledRecalculate < DateTime.Now)
                {
                    get.ForceRecalculate(true);
                }
            }
            
            return invoices;
        }

        public List<InvoiceItem> GetRegulatedCharges()
        {
            var rik = RateItemKey.GetByProviderId(Provider.Towbook.ProviderId, "RegulatedCharge");

            var regulated = RateItemKeyValue.GetByCompany(this.DispatchEntry.CompanyId, Provider.Towbook.ProviderId)
                .Where(o => o.KeyId == rik.Id && o.Value == "1")
                .Select(o => o.RateItemId)
                .ToList();

            return this.DispatchEntry.InvoiceItems.Where(o => o.RateItem != null && regulated.Contains(o.RateItem.RateItemId)).ToList();
        }
    }
}
