using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using Extric.Towbook.Integration;
using System.Threading.Tasks;
using System.Collections;
using System.Collections.Generic;

namespace Extric.Towbook.Integrations.Email
{
    /// <summary>
    /// Represents a Towbook managed email account that Towbook will accept emails for.
    /// </summary>
    [Table("Email.EmailAddresses")]
    public class EmailAddress : IUsesSqlKey
    {
        private const string cacheFormat = "f-fn-{0}";

        private string address; 

        public int Id { get; protected set; }
        /// <summary>
        /// The actual email address (<EMAIL>)
        /// </summary>
        [Key("EmailAddress")]
        public string Address
        {
            get { return address; }
            set { address = (value ?? "").Trim(); }
        }

        public int CompanyId { get; set; } 
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public bool Deleted { get; set; }
        public int DomainId { get; set; }

        public EmailAddress()
        {

        }
        public override string ToString()
        {
            return Address;
        }

        public static EmailAddress GetById(int id)
        {
            return Map(SqlMapper.QuerySP<dynamic>("Email.EmailAddressesGetById",
                new { @EmailAddressId = id }).FirstOrDefault());
        }

        public static async Task<EmailAddress> GetByIdAsync(int id)
        {
            var result = await SqlMapper.QuerySpAsync<dynamic>(
                "Email.EmailAddressesGetById",
                new { EmailAddressId = id });

            return Map(result.FirstOrDefault());
        }


        public static Collection<EmailAddress> GetByCompanyId(int companyId)
        {
            return SqlMapper.QuerySP<dynamic>("Email.EmailAddressesGetByCompanyId",
                new { @CompanyId = companyId }).Select<dynamic, EmailAddress>(o => Map(o)).ToCollection();
        }
        
        public static async Task<Collection<EmailAddress>> GetByCompanyIdAsync(int companyId)
        {
            return (await SqlMapper.QuerySpAsync<dynamic>("Email.EmailAddressesGetByCompanyId",
                new { @CompanyId = companyId })).Select<dynamic, EmailAddress>(o => Map(o)).ToCollection();
        }

        public static async Task<Collection<EmailAddress>> GetByCompanyIdsAsync(int[] companyIds)
        {
            var addresses = new Collection<EmailAddress>();

            if (companyIds.Count() == 0)
                return addresses;

            foreach (var batch in companyIds.Batch(500))
            {
                addresses = addresses.Union(await SqlMapper.QueryAsync<EmailAddress>(
                     "SELECT * FROM Email.EmailAddresses EA WITH (NOLOCK) WHERE (CompanyId in @CompanyIds) and deleted=0",
                     new { CompanyIds = batch })).ToCollection();
            }

            return addresses;
        }

        public static async Task<IEnumerable<EmailAddress>> GetAllAsync()
        {
            return await SqlMapper.QueryAsync<EmailAddress>(
                 "SELECT CompanyId, EmailAddress FROM Email.EmailAddresses EA WITH (NOLOCK) WHERE deleted=@Deleted", new { Deleted = 0 });
        }

        /// <summary>
        /// Retrieves a EmailAddress object, by a regular 10 or 11 digit emailAddress. 
        /// </summary>
        /// <param name="emailAddress"></param>
        /// <returns></returns>
        public static EmailAddress GetByEmailAddress(string emailAddress)
        {
            if (emailAddress == null)
                return null;

            emailAddress = emailAddress.ToLowerInvariant();

            if (emailAddress.Contains("@towbooks.net"))
                emailAddress = emailAddress.Replace("@towbooks.net", "@towbook.net");

            var r = Map(SqlMapper.QuerySP<dynamic>("Email.EmailAddressesGetByEmailAddress",
                new { @EmailAddress = emailAddress }).FirstOrDefault());

            if (r == null)
            {
                if (emailAddress.StartsWith("company-") && emailAddress.EndsWith("@towbook.net"))
                {
                    var cId = GetCompanyIdFromTowbookDotNetEmailAddress(emailAddress);

                    var c = Company.Company.GetById(cId);

                    if (c != null)
                    {
                        return new EmailAddress()
                        {
                            CompanyId = c.Id,
                            Address = emailAddress,
                            CreateDate = c.CreateDate,
                            DomainId = 1,
                            Deleted = false,
                            Id = 0,
                            OwnerUserId = 1,
                        };
                    }
                }
            }
            return r;
        }


        /// <summary>
        /// Returns the email address for the specified companyId. Format will be like: <EMAIL>
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns><EMAIL> the x's represent an encoded version of the companyId that equals 16 digits.</returns>
        public static string GetTowbookDotNetEmailAddressForCompany(int companyId)
        {
            var startingPoint = new ZBase32Encoder().Encode(System.Text.Encoding.UTF8.GetBytes(companyId.ToString().PadLeft(10, '0')));

            var e = "company-" + startingPoint + "@towbook.net";

            if (startingPoint.Length != 16)
                throw new TowbookException("CompanyId hash doesn't equal 16 characters... companyId=" +
                    companyId + ", hash=" + startingPoint);


            // this is to allow a company to ALWAYS use the generated address from above so that their custom towbook.net address
            // doesn't get used as the sending address. this allows the company to receive replies to the address set in their 
            // company profile (Company.Email)
            var forceSendFromGeneratedEmail = CompanyKeyValue.GetByCompanyId(companyId, Provider.Towbook.ProviderId,
                "ForceSendEmailsFromGeneratedEmailAddress").FirstOrDefault()?.Value == "1";

            if (!forceSendFromGeneratedEmail)
            {
                var realEmail = GetByCompanyId(companyId).Where(o => o.Deleted == false).FirstOrDefault();

                if (realEmail != null)
                    return realEmail.Address;
            }

            return e;
        }

        public static string GetPrimaryTowbookEmailAddress(int companyId)
        {
            var realEmail = GetByCompanyId(companyId).Where(o => o.Deleted == false).FirstOrDefault();

            if (realEmail != null)
                return realEmail.Address;

            return null;
        }

        /// <summary>
        /// Returns the email address for the specified companyId. Format will be like: <EMAIL>
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns><EMAIL> the x's represent an encoded version of the companyId that equals 16 digits.</returns>
        public static async Task<string> GetPrimaryTowbookEmailAddressAsync(int companyId)
        {
            var realEmail = (await GetByCompanyIdAsync(companyId)).Where(o => o.Deleted == false).FirstOrDefault();

            if (realEmail != null)
                return realEmail.Address;

            return null;
        }


        /// <summary>
        /// Retrieves the companyId <NAME_EMAIL> email address, as generated by 
        /// the GetTowbookDotNetEmailAddressForCompany method.
        /// </summary>
        /// <param name="email"></param>
        /// <returns>companyId of the email address, or 0. If you pass in a null email address, it will return 0.</returns>
        public static int GetCompanyIdFromTowbookDotNetEmailAddress(string email)
        {
            if (email == null)
                return 0;

            var input = email.Replace("company-", "").Replace("@towbook.net", "");

            if (input.Length == 16)
                return Convert.ToInt32(System.Text.Encoding.UTF8.GetString(new ZBase32Encoder().Decode(input)));

            return 0;
        }

        protected static EmailAddress Map(dynamic row)
        {
            if (row == null)
                return null;

            return new EmailAddress()
            {
                Id = row.EmailAddressId,
                Address = row.EmailAddress,
                DomainId = row.EmailDomainId,
                CompanyId = row.CompanyId,
                CreateDate = row.CreateDate,
                OwnerUserId = row.OwnerUserId,
                Deleted = row.Deleted
            };
        }

        public void Save()
        {
            if (this.Id == 0)
            {
                var d = SqlMapper.QuerySP<dynamic>("Email.EmailAddressesInsert",
                    new
                    {
                        @EmailAddress = Address,
                        @EmailDomainId = DomainId,
                        @CompanyId = CompanyId,
                        @OwnerUserId = OwnerUserId,
                    }).FirstOrDefault();

                this.Id = d.Id;
                this.CreateDate = d.CreateDate;
            }
            else
            {
                SqlMapper.ExecuteSP("Email.EmailAddressesUpdateById",
                    new
                    {
                        @EmailAddressId = this.Id,
                        @EmailDomainId = DomainId,
                        @EmailAddress = Address,
                        @CompanyId = CompanyId,
                        @OwnerUserId = OwnerUserId
                    });
            }

            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, this.Id));
        }

        public void Delete()
        {
            SqlMapper.ExecuteSP("Email.EmailAddressesDeleteById",
                new
                {
                    @EmailAddressId = this.Id
                });
            this.Deleted = true;
            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, this.Id));
        }

    }
}
