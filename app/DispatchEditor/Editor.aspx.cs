using System;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.WebShared;
using Extric.Towbook.Utility;
using Extric.Towbook.Integration;
using System.Linq;
using Extric.Towbook.Generated;
using System.Web;
using Extric.Towbook.Dispatch.QuoteModels;
using System.Threading.Tasks;
using System.Web.UI;
using Extric.Towbook.API.Models.Quotes;

public partial class DispatchEditor : System.Web.UI.Page
{
    private int id;

    protected int Id { get { return id; } }
    protected int AccountId { get; set; }
    protected string AccountAddress { get; set; }
    protected decimal? AccountLatitude { get; set; }
    protected decimal? AccountLongitude { get; set; }

    protected CallModel Call { get; set; }
    protected QuoteModel Quote { get; set; }

    private int impoundId;
    protected int ImpoundId { get { return impoundId; } }
    protected int DefaultTaxRateId { get; private set; }
    protected bool HideCharges { get; set; }
    protected bool HasHourlyStorage {  get { return Global.CurrentUser.Company.HasFeature(Features.Impounds_HourlyStorage); } }
    protected bool HasAutoMileage { get { return Global.CurrentUser.Company.HasFeature(Features.GPS_AutomaticMileage); } }
    protected bool HasRoadside { get { return Global.CurrentUser.Company.HasFeature(Features.Roadside); } }
    protected int[] allowedRoadsideMotorClubAccounts = new int[] { };

    protected int[] alwaysIncludeDeadheadMileageLineItemAccounts = new int[] { };
    protected bool AllowAdHocPricingItems { get { return HasPermissionToCreateAdHocPricingItems(); } }
    protected bool BlockCommissionsOnAdHocItems { get { return BlockCommissionOnAdHocPricingItems(); } }
    protected bool LimitPricingtoAccountRateItems { get { return HasLimitPricingOptionsToAccountPricing(); } }
    protected bool AllowPurchaseOrderNumberChangeOnLockedCalls { get { return HasPermissionToChangePurchaseOrderNumber(); } }
    protected bool ForceIncludeUnitNumberForAllAssetTypes { get { return AlwaysIncludeUnitNumberForAllAssetTypes(); } }

    private bool _autoFillMiles;
    public bool AutomaticallyFillInMiles { get { return _autoFillMiles; } }
    public bool RoadsideOnly {get; set;}

    public string EditorName { get; set; }
    public string EditorAction { get; set; }

    public string PermitStatusesJson { get; set; }
    
    protected bool HasQuotes { get { return Global.CurrentUser.Company.HasFeature(Features.Dispatching_Quotes); } }
    protected bool HasPermissionToRemovePoliceHolds {  get { return WebGlobal.CurrentUser.HasPermissionToRemovePoliceHolds(); } }
    protected int QuoteNumber { get; set; }
    protected string QuoteJson { get; set; }

    protected string UserJson { get { return GetCurrentUser(); } }

    public bool HasImpoundOnlyEnabled { get; set; }
    protected bool hasInvoiceItemNotesFeature = false;

    protected void Page_Load(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(GetCallAsync));
    }

    private int statusCode = 0;
    protected string GetCurrentUser()
    {
        return WebGlobal.GetResponseFromUrl("/api/user", out statusCode);
    }

    protected string GetQuote(Guid id, bool? duplicate = false)
    {
        int statusCode = 0;
        string json = null;

        if(duplicate.GetValueOrDefault()) 
            json = WebGlobal.GetResponseFromUrl("/api/quotes/" + id + "/duplicate", out statusCode);
        else
            json = WebGlobal.GetResponseFromUrl("/api/quotes/" + id, out statusCode);

        if (json.Equals("[]"))
            throw new HttpException(404, "The quote cannot be found or you don't have permission to view it.");

        return json;
    }

    private static bool HasPermissionToCreateAdHocPricingItems()
    {
        var kv = CompanyKeyValue.GetByCompanyId(Global.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "PreventAdHocInvoiceItems").FirstOrDefault();
        if (kv != null && kv.Value == "1")
            return false;
        else
            return true;
    }

    private static bool BlockCommissionOnAdHocPricingItems()
    {
        var kv = CompanyKeyValue.GetByCompanyId(Global.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "BlockCommissionOnAdHocInvoiceItems").FirstOrDefault();
        if (kv != null && kv.Value == "1")
            return true;
        else
            return false;
    }

    private static bool HasLimitPricingOptionsToAccountPricing()
    {
        var kv = CompanyKeyValue.GetByCompanyId(Global.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "LimitPricingOptionsInEditorToAccountPricingOnly").FirstOrDefault();
        if (kv != null && kv.Value == "1")
            return true;
        else
            return false;
    }

    private static bool HasPermissionToChangePurchaseOrderNumber()
    {
        var kv = CompanyKeyValue.GetByCompanyId(Global.CurrentUser.PrimaryCompanyId, Provider.Towbook.ProviderId, "AllowPurchaseOrderChangesOnLockedCalls").FirstOrDefault();
        if (kv != null && kv.Value == "1")
            return true;
        else
            return false;
    }

    private static bool AlwaysIncludeUnitNumberForAllAssetTypes()
    {
        var kv = CompanyKeyValue.GetByCompanyId(Global.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "ForceIncludeUnitNumberForAllAssetsTypes").FirstOrDefault();
        if (kv != null && kv.Value == "1")
            return true;
        else
            return false;
    }

    protected static bool DisablePreviousCallInfoSearchByPhoneNumber()
    {
        var kv = CompanyKeyValue.GetByCompanyId(Global.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "DisableFetchingPreviousCallByContactPhoneNumber").FirstOrDefault();
        if (kv != null && kv.Value == "1")
            return true;
        else
            return false;
    }

    private async Task GetCallAsync()
    {
        // 12 Days Christmas Check HasImpoundOnlyEnabled used in .aspx
        HasImpoundOnlyEnabled = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("impoundCustomFields");

        EditorName = "Call";
        EditorAction = "Create";
        this.Call = null;

        Int32.TryParse(Request.QueryString["id"], out id);

        int callNumber = 0;
        if (Int32.TryParse(Request.QueryString["c"], out callNumber))
        {
            var cen = await Extric.Towbook.Dispatch.Entry.GetByCallNumberAsync(callNumber, WebGlobal.CurrentUser.Company);
            if (cen != null)
            {
                Response.Redirect("/DispatchEditor/editor.aspx?id=" + cen.Id);
            }
        }

        hasInvoiceItemNotesFeature = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("invoice_item_notes");

        if (Request.QueryString["impoundId"] != null)
        {
            Int32.TryParse(Request.QueryString["impoundId"], out impoundId);
            EditorName = "Impound";
        }

        PermitStatusesJson = Extric.Towbook.Accounts.PermitStatus.GetAll().ToJson();

        QuoteJson = "{}";
        Guid quoteId = Guid.Empty;
        var isDuplicate = Request.QueryString["duplicate"] == "true";
        if (HasQuotes)
        {
            if (Request.QueryString["q"] != null)
            {

                if (Guid.TryParse(Request.QueryString["q"], out quoteId))
                {
                    EditorName = "Quote";
                    EditorAction = isDuplicate ? "Create" : "Update";
                    QuoteJson = GetQuote(quoteId, isDuplicate);

                    Quote = Newtonsoft.Json.JsonConvert.DeserializeObject<QuoteModel>(QuoteJson);
                    QuoteNumber = Quote.QuoteNumber;
                    Call = Quote.Call;
                    id = Call.Id;
                    AccountId = Call.Account.Id;
                }
            }
            else
            {
                QuoteNumber = 0;
                QuoteJson = (await QuoteModelExtension.MapAsync(new QuoteModel())).ToJson(true); // GetQuote(Guid.Empty);
            }
        }

        if (id == 0 && impoundId > 0)
        {
            var imp = await Extric.Towbook.Impounds.Impound.GetByIdAsync(impoundId);
            if (await WebGlobal.CurrentUser.HasAccessToCompanyAsync(imp.Company.Id))
                this.id = imp.DispatchEntry.Id;
        }

        if (Request.QueryString["accountId"] != null)
            AccountId = Convert.ToInt32(Request.QueryString["accountId"]);

        if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser)
        {
            AccountId = WebGlobal.CurrentUser.AccountId;

            AccountAddress = String.Empty;
            var account = await Extric.Towbook.Accounts.Account.GetByIdAsync(WebGlobal.CurrentUser.AccountId);
            if (account != null)
            {
                AccountAddress = account.FormatAddress();
                AccountLatitude = account.Latitude;
                AccountLongitude = account.Longitude;
            }
        }

        int newCompanyId = WebGlobal.CurrentUser.CompanyId;
        var newCompany = WebGlobal.CurrentUser.Company;

        if (Request.QueryString["__companyId"] != null)
        {
            newCompanyId = Convert.ToInt32(Request.QueryString["__companyId"]);

            if (await WebGlobal.CurrentUser.HasAccessToCompanyAsync(newCompanyId))
            {
                WebGlobal.OverrideCompanyForThisRequestOnly(newCompanyId, WebGlobal.CurrentUser);
                newCompany = await Extric.Towbook.Company.Company.GetByIdAsync(newCompanyId);
            }
        }

        if (new int[] { 4599, 4598, 4597, 4596 }.Contains(newCompanyId)) AccountId = 24596;

        if (Id > 0)
        {
            if (Request.QueryString["undelete"] == "1")
            {
                await Extric.Towbook.Dispatch.Entry.UndeleteById(new Extric.Towbook.AuthenticationToken()
                {
                    ClientVersionId = Extric.Towbook.Platform.ClientVersion.GetByGitHash("web-app",
                        Extric.Towbook.Platform.ClientVersionType.WebApp).Id,
                    UserId = WebGlobal.CurrentUser.Id,
                }, WebGlobal.GetRequestingIp(), Id);
            }

            if (Call == null)
            {
                var en = Extric.Towbook.Dispatch.Entry.GetById(id);

                if (en == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(en.CompanyId))
                {
                    Response.Write("You don't have access to this call.");
                    Response.End();
                    return;
                }

                //this.Call = new Extric.Towbook.API.Controllers.CallsController().Get(Id);
                var calljson = WebGlobal.GetResponseFromUrl("/api/calls/" + id);
                this.Call = Newtonsoft.Json.JsonConvert.DeserializeObject<CallModel>(calljson);

                if (this.Call.Status != null)
                {
                    if (this.Call.Status.Id == 252)
                        this.Call.Status.Id = 5;

                    if (this.Call.Status.Id == 253)
                        this.Call.Status.Id = 255;
                }

                newCompanyId = en.CompanyId;

                EditorAction = "Update";

                if (Request.QueryString["duplicate"] == "true")
                    EditorAction = "Duplicate";

                if (Request.QueryString["auction"] == "1")
                {
                    EditorAction = "Duplicate";

                    int nStat = 0;
                    var json = WebGlobal.GetResponseFromUrl("/api/calls/" + id + "/duplicate?type=auction", out nStat);

                    this.Call = Newtonsoft.Json.JsonConvert.DeserializeObject<CallModel>(json);
                }

            }
        }
        else
        {
            this.Call = null;
        }

        var serviceCallsOnly = (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "ServiceCallsOnly")).FirstOrDefault();

        if (serviceCallsOnly != null && serviceCallsOnly.Value == "1") RoadsideOnly = true;

        var hideChargesFromDispatcher = (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromViewingInvoiceItems")).FirstOrDefault();
        var hideChargesFromDriver = (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingInvoiceItems")).FirstOrDefault();

        if (hideChargesFromDispatcher != null && hideChargesFromDispatcher.Value == "1" && Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher) HideCharges = true;
        if (hideChargesFromDriver != null && hideChargesFromDriver.Value == "1" && Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver) HideCharges = true;
        if (Request.QueryString["hidecharges"] != null) HideCharges = true;

        HideCharges = false;

        var defaultTaxRateId = (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "DefaultTaxRateId")).FirstOrDefault();

        if (defaultTaxRateId != null)
        {
            DefaultTaxRateId = Convert.ToInt32(defaultTaxRateId.Value);
        }

        if (defaultTaxRateId == null)
        {
            var taxRates = await Extric.Towbook.TaxRate.GetByCompanyAsync(newCompany);
            if (taxRates != null && taxRates.Count() == 1)
            {
                var tr = taxRates.First();
                if (tr.CompanyId == newCompanyId)
                    DefaultTaxRateId = Convert.ToInt32(tr.Id);
            }
        }

        var fillMiles = CompanyKeyValue.GetByCompanyId(newCompanyId, Provider.Towbook.ProviderId, "Towbook_Calls_AutomaticallyAddMiles").FirstOrDefault();
        if (fillMiles != null && fillMiles.Value == "1")
        {
            _autoFillMiles = true;
            if (Call != null && Call.Attributes != null)
            {
                var av = Call.Attributes.Where(o => o.AttributeId == 51).FirstOrDefault();

                if (av != null && av.Value == "2")
                    _autoFillMiles = false;
            }

            if (AccountId > 1)
            {
                var akv = AccountKeyValue.GetFirstValueOrNull(newCompanyId, AccountId, Provider.Towbook.ProviderId, "AutomaticallyAddMiles");
                if (akv != null && akv != "0")
                {
                    _autoFillMiles = (akv == "1");
                }
            }
        }

        /*        if (Id > 0 && this.Call != null)
                {
                    this._elr = EntryLocationRequest.GetByCallId(Id);
                }
        */
        if (Request.QueryString["q"] == null && isDuplicate)
        {
            Call.Insights = null;
            Call.AvailableActions = Array.Empty<string>();
        }
    }
}
