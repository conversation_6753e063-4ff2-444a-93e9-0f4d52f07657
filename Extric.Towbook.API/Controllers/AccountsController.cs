using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.API.Models;
using Extric.Towbook.WebShared;
using Extric.Towbook.Accounts;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using Extric.Towbook.Utility;
using Extric.Towbook.Integration;
using Extric.Towbook.API.Models.MotorClubs;
using Extric.Towbook.Dispatch;
using Extric.Towbook.API.Models.Accounts;
using Newtonsoft.Json;
using System.Threading.Tasks;
using static Extric.Towbook.API.ApiUtility;
using System.IO;
using Extric.Towbook.Web;
using Extric.Towbook.Storage;
using Extric.Towbook.Integrations.MotorClubs.Swoop;
using Extric.Towbook.Integrations.Email;
using NLog;
using Extric.Towbook.Integrations.MotorClubs.Swoop.Model;

using Extric.Towbook.WebShared.Multipart;
using GeoCoordinatePortable;
using HttpContextHelper = Extric.Towbook.Web.HttpContext;


namespace Extric.Towbook.API.Controllers
{
    [Route("accounts")]
    public class AccountsController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private readonly IHttpClientFactory _clientFactory;

        public class AccountListModel
        {
            public int Id { get; set; }
            public string Company { get; set; }
            public string Address { get; set; }
            public string Phone { get; set; }
            public AccountType? Type { get; set; }
            public string Zone { get; set; }
            public decimal? Balance { get; set; }
            public decimal? CreditBalance { get; set; }
            public decimal? CreditLimit { get; set; }
            public string ContractorId { get; set; }
            public string GateCode { get; set; }
            public string Fax { get; set; }
            public string Email { get; set; }
            public string Notes { get; set; }
            public int[] Companies { get; set; }
            public bool Deleted { get; set; }
            public IEnumerable<int> Tags { get; set; }
            public DateTime CreateDate { get; set; }

            public static AccountListModel Map(AccountMinimalModel o) => o == null ? null :
                new AccountListModel()
                {
                    Id = o.Id,
                    Company = o.Name,
                    Address = o.Address.ToString(),
                    Balance = o.Balance,
                    ContractorId = o.ContractorId,
                    CreditBalance = o.CreditBalance,
                    CreditLimit = o.CreditLimit,
                    Email = o.Email,
                    Fax = o.Fax,
                    GateCode = o.PropertyGateCode,
                    Notes = o.AccountNotes,
                    Phone = o.Phone,
                    Tags = o.Tags,
                    Type = o.Type.Value,
                    Companies = o.Companies,
                    Deleted = o.Deleted,
                    CreateDate = o.CreateDate
                };

            public static AccountListModel Reduce(AccountListModel m, User currentUser)
            {
                if (m == null)
                    return m;

                var companyId = m.Companies?.FirstOrDefault() ?? currentUser.PrimaryCompanyId;

                if (currentUser.Type == Towbook.User.TypeEnum.Driver)
                {
                    var hideNotes = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Towbook.ProviderId, "HideAccountNotesFromDrivers");
                    if (hideNotes == "1")
                        m.Notes = "";

                    var hide = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Towbook.ProviderId, "HideAccountDetailsFromDrivers");

                    if ((hide == "1" || hide == "2" || hide == "3") && m != null)
                    {
                        m.Address = "";
                        m.Email = "";
                        m.Phone = "";
                        m.Notes = "";
                    }

                    if (hide == "2")
                    {
                        // show the Type as the Name.
                        m.Company = m.Type.ToString();
                    }

                    if (hide == "3")
                    {
                        // show the Type as the Name.
                        m.Company = "(Hidden)";
                        m.Address = "";
                        m.Email = "";
                        m.Phone = "";
                        m.Notes = "";
                        m.Notes = "";
                    }

                    if (hide == "4")
                    {
                        // show the Type as the Name.
                        m.Company = "(Hidden)";
                    }

                    m.Balance = null;
                    m.CreditBalance = null;
                    m.CreditLimit = null;
                }


                return m;
            }
        }


        public AccountsController(IHttpClientFactory clientFactory) {
            _clientFactory = clientFactory;
        }


        [HttpGet]
        [Route("list")]
        public async Task<IEnumerable<AccountListModel>> List()
        {
            var key = "accounts_list:" + string.Join(",",
                    (await this.GetCompaniesForRequestAsync()).Select(o => o.Id).OrderBy(ro => ro)
                );

            string al = null;

            al = Cache.SafeStringDecode(await Core.GetRedisValueAsByteArrayAsync(key));

            IEnumerable<AccountListModel> ret = null;
            if (al != null)
            {
                ret = JsonConvert.DeserializeObject<IEnumerable<AccountListModel>>(al);
            }
            else
            {
                ret = (await InternalGetAccounts(await this.GetCompaniesForRequestAsync(), false, true))
                    .Select(o => AccountListModel.Map(o)).OrderBy(o => o.Company);

                Core.SetRedisValue(key, Cache.Compress(ret.ToJson()),
                    TimeSpan.FromDays(7));

                Core.GetRedisDatabase().SetAdd($"account_list_cache:{WebGlobal.CurrentUser.PrimaryCompanyId}", key);
            }

            if (WebGlobal.CurrentUser.IsAccountTypeUser())
            {
                var aul = AccountUser.GetByUserId(WebGlobal.CurrentUser.Id);

                ret = ret.Where(o => o.Id == WebGlobal.CurrentUser.AccountId ||
                    aul.Any(au => au.AccountId == o.Id))
                    .ToCollection();
            }
            else
                ret = ret.Select(o => AccountListModel.Reduce(o, WebGlobal.CurrentUser)).ToCollection();

            if (ret != null)
            {
                foreach (var r in ret)
                {
                    var company = (await WebGlobal.GetCompaniesAsync()).FirstOrDefault(f => f.Id == r.Companies?.FirstOrDefault());
                    if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.AccountTags) ||
                        (company != null && await company.HasFeatureAsync(Generated.Features.AccountTags) != true))
                    {
                        r.Tags = new Collection<int>();
                    }
                }
            }

            return ret;
        }

        [HttpGet]
        [Route("")]
        [Route("{id}")]
        public async Task<object> Resolver(int? id, bool calculateBalance = false, bool showInactive = false, bool showDeleted = false, [FromQuery] AccountType? accountTypeId = null)
        {
            if (id != null)
            {
                return await GetAsync((int)id);
            }
            else
            {
                return await Get(calculateBalance, showInactive, showDeleted, accountTypeId);
            }
        }

        /// <summary>
        /// Retrieve all accounts for the current company.
        /// </summary>
        /// <param name="calculateBalance"></param>
        /// <param name="showInactive">Include inactive accounts</param>
        /// <param name="showDeleted">Include deleted Accounts</param>
        /// <param name="accountTypeId"></param>
        /// <returns></returns>
        //[HttpGet]
        protected async Task<IActionResult> Get(bool calculateBalance = false, bool showInactive = false, bool showDeleted = false, AccountType? accountTypeId = null)
        {
            var key = "accounts:" + string.Join(",",
                (await this.GetCompaniesForRequestAsync()).Select(o => o.Id).OrderBy(ro => ro));

            var al = await Core.GetRedisValueAsync(key);

            Collection<AccountMinimalModel> ret = null;

            if (al != null && !calculateBalance &&
                !showInactive && // if showInactive is set, dont use the cache.   
                !showDeleted && // if showDeleted is set, dont use the cache.   
                Web.HttpContext.Current != null &&
                !Web.HttpContext.Current.IsAndroidDevice() &&
                !Web.HttpContext.Current.IsAppleDevice())
            {
                ret = JsonConvert.DeserializeObject<IEnumerable<AccountMinimalModel>>(al).ToCollection();
            }
            else
            {
                ret = (await InternalGetAccounts(await this.GetCompaniesForRequestAsync(), calculateBalance, showInactive, showDeleted, accountTypeId)).ToCollection();

                if (!calculateBalance &&
                    !showInactive && // if showInactive is set, dont set the cache.   
                    !showDeleted && // if showDeleted is set, dont set the cache.   
                    WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Driver &&
                    !WebGlobal.CurrentUser.IsAccountTypeUser())
                {
                    Core.SetRedisValue(key, ret.ToJson(),
                        TimeSpan.FromDays(1));
                }
            }

            if (WebGlobal.CurrentUser.IsAccountTypeUser())
            {
                ret = ret.Where(o => o.Id == WebGlobal.CurrentUser.AccountId)
                    .ToCollection();
            }

            // Filter out inactive accounts if not show inactive  
            if (ret != null && !showInactive)
                ret = ret.Where(w => w.Status != AccountStatus.Inactive).ToCollection();

            if (ret != null)
            {
                foreach (var r in ret)
                {
                    var company = (await WebGlobal.GetCompaniesAsync()).FirstOrDefault(f => f.Id == r.Companies?.FirstOrDefault());
                    if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.AccountTags) || 
                        (company != null && await company.HasFeatureAsync(Generated.Features.AccountTags) != true))
                    {
                        r.Tags = new Collection<int>();
                    }
                }
            }

            return Ok(ret);
        }

        /// <summary>
        /// Retrieve the specified account.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        //[HttpGet]
        //[Route("{id}")]
        protected async Task<AccountModel> GetAsync(int id)
        {
            var x = await Account.GetByIdAsync(id);

            if (x == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(x.Companies))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified account either doesn't exist or you don't have access to it.")
                });

            if (WebGlobal.CurrentUser.IsAccountTypeUser() && WebGlobal.CurrentUser.AccountId != id)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified account either doesn't exist or you don't have access to it.")
                });
            }

            var company = (await WebGlobal.GetCompaniesAsync()).FirstOrDefault(f => f.Id == x.CompanyId);
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.AccountTags) ||
                        (company != null && await company.HasFeatureAsync(Generated.Features.AccountTags) != true))
            {
                x.Tags = new Collection<AccountTag>();
            }

            var y = await AccountModel.MapAsync(x, true, false, WebGlobal.CurrentUser);
            y.StorageRate = null;

            y = AccountModel.ReduceDetails(y, WebGlobal.CurrentUser);
            y = AccountModel.FillKeyValues(y, currentUser: WebGlobal.CurrentUser);

            if (y.CallValidationRuleSet == null)
            {
                y.CallValidationRuleSet = AccountValidationRuleSetModel.Map(CallValidationRuleSetModel.Map(EntryValidationRuleSet.Get(x.CompanyId, x.Id)), WebGlobal.CurrentUser.Type)
                    .Where(ro => ro.IsRequired || ro.ShowWarning).ToCollection(); ;
            }

            if (Web.HttpContext.Current.IsAppleDevice())
            {
                y.CallValidationRuleSet = y.CallValidationRuleSet.Where(r => r?.Name != "Charges").ToCollection();
            }

            return y;
        }

        [HttpGet]
        [Route("tags")]
        [Route("{id}/tags")]
        public async Task<IEnumerable<AccountTagModel>> TagsAsync()
        {
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AccountTags))
                return new Collection<AccountTagModel>();

            return await InternalGetAccountTagsAsync();
        }

        [NonAction]
        public async Task<IEnumerable<AccountTagModel>> InternalGetAccountTagsAsync()
        {
            return (await AccountTag.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId))
                .Select(o => AccountTagModel.Map(o));
        }

        [HttpPost]
        [Route("{accountId}/file")]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.Accountant)]
        [DisableFormValueModelBinding]
        public async Task<ObjectResult> File(int accountId, [FromQuery] string description = "")
        {
            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }

            var account = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(account?.CompanyId);

            // Save file
            string path = Path.GetTempPath();

            //MultipartFormDataStreamProvider provider = new MultipartFormDataStreamProvider(path);

            //var task = await Request.Content.ReadAsMultipartAsync(provider);
            var form = await Web.HttpContext.Current.Request.ReadFormAsync();
            var file = form.Files[0];

            try
            {
                string fn = "";

                CompanyFile cn = new Extric.Towbook.CompanyFile();

                cn.CompanyId = WebGlobal.CurrentUser.CompanyId;
                cn.Accounts = new Collection<int>() { account.Id };
                cn.OwnerUserId = WebGlobal.CurrentUser.Id;
                cn.Filename = file.FileName;
                cn.Size = (int)file.Length;
                cn.Description = description;
                cn.RawUrl = cn.Filename;
                cn.Save();

                fn = HttpContextHelper.MapPath(cn.LocalLocation);

                if (!Directory.Exists(Path.GetDirectoryName(fn)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(fn));
                }

                await FileUploadUtils.saveAsAsync(file, fn);

                if (!DefenderApi.IsFileUnsafe(fn))
                {
                    var result = await FileUtility.SendFileAsync(fn);
                    if (result.IsHttpSuccess())
                        System.IO.File.Delete(fn);
                }
                else
                {
                    cn.Delete(WebGlobal.CurrentUser);
                    System.IO.File.Delete(fn);
                }

                return StatusCode((int)HttpStatusCode.Created, CompanyFileModel.Map(cn));
                //this.Request.CreateResponse(HttpStatusCode.Created, CompanyFileModel.Map(cn), PerRequestJsonSettingsFormatter.Instance);
            }
            catch (Exception e)
            {
                //throw;
                throw new TowbookException("File upload error", e);
            }
            finally
            {
                //file.Delete();
            }
        }

        [HttpPost]
        [Route("")]
        [ApiPermission(Towbook.User.TypeEnum.Manager)]
        public async Task<AccountModel> Post(AccountModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include JSON content in your request?")
                });
            }

            if (model.Id > 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You shouldn't set the ID in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            var account = new Account();

            AccountModel.Map(model, account);

            account.CompanyId = WebGlobal.CurrentUser.CompanyId;
            await account.Save();
            model.Id = account.Id;
            SaveKeyValues(model);

            return AccountModel.FillKeyValues(await AccountModel.MapAsync(account));
        }

        [HttpPut]
        [Route("{id}")]
        [ApiPermission(Towbook.User.TypeEnum.Manager)]
        public async Task<AccountModel> Put(int id, AccountModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.Id < 1)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You must set the Id in a POST request. If you're trying to create a new account, use the POST method instead.")
                });
            }

            if (model.Id != id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("The ID in the body of the request doesn't match the ID passed via the incoming URL.")
                });
            }

            var account = await Account.GetByIdAsync(id);

            if (account.CompanyId == 0 || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(account.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You cannot update built-in accounts or accounts that you don't have access to.")
                });


            if (account == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(account.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("The specified account either doesn't exist or you don't have access to it.") });

            bool addressChanged = account.Address != model.Address || account.City != model.City || account.State != model.State || account.Zip != model.Zip;
            
            AccountModel.Map(model, account);

            if (account.Type == AccountType.PrivateProperty && (addressChanged || account.Latitude == null || account.Longitude == null))
            {
                await account.UpdateLocation();
            }

            await account.Save();

            SaveKeyValues(model);

            var returnAccount = AccountModel.FillKeyValues(await AccountModel.MapAsync(account));
            var company = (await WebGlobal.GetCompaniesAsync()).FirstOrDefault(f => f.Id == account.CompanyId);
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.AccountTags) ||
                (company != null && await company.HasFeatureAsync(Generated.Features.AccountTags) != true))
            {
                returnAccount.Tags = new Collection<int>();
            }
            return returnAccount;
        }

        private void SaveKeyValues(AccountModel model)
        {
            if (model.Id < 1)
                return;

            if (model.InvoiceHideChargesByDefault != null)
                AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                    "AlwaysHideCharges", model.InvoiceHideChargesByDefault == true ? "1" : "0");

            if (model.InvoiceHideDiscountsByDefault != null)
                AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                    "AlwaysHideDiscounts", model.InvoiceHideDiscountsByDefault == true ? "1" : "0");

            if (model.PropertyGateCode != null)
                AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                    "PropertyGateCode", model.PropertyGateCode);

            if (model.PropertyManagementDetails?.PropertyContractStartDate != null)
                AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                    "PropertyContractStartDate", model.PropertyManagementDetails.PropertyContractStartDate.Value.ToShortDateString());

            if (model.PropertyManagementDetails?.PropertyContractEndDate != null)
                AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                    "PropertyContractEndDate", model.PropertyManagementDetails.PropertyContractEndDate.Value.ToShortDateString());

            // statement billing method 
            if (model.StatementPreferences?.BillingMethod != null)
            {
                if (Enum.TryParse(model.StatementPreferences.BillingMethod.Id.ToString(), out Account.PreferredBillableMethod pbm))
                {
                    AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                        "PreferredBillingMethod", ((int)pbm).ToString());
                }
            }

            // statement billing delivery method 
            if (model.StatementPreferences?.BillingDeliveryMethods != null)
            {
                var deliveryAsPrint = model.StatementPreferences.BillingDeliveryMethods.Contains("PRINT");
                var deliveryAsEmail = model.StatementPreferences.BillingDeliveryMethods.Contains("EMAIL");

                if (deliveryAsPrint && deliveryAsEmail)
                {
                    AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                        "PreferredStatementDeliveryMethod", "2");
                }
                else if (deliveryAsEmail)
                {
                    // EMAIL ONLY
                    AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                        "PreferredStatementDeliveryMethod", "1");
                }
                else if (deliveryAsPrint)
                {
                    // PRINT (Default)
                    AccountKeyValue.Save(model.Id, Provider.Towbook.ProviderId,
                        "PreferredStatementDeliveryMethod", "0");
                }
            }
        }

        [HttpDelete]
        [Route("{id}")]
        [Route("")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        public async Task<HttpResponseMessage> Delete([FromQuery] int id)
        {
            var account = await Account.GetByIdAsync(id);

            if (account == null)
                return new HttpResponseMessage(HttpStatusCode.OK);

            if (account.CompanyId == 0 || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(account.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You cannot update built-in accounts or accounts that you don't have access to.")
                });

            await account.Delete(WebGlobal.CurrentUser);
            await DoCacheUpdateAsync(account, CurrentUser, true);

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        [HttpPost]
        [HttpPut]
        [Route("{id}/undelete")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        public async Task<HttpResponseMessage> Undelete(int id)
        {
            var account = await Account.GetByIdAsync(id);

            if (account == null)
                return new HttpResponseMessage(HttpStatusCode.OK);

            if (account.CompanyId == 0 || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(account.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You cannot update built-in accounts or accounts that you don't have access to.")
                });

            await account.Undelete(WebGlobal.CurrentUser);
            await DoCacheUpdateAsync(account, CurrentUser);

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        [HttpGet]
        [Route("{id}/balance")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Accountant)]
        public async Task<decimal> BalanceAsync(int id)
        {
            var account = await Account.GetByIdAsync(id);

            if (account == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(account.CompanyId))
            {
                throw new Web.HttpResponseException(
                    new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified account either doesn't exist or you don't have access to it.")
                    });
            }

            return await account.GetBalanceAsync(WebGlobal.CurrentUser.CompanyId);
        }

        #region Notes

        /// <summary>
        /// Retrieve notes for the specified account.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}/notes")]
        public async Task<IEnumerable<AccountNoteModel>> Notes(int id)
        {
            var account = await Account.GetByIdAsync(id);

            if (account == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(account.CompanyId))
            {
                throw new Web.HttpResponseException(
                    new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified account either doesn't exist or you don't have access to it.")
                    });
            }

            if (WebGlobal.CurrentUser.AccountId > 0)
            {
                return Array.Empty<AccountNoteModel>();
            }

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
            {
                var hideNotes = CompanyKeyValue.GetFirstValueOrNull(account.CompanyId,
                    Provider.Towbook.ProviderId, "HideAccountNotesFromDrivers");

                if (hideNotes == "1")
                    return Array.Empty<AccountNoteModel>();
            }

            return await Task.WhenAll(AccountNote.GetByAccountId(account.Id).Select(async n =>
            {
                var user = await Extric.Towbook.User.GetByIdAsync(n.OwnerUserId);

                return new AccountNoteModel(
                    n.Id,
                    n.Title,
                    n.Body,
                    n.TypeId,
                    n.Date,
                    n.CreateDate,
                    user?.FullName,
                    user?.Id
                );
            }));
        }

        [HttpPost("{id}/notes")]        
        public async Task<AccountNoteModel> NotesAsync(int id, AccountNotesBody body)
        {
            var imp = await Account.GetByIdAsync(id);

            if (imp == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(imp.CompanyId))
            {
                throw new Web.HttpResponseException(
                    new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified impound either doesn't exist or you don't have access to it.")
                    });
            }

            if (WebGlobal.CurrentUser.AccountId > 0)
            {
                throw new Web.HttpResponseException(
                    new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("Submitting notes from your user type isn't supported.")
                    });
            }

            var n = new AccountNote()
            {
                AccountId = imp.Id,
                OwnerUserId = WebGlobal.CurrentUser.Id,
                Body = body.Note,
                TypeId = body.Type,
                Title = body.Title
            };

            n.Save();

            var user = await Extric.Towbook.User.GetByIdAsync(n.OwnerUserId);

            return new AccountNoteModel()
            {
                Id = n.Id,
                Title = n.Title,
                Note = n.Body,
                Type = n.TypeId,
                Date = WebGlobal.OffsetDateTime(n.Date),
                CreateDate = WebGlobal.OffsetDateTime(n.CreateDate),
                AuthorName = user.FullName,
                AuthorId = user.Id
            };
        }


        //routes.MapHttpRoute(
        //  name: "Account_Note_Delete",
        //  routeTemplate: "accounts/note/{id}",
        //  defaults: new { controller = "Accounts", action = "Note" },
        //  constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) })
        //  .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpDelete]
        [Route("note/{id}")]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.Accountant)]
        public async Task<HttpResponseMessage> Note(int id)
        {
            var note = AccountNote.GetById(id);

            if (note == null)
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            var ac = await Account.GetByIdAsync(note.AccountId);
            if (ac == null)
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(ac.CompanyId))
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            note.Delete(WebGlobal.CurrentUser);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }


        public class AccountNotesBody
        {
            public string Note { get; set; }
            public string Title { get; set; }
            public int Type { get; set; }
        }

        public class NotesBody
        {
            public string Note { get; set; }
        }

        #endregion

        [HttpGet]
        [Route("unpaidinvoices")]
        public async Task<object> UnpaidInvoices(
            [FromQuery] DateTime endDate,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] int? reasonId = null)
        {
            // ZORESHACK, ZORES, HACK: make bobs not be included.
            int[] companies = (await WebGlobal.GetCompaniesAsync()).Where(o => o.Id != 1525).Select(o => o.Id).ToArray();

            // TODO: this is broken for people with multiple companies outside of zores.. 
            // Should be determining shared balances based on DispatchEntrySequencesShared

            if (WebGlobal.CurrentUser.CompanyId == 1525)
                companies = new int[] { 1525 };

            var statements = await Statement.GetUnpaidUntilAsync(companies, endDate, startDate, reasonId);


            if (new int[] { 4185, 4189, 4194 }.Contains(WebGlobal.CurrentUser.CompanyId))
            {
                var toIgnore = new int[] { 2432, 36960, 34766 };

                foreach (var x in statements.Values)
                {

                    if (reasonId.GetValueOrDefault() == 0)
                    {
                        foreach (var tm in x.ToCollection().Where(o => toIgnore.Contains(o.DispatchEntry.ReasonId)))
                        {
                            x.Remove(tm);
                        }
                    }
                }
            }

            return statements.Select(o => new
            {
                AccountId = o.Key,
                Amount = o.Value.Sum(x => x.BalanceDue),
                Invoices = o.Value.Select(y => new
                {
                    CallId = y.DispatchEntry != null ? y.DispatchEntry.Id : 0,
                    CallNumber = y.DispatchEntry != null ? y.DispatchEntry.CallNumber : 0,
                    ReasonId = y.DispatchEntry != null ? y.DispatchEntry.ReasonId : 0,
                    ReasonName = y.DispatchEntry != null ? y.DispatchEntry.Reason?.Name ?? "Unknown" : "Unknown",
                    InvoiceTotal = y.GrandTotal,
                    AmountDue = y.BalanceDue,
                    CreateDate = y.DispatchEntry != null ? y.DispatchEntry.CreateDate : y.CreateDate,
                    PurchaseOrderNumber = y.DispatchEntry != null ? y.DispatchEntry.PurchaseOrderNumber : "T_INV_" + y.Id
                })
                .OrderBy(a => a.CallId)
                .ToArray()
            })
            .OrderBy(b => b.AccountId)
            .ToArray();
        }

        public class CreateStatementDueDateModel
        {
            public int AccountId { get; set; }
            public DateTime DueDate { get; set; }
        }

        public class CreateStatementModel
        {
            public DateTime EndDate { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime DueDate { get; set; }
            public DateTime StatementDate { get; set; }
            public int[] Accounts { get; set; }
            public int ReasonId { get; set; }
            public CreateStatementDueDateModel[] DueDates { get; set; }
        }

        [HttpPost]
        [Route("createstatements")]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.Accountant)]
        public async Task<object> CreateStatements(CreateStatementModel model)
        {
            // ZORESHACK, ZORES, HACK: make bobs not be included.
            int[] companies = (await WebGlobal.GetCompaniesAsync()).Where(o => o.Id != 1525).Select(o => o.Id).ToArray();

            if (WebGlobal.CurrentUser.CompanyId == 1525)
                companies = new int[] { 1525 };

            var unpaid = await Statement.GetUnpaidUntilAsync(companies, model.EndDate, model.StartDate, model.ReasonId);

            var ss = Dispatch.Status.GetByCompany(companies[0], StatusCategory.Dispatching);

            //using (var scope = Core.CreateTransactionScope())
            {
                Collection<Statement> statements = new Collection<Statement>();

                foreach (int accountId in model.Accounts)
                {
                    if (unpaid.ContainsKey(accountId))
                    {
                        var invoices = unpaid[accountId];

                        if (invoices != null)
                        {
                            Statement st = new Statement();
                            st.SourceId = StatementSource.BatchStatementCreation;
                            st.Company = WebGlobal.CurrentUser.Company;
                            st.DueDate = model.DueDate;
                            st.DispatchEntries = new Collection<Entry>();

                            var overrideDueDate = model.DueDates.FirstOrDefault(f => f.AccountId == accountId);
                            if (overrideDueDate != null)
                                st.DueDate = overrideDueDate.DueDate;

                            foreach (var d in invoices)
                            {
                                if (d.DispatchEntry != null)
                                {
                                    if (model.ReasonId != 0)
                                    {
                                        if (d.DispatchEntry.ReasonId == model.ReasonId)
                                            st.DispatchEntries.Add(d.DispatchEntry);
                                    }
                                    else
                                    {
                                        if (new int[] { 4185, 4189, 4194 }.Contains(WebGlobal.CurrentUser.CompanyId))
                                        {
                                            if (!new int[] { 2432, 36960, 34766 }.Contains(d.DispatchEntry.ReasonId))
                                                st.DispatchEntries.Add(d.DispatchEntry);
                                        }

                                        st.DispatchEntries.Add(d.DispatchEntry);
                                    }
                                }
                                else if (d.AccountId != null)
                                    st.Invoices.Add(d);
                            }

                            st.AccountId = accountId;
                            st.OwnerUserId = WebGlobal.CurrentUser.Id;
                            st.StatementDate = model.StatementDate;

                            await st.Save();
                            statements.Add(st);
                        }
                    }
                }

                StatementBatch sb = new StatementBatch();

                sb.OwnerUserId = WebGlobal.CurrentUser.Id;
                sb.CompanyId = WebGlobal.CurrentUser.CompanyId;
                sb.StatementIds = statements.Select(o => o.Id).ToList();
                sb.Save();

                return statements.Select(o => new { Id = o.Id, AccountId = o.AccountId });
            }
        }

        private static async Task<AccountMinimalModel> RemapAsync(AccountModel o)
        {
            return await AccountMinimalModel.MapAsync(o, null, null, null, null, WebGlobal.CurrentUser);
        }


        public class XComparer : IEqualityComparer<AccountModel>
        {
            public bool Equals(AccountModel x1, AccountModel x2)
            {
                if (object.ReferenceEquals(x1, x2))
                    return true;
                if (x1 == null || x2 == null)
                    return false;

                // this is very slow.
                return x1.ToJson() == x2.ToJson();
            }

            public int GetHashCode(AccountModel x)
            {
                return x.Id.GetHashCode();
            }
        }


        public class InternalGetForAccountObjectComparer : IEqualityComparer<InternalGetForAccountObject>
        {
            public bool Equals(InternalGetForAccountObject x1,
                InternalGetForAccountObject x2)
            {
                if (object.ReferenceEquals(x1, x2))
                    return true;
                if (x1 == null || x2 == null)
                    return false;

                // this is very slow.
                return x1.ToJson() == x2.ToJson();
            }

            public int GetHashCode(InternalGetForAccountObject x)
            {
                return x.Id.GetHashCode();
            }
        }

        public static async Task<IEnumerable<AccountMinimalModel>> InternalGetAccounts(
            Company.Company[] companies = null, bool calculateBalance = false, bool showInactive = false, bool showDeleted = false, AccountType? accountTypeId = null,
            User currentUser = null)
        {
            if (currentUser == null)
                currentUser = WebGlobal.CurrentUser;

            if (currentUser.Type == Towbook.User.TypeEnum.AccountUser ||
                currentUser.Type == Towbook.User.TypeEnum.AccountManager)
            {
                var a = await Account.GetByIdAsync(currentUser.AccountId);

                if (a != null)
                {
                    var aul = AccountUser.GetByUserId(currentUser.Id);
                    var accountsToMap = await Account.GetByIdsAsync(aul.Select(o => o.AccountId).ToArray());
                    var mappedAccounts = await Task.WhenAll(accountsToMap.Select(async o => await RemapAsync(await AccountModel.MapAsync(o))));

                    return new AccountMinimalModel[] { await RemapAsync(await AccountModel.MapAsync(a)) }
                        .Union(mappedAccounts);
                }
                else
                {
                    throw new TowbookException("Account for your user doesn't exist.");
                }
            }

            var tempList = new Collection<AccountModel>();
            var rates = new Collection<InternalGetForAccountObject>();
            var customFields = new Collection<AccountKeyValue>();
            var ruleSets = new Collection<EntryValidationRuleSet>();
            var customFieldDefaultValues = new Collection<CompanyKeyDefaultValue>();

            Collection<Account> accounts = null; // Account.GetByCompany(companies, showInactive, accountTypeId);
            if (companies.Length > 1 || accountTypeId != null)
                accounts = await Account.GetByCompanyAsync(companies, showInactive, accountTypeId, showDeleted);
            else
                accounts = await Account.GetByCompany(companies.First().Id, showInactive, showDeleted);

            rates = AccountRateItemModel.InternalGetForAccount(accounts, currentUser).ToCollection();

            tempList = tempList.Union(
                await AccountModel.MapAsync(accounts, !calculateBalance), new XComparer()).ToCollection();

            var accountKeyNames = new Collection<string>(){ "AlwaysHideCharges",
                        "AlwaysHideDiscounts",
                        "ProviderId",
                        "PropertyGateCode",
                        "PropertyContractStartDate",
                        "PropertyContractEndDate",
                        "ParkingPermitsEnabled",
                        "StickeringEnabled",
                        "AccountPhysicalAddressAsDefaultLocation",
                        "DefaultAssetBodyTypeId",
                        "AutomaticallyAddMiles",
                        "AutomaticallyAddDeadheadMileage",
                        "DefaultSubcontractorAccountId",
                        "DefaultBillToAccountId",
                        "PreferredBillingMethod",
                        "PreferredStatementDeliveryMethod"
                    };

            foreach (var c in companies)
            {
                #region include payment link preset
                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.PaymentIntegrations_Square))
                {
                    customFieldDefaultValues.Add(new CompanyKeyDefaultValue()
                    {
                        Key = "Square_AlwaysIncludePaymentLinkOnInvoices",
                        CompanyId = c.Id,
                        DefaultValue = CompanyKeyValue.GetFirstValueOrNull(c.Id, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices") == "1" ? "1" : "0"
                    });


                    if (!accountKeyNames.Contains("Square_AlwaysIncludePaymentLinkOnInvoices"))
                        accountKeyNames.Add("Square_AlwaysIncludePaymentLinkOnInvoices");
                }
                #endregion

                customFields = customFields.Union(AccountKeyValue.GetByCompany(c.Id, Provider.Towbook.ProviderId, accountKeyNames.ToArray())).ToCollection();
            }

            var companyIds = companies.Select(s => s.Id).ToArray();
            var accountIds = accounts.Select(s => s.Id).ToArray();

            ruleSets = EntryValidationRuleSet.GetByAccountIds(companyIds, accountIds).ToCollection();

            var attributes = await Dispatch.Attribute.GetByAccountAsync(companyIds, accountIds);

            var managers = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Accounts_AccountManagers) ?
                                AccountManager.GetByAccountIds(accounts.Select(s => s.Id).ToArray()) : null;

            IEnumerable<AccountMinimalModel> list =
                (await Task.WhenAll(
                    tempList.Select(async accountModel => await AccountMinimalModel.MapAsync(accountModel,
                        rates.Where(r => r.AccountId == accountModel.Id).Select(accountObject => new InternalGetForAccountObject
                        {
                            AccountId = accountObject.AccountId,
                            Id = accountObject.Id,
                            Cost = accountObject.Cost,
                            FreeQuantity = accountObject.FreeQuantity,
                            Discount = accountObject.Discount,
                            DiscountExempt = accountObject.DiscountExempt,
                            ExtendedRates = accountObject.ExtendedRates
                        }),
                        customFields.Where(accountKeyValue => accountKeyValue.AccountId == accountModel.Id),
                        ruleSets,
                        attributes.ContainsKey(accountModel.Id) ? attributes[accountModel.Id].ToArray() : Array.Empty<Dispatch.Attribute>(),
                        currentUser,
                        customFieldDefaultValues,
                        managers)
                    )
                ))
                .OrderBy(a => a.Name).ToCollection();

            return list;
        }

        /// <summary>
        /// Returns a set of the accounts with only the Name, Type and Id.
        /// </summary>
        /// <returns></returns>
        public static async Task<object> InternalGetAccountsMinimalAsync(Company.Company[] companies = null, bool calculateBalance = false)
        {
            IEnumerable<dynamic> list = new Collection<dynamic>();
            if (companies == null)
            {
                companies = new Company.Company[] { WebGlobal.CurrentUser.Company };
            }

            foreach (var c in companies)
            {
                var accounts = await Account.GetByCompanyAsync(c, false, null);
                var mappedAccounts = await AccountModel.MapAsync(accounts, !calculateBalance);

                list = list.Union(mappedAccounts
                    .Select(o => AccountModel.ReduceDetails(o, WebGlobal.CurrentUser))
                    .Select(o => new
                    {
                        Id = o.Id,
                        Name = o.Company,
                        Type = o.Type
                    }));
            }

            list = list.OrderBy(a => a.Name).ToArray();

            if (WebGlobal.CurrentUser.AccountId > 0)
            {
                list = list.Where(o => o.Id == WebGlobal.CurrentUser.AccountId).ToArray();
            }


            return list;
        }


        public class RotationStatusModel
        {
            public NextUpModel NextUp { get; set; }

            public class NextUpModel
            {
                public int AccountId { get; set; }
                public string Name { get; set; }
                public string Phone { get; set; }
            }

        }

        [HttpGet]
        [Route("{id}/rotationstatus")]
        public async Task<RotationStatusModel> RotationStatusAsync(int id, [FromQuery] int bodyTypeId)
        {
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.DispatchToSubcontractors_SubcontractorRotation))
                return new RotationStatusModel();

            var rsh = RotationStatusHandler.Peak(id, bodyTypeId);

            var ac = await Account.GetByIdAsync(rsh.Rotation.SubcontractorAccountId);

            return new RotationStatusModel()
            {
                NextUp = new RotationStatusModel.NextUpModel()
                {
                    AccountId = rsh.Rotation.SubcontractorAccountId,
                    Name = ac.Company,
                    Phone = ac.Phone
                }
            };
        }


        /// <summary>
        /// Requests Towbook to attempt logging this accounts Contractor ID's in (for Allstate, etc)
        /// </summary>
        /// <param name="id">JobID that is processing the request. A separate pusher event will come back to the user notifying the user of completion/failure.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("{id}/login")]
        public async Task<object> Login(int id)
        {
            if (WebGlobal.CurrentUser != null &&
                WebGlobal.CurrentUser.CompanyId == 20005 &&
                WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager)
                return null;

            var d = new DigitalDispatchController();
            return await d.Login(id);
        }

        public class DefaultEtaModel
        {
            public int? Eta { get; set; }
        }
        [HttpPost]
        [Route("{id}/defaulteta")]
        public async Task<HttpResponseMessage> DefaultEta(int id, DefaultEtaModel etaModel)
        {
            if (WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager &&
                WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Dispatcher)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You don't have permission to do that.")
                });


            var a = await Account.GetByIdAsync(id);

            if (a == null || (WebGlobal.CurrentUser != null &&
                !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId)))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified account either doesn't exist or you don't have access to it.")
                });

            AccountKeyValue.Save(a.Id, Provider.Towbook.ProviderId,
                "DefaultEta", etaModel.Eta?.ToString());


            if (a.MasterAccountId == MasterAccountTypes.Sykes && etaModel.Eta != null)
            {
                // TODO: move this to backend service so our web servers aren't talking directly to Sykes
                var sc = Integrations.MotorClubs.Sykes.SykesContractor.GetByAccountId(a.Id);
                var src = Integrations.MotorClubs.Sykes.SykesRestClient.Get();

                foreach (var each in sc)
                {
                    src.ETAPost(each.ContractorId, "EDETA", etaModel.Eta.Value);
                    src.ETAPost(each.ContractorId, "EDETAF", etaModel.Eta.Value);
                    src.ETAPost(each.ContractorId, "EDETAS", etaModel.Eta.Value);
                }
            }

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }



        public class AutoAcceptModel
        {
            public bool Enabled { get; set; }
        }

        [HttpPost("{id}/autoAccept")]
        public async Task<HttpResponseMessage> AutoAccept(int id, AutoAcceptModel eta)
        {
            if (WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You don't have permission to do that.")
                });

            var a = await Account.GetByIdAsync(id);

            if (a == null || (WebGlobal.CurrentUser != null &&
                !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId)))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified account either doesn't exist or you don't have access to it.")
                });

            if (eta.Enabled)
                AccountKeyValue.Save(a.Id, Provider.Towbook.ProviderId,
                    "DigitalAutoAccept", "60");
            else
                AccountKeyValue.Save(a.Id, Provider.Towbook.ProviderId,
                    "DigitalAutoAccept", "Disabled");

            var logEvent = new LogEventInfo();
            logEvent.LoggerName = logger.Name;
            logEvent.Message = "AutoAccept Adjusted - " + eta.Enabled;
            logEvent.Level = LogLevel.Warn;
            logEvent.TimeStamp = DateTime.Now;
            logEvent.Properties["companyId"] = a.CompanyId;
            logEvent.Properties["companyName"] = (await Company.Company.GetByIdAsync(a.CompanyId)).Name;
            logEvent.Properties["username"] = WebGlobal.CurrentUser.Username;
            logEvent.Properties["accountName"] = a.Company;
            logEvent.Properties["masterAccountId"] = a.MasterAccountId;
            logEvent.Properties["json"] = eta.ToJson();

            logger.Log(logEvent);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }


        public sealed class DirectBillingLoginModel
        {
            public string Username { get; set; }
            public string Password { get; set; }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPost("{id}/billinglogin")]
        [FormContentType]
        public async Task<object> BillingLoginFormAsync(int id, [FromForm] DirectBillingLoginModel model) => await InternalBillingLoginAsync(id, model);

        [HttpPost("{id}/billinglogin")]
        public async Task<object> BillingLoginBodyAsync(int id, [FromBody] DirectBillingLoginModel model) => await InternalBillingLoginAsync(id, model);

        private async Task<object> InternalBillingLoginAsync(int id, [FromBody]DirectBillingLoginModel model)
        {
            var avq = new Extric.Towbook.Integration.MotorClubs.Queue.AuthenticationQueueItem();
            avq.CompanyId = WebGlobal.CurrentUser.CompanyId;
            avq.AccountId = id;
            avq.Status = Extric.Towbook.Integration.MotorClubs.Queue.AuthenticationQueueItemStatus.Initializing;
            avq.OwnerUserId = WebGlobal.CurrentUser.Id;
            avq.Username = model.Username;
            avq.Password = model.Password;
            avq.Save();

            var avs = new Extric.Towbook.Integration.MotorClubs.Services.AuthenticationVerificationService();
            await avs.RequestVerificationOfUserCredentials(avq);

            return new { JobId = avq.Id };
        }

        /// <summary>
        /// Requests that Towbook log this accounts contractor IDs for Digital Dispatching. 
        /// </summary>
        /// <param name="id">JobID that is processing the request. A separate pusher event will come back to the user notifying the user of completion/failure.</param>
        /// <returns></returns>
        [HttpPost("{id}/logoff")]
        public async Task<object> Logoff(int id)
        {
            if (WebGlobal.CurrentUser != null &&
                WebGlobal.CurrentUser.CompanyId == 20005 &&
                WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager)
                return null;

            var d = new DigitalDispatchController();
            return await d.Logout(id);
        }

        [HttpPost]
        [Route("{id}/logout")]
        public async Task<object> Logout(int id) => await Logoff(id);

        public class AccountLocationModel
        {
            public int? LocationId { get; set; }
            public string Name { get; set; }
            public string Address { get; set; }
            public decimal? Latitude { get; set; }
            public decimal? Longitude { get; set; }

            /// <summary>
            /// The ID of the location in the remote system 
            /// (Agero FacilityId, Swoop SiteId, Geico LocationId)
            /// </summary>
            public string ReferenceId { get; set; }

            public int? AccountId { get; set; }
            public string AccountName { get; set; }

            public int? CompanyId { get; set; }
            public string CompanyName { get; set; }

            /// <summary>
            /// Distance of the Towbook Company Address or AccountMapFrom Address to the reference Location Address. 
            /// </summary>
            public decimal? Distance { get; set; }

            public string[] CoveredZips { get; set; }
        }


        /// <summary>
        /// Returns Location mappings for an account. 
        /// To be used with Agero, Agero (Swoop), Geico, and any others that have the concept of location/sites.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{id}/locations")]
        public async Task<AccountLocationModel[]> Locations(int id)
        {
            var a = await Account.GetByIdAsync(id);

            if (a == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId))
                return null;

            if (a.MasterAccountId != MasterAccountTypes.Swoop)
                return Array.Empty<AccountLocationModel>();

            var session = SwoopSession.GetByAccountId(id);

            if (session == null) 
                return Array.Empty<AccountLocationModel>();

            var src = new SwoopRestClient();

            var sites = await src.GetSites(session.AccessToken);

            var rateAgreementSites = (await src.GetRateAgreements(session.AccessToken, true))
                .Select(ra => new PartnerSite()
                {
                    Id = ra.Id,
                    Name = ra.Name,
                    Status = ra.Status,
                    Address = ra.AnchorLocation.Address,
                    Latitude = ra.AnchorLocation.Latitude,
                    Longitude = ra.AnchorLocation.Longitude,
                    ServiceRates = ra.ServiceRates?.Select(sr => new
                    {
                        Name = sr.Name,
                        Rates = sr.ratesByType
                    }).ToArray()
                }).ToArray();

            var links = await Task.WhenAll(SwoopSite.GetBySessionId(session.SwoopSessionId)
                .Select(async o => new
                {
                    Account = await Account.GetByIdAsync(o.AccountId),
                    Company = await Company.Company.GetByIdAsync(o.CompanyId),
                    Site = o
                }).ToList());

            var siteModels = sites.Union(rateAgreementSites).Select(o =>
            {
                var ret = new AccountLocationModel()
                {
                    ReferenceId = o.Id,
                    Name = o.Name,
                    Latitude = o.Latitude,
                    Longitude = o.Longitude,
                    Address = o.Address,
                    CoveredZips = o.CoveredZips
                };

                var linked = links.FirstOrDefault(r => r.Site.SiteId == o.Id);
                if (linked != null)
                {
                    ret.LocationId = linked.Site.SwoopSiteId;
                    ret.CompanyId = linked.Company.Id;
                    ret.CompanyName = linked.Company.Name;
                    ret.AccountId = linked.Account.Id;
                    ret.AccountName = linked.Account.Company;

                    if (linked.Company.Latitude != 0 &&
                        linked.Company.Longitude != 0)
                    {
                        var companyGeo = new GeoCoordinate((double)linked.Company.Latitude,
                                                       (double)linked.Company.Longitude);
                        var swoopGeo = new GeoCoordinate((double)o.Latitude,
                            (double)o.Longitude);

                        var distance = swoopGeo.GetDistanceTo(companyGeo);

                        ret.Distance = (decimal)distance / (decimal)1609.344;
                    }
                }

                return ret;
            });

            return siteModels.ToArray();
        }

        public static async Task DoCacheUpdateAsync(Account _account, User currentUser, bool delete = false)
        {

            var cmp = await GetCompaniesForRequestAsync();
            var key = "accounts:" + string.Join(",",
                cmp.Select(o => o.Id).OrderBy(ro => ro));

            var json = await Core.GetRedisValueAsync(key);

            if (json != null)
            {
                var accountList = Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<Extric.Towbook.API.Models.AccountMinimalModel>>(json);

                if (delete)
                {
                    accountList = accountList.Where(o => o.Id != _account.Id);
                }
                else
                {
                    accountList = accountList.Where(o => o.Id != _account.Id).Union(new[]
                    {
                        await AccountMinimalModel.MapAsync(await AccountModel.MapAsync(_account, true), null, null, null, null, currentUser)
                    }).OrderBy(o => o.Name);
                }

                json = accountList.ToJson();
                await Core.SetRedisValueAsync(key, json);
            }
        }

       public static async Task<Extric.Towbook.Company.Company[]> GetCompaniesForRequestAsync()
        {
            try
            {
                var obj = Extric.Towbook.Web.HttpContext.Current;

                string company = "";

                if (!string.IsNullOrWhiteSpace(company))
                {
                    if (company == "all")
                    {
                        return (await WebGlobal.GetCompaniesAsync()).ToArray();
                    }
                    else
                    {
                        var companies = await WebGlobal.GetCompaniesAsync();
                        var single = companies.FirstOrDefault(o => o.Id == Convert.ToInt32(company));
                        return new Extric.Towbook.Company.Company[] { single };
                    }
                }
                else
                {
                    return new Extric.Towbook.Company.Company[] { WebGlobal.CurrentUser.Company };
                }
            }
            finally
            {
            }
        }


        [HttpPost]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.SystemAdministrator)]
        [Route("{id}/locations")]
        public async Task<AccountLocationModel> Locations(int id, AccountLocationModel model)
        {
            var logEvent = new LogEventInfo();

            var a = await Account.GetByIdAsync(id);

            if (a == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId))
                return null;

            if (a.MasterAccountId != MasterAccountTypes.Swoop)
                return null;

            var session = SwoopSession.GetByAccountId(id);

            if (session == null)
                return null;

            if (model.ReferenceId == null)
                throw new Exception("referenceId is required to store a location mapping.");

            var links = SwoopSite.GetBySessionId(session.SwoopSessionId);

            if (links.Any(o => o.SiteId == model.ReferenceId))
            {
                logEvent.Message = "Added Location Mapping, replacing existing " + links.Count() + " links";
                foreach (var link in links)
                {
                    link.Delete();
                }
                links = new SwoopSite[0];
            }

            var alma = await Account.GetByIdAsync(model.AccountId.Value);

            if (alma == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(alma.CompanyId))
                throw new Exception("invalid accountId");

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.CompanyId.Value))
                throw new Exception("invalid accountId");

            var newSite = new SwoopSite()
            {
                SwoopSessionId = session.SwoopSessionId,
                SiteId = model.ReferenceId,
                CompanyId = model.CompanyId.Value,
                AccountId = model.AccountId.Value,
                OwnerUserId = WebGlobal.CurrentUser.Id,
                IpAddress = WebGlobal.GetRequestingIp(),
            };
            newSite.Save();

            var alm = new AccountLocationModel();

            alm.CompanyId = newSite.CompanyId;
            alm.AccountId = newSite.AccountId;
            alm.ReferenceId = newSite.SiteId;


            logEvent.LoggerName = logger.Name;
            if (logEvent.Message == null)
                logEvent.Message = "Added Location Mapping";
            logEvent.Level = LogLevel.Info;
            logEvent.TimeStamp = DateTime.Now;
            logEvent.Properties["companyId"] = a.CompanyId;
            logEvent.Properties["companyName"] = (await Company.Company.GetByIdAsync(a.CompanyId)).Name;
            logEvent.Properties["username"] = WebGlobal.CurrentUser.Username;
            logEvent.Properties["accountName"] = a.Company;
            logEvent.Properties["masterAccountId"] = a.MasterAccountId;
            logEvent.Properties["masterAccountName"] = MasterAccountTypes.GetName(a.MasterAccountId);
            logEvent.Properties["json"] = alm.ToJson();

            logger.Log(logEvent);

            return alm;
        }


        [HttpPut]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.SystemAdministrator)]
        [Route("{id}/locations/{locationId}")]
        public async Task<AccountLocationModel> LocationsPut(int id, string locationId, AccountLocationModel model)
        {
            var logEvent = new LogEventInfo();

            var a = await Account.GetByIdAsync(id);
            
            if (a == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId))
                return null;

            if (a.MasterAccountId != MasterAccountTypes.Swoop)
                return null;

            var session = SwoopSession.GetByAccountId(id);

            if (session == null)
                return null;

            if (model.ReferenceId == null)
                throw new Exception("referenceId is required to store a location mapping.");

            var links = SwoopSite.GetBySessionId(session.SwoopSessionId);

            var alma = await Account.GetByIdAsync(model.AccountId.Value);

            if (alma == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(alma.CompanyId))
                throw new Exception("invalid accountId");

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.CompanyId.Value))
                throw new Exception("invalid companyId");

            var linkToUpdate = links.FirstOrDefault(o => o.SiteId == locationId);

            if (linkToUpdate == null)
            {
                throw new Exception("No existing mapping exists for this ID");
            }

            linkToUpdate.CompanyId = model.CompanyId.Value;

            var account = await Account.GetByIdAsync(model.AccountId.Value);

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(account.CompanyId))
                throw new Exception("invalid accountId");

            linkToUpdate.AccountId = model.AccountId.Value;

            linkToUpdate.Save();

            logEvent.LoggerName = logger.Name;
            if (logEvent.Message == null)
                logEvent.Message = "Updated Location Mapping for " + id + " / " + locationId;

            logEvent.Level = LogLevel.Info;
            logEvent.TimeStamp = DateTime.Now;
            logEvent.Properties["companyId"] = a.CompanyId;
            logEvent.Properties["companyName"] = (await Company.Company.GetByIdAsync(a.CompanyId)).Name;
            logEvent.Properties["username"] = WebGlobal.CurrentUser.Username;
            logEvent.Properties["accountName"] = a.Company;
            logEvent.Properties["masterAccountId"] = a.MasterAccountId;
            logEvent.Properties["masterAccountName"] = MasterAccountTypes.GetName(a.MasterAccountId);
            logEvent.Properties["json"] = model.ToJson();

            logger.Log(logEvent);

            var alm = new AccountLocationModel();

            alm.CompanyId = linkToUpdate.CompanyId;
            alm.AccountId = linkToUpdate.AccountId;
            alm.ReferenceId = linkToUpdate.SiteId;

            return alm;
        }

        [HttpDelete]
        [Route("{id}/locations/{locationId}")]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<HttpResponseMessage> Locations(int id, int locationId)
        {
            var a = await Account.GetByIdAsync(id);

            if (a == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId))
                return null;

            if (a.MasterAccountId != MasterAccountTypes.Swoop)
                return null;

            var link = SwoopSite.GetById(locationId);
            if (link == null)
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            await ThrowIfNoCompanyAccessAsync(link.CompanyId);

            link.Delete();


            var logEvent = new LogEventInfo();

            logEvent.LoggerName = logger.Name;
            logEvent.Message = "Deleted Location Mapping";
            logEvent.Level = LogLevel.Info;
            logEvent.TimeStamp = DateTime.Now;
            logEvent.Properties["companyId"] = a.CompanyId;
            logEvent.Properties["companyName"] = (await Company.Company.GetByIdAsync(a.CompanyId)).Name;
            logEvent.Properties["username"] = WebGlobal.CurrentUser.Username;
            logEvent.Properties["accountName"] = a.Company;
            logEvent.Properties["masterAccountId"] = a.MasterAccountId;
            logEvent.Properties["json"] = link.ToJson();

            logger.Log(logEvent);


            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        public class RegisterModel
        {
            public string ContractorId { get; set; }
            public string TaxId { get; set; }
            public string LocationId { get; set; }
        }

        /// <summary>
        /// Requests that Towbook register this accounts contractor IDs for Digital Dispatching. 
        /// </summary>
        /// <param name="id">JobID that is processing the request. A separate pusher event will come back to the user notifying the user of completion/failure.</param>
        /// <param name="rm">Specify the ContractorId, and optionally TaxId and Locationid, if the club requires it.</param>
        /// <returns></returns>
        [HttpPost("{id}/digitalregister")]
        public async Task<object> DigitalRegister(int id, RegisterModel rm)
        {
            var a = await Account.GetByIdAsync(id);

            if (a == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId))
                return null;

            Towbook.Integration.MotorClubs.Services.DigitalDispatchService.ValidateContractor(
                rm.ContractorId,
                rm.TaxId,
                rm.LocationId,
                a,
                a.CompanyId);

            var jobId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.RegisterCompany(
                a.CompanyId,
                a.Id,
                WebGlobal.CurrentUser.Id,
                rm.ContractorId,
                rm.TaxId,
                rm.LocationId);

            return new { JobId = jobId };
        }

        [HttpDelete("{id}/digitaldelete")]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<object> DigitalDelete(int id, RegisterModel rm)
        {
            var a = await Account.GetByIdAsync(id);

            if (a == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId))
                return null;

            var jobId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.DeregisterCompany(
                a.CompanyId,
                a.Id,
                WebGlobal.CurrentUser.Id,
                rm.ContractorId,
                rm.TaxId,
                rm.LocationId);

            return new { JobId = jobId };
        }

        public sealed class FindSubcontratorRequestModel
        {
            public string Email { get; set; }
        }

        public class FindSubcontratorResponseModel
        {
            public string Email { get; set; }
            public string Name { get; set; }
            public string Address { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string Zip { get; set; }
            public string Phone { get; set; }

            /// <summary>
            /// How far away is their office from you, in a straight line?
            /// </summary>
            public decimal DistanceInMiles { get; set; }
        }


        [HttpPost("findSubcontractor")]
        public async Task<FindSubcontratorResponseModel> FindSubcontractor(FindSubcontratorRequestModel fsm)
        {
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.DispatchToSubcontractors))
                return null;

            if (fsm == null || !Core.IsEmailValid(fsm.Email))
                return null;

            var found = EmailAddress.GetByEmailAddress(fsm.Email);

            if (found == null)
                return null;

            var company = await Company.Company.GetByIdAsync(found.CompanyId);
            if (found == null)
                return null; // company is invalid.

            var fsrm = new FindSubcontratorResponseModel();
            fsrm.Email = found.Address;

            fsrm.Name = company.Name;
            fsrm.City = company.City;
            fsrm.State = company.State;

            // If the company has this company in their account list, show they are pending. 
            bool isPending = false;

            if (isPending)
            {
                fsrm.Address = company.Address;
                fsrm.Zip = company.Zip;
                fsrm.Phone = company.Phone;
            }

            return fsrm;
        }

        [HttpGet("digitalstatus")]
        public async Task<IEnumerable<DigitalStatusModel>> DigitalStatusAsync()
        {
            return await DigitalDispatchController.StatusAsync();
        }

        [HttpGet("location")]
        public PolygonModel Location(int accountId)
        {
            // TODO: add implementation
            return new PolygonModel();
        }

        [HttpPut]
        [Route("location")]
        public PolygonModel Location(int accountId, PolygonModel model)
        {
            // TODO: add implementation
            return model;
        }
    }

    public sealed class PolygonModel
    {
        public int AccountId { get; set; }

        public DateTime LastUpdated { get; set; }

        public int OwnerUserId { get; set; }

        public decimal[][] Points { get; set; }
    }

    public sealed class AccountNoteModel
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Note { get; set; }
        public int Type { get; set; }

        public DateTime? Date { get; set; }
        public DateTime CreateDate { get; set; }
        public string AuthorName { get; set; }
        public int? AuthorId { get; set; }

        public AccountNoteModel()
        {

        }

        public AccountNoteModel(int id, string title, string note, int type, DateTime? date, DateTime createDate, string authorName, int? authorId)
        {
            Id = id;
            Title = title;
            Note = note;
            Type = type;
            Date = date;
            CreateDate = createDate;
            AuthorName = authorName;
            AuthorId = authorId;
        }

        public override bool Equals(object obj)
        {
            return obj is AccountNoteModel other &&
                   Id == other.Id &&
                   Title == other.Title &&
                   Note == other.Note &&
                   Type == other.Type &&
                   Date == other.Date &&
                   CreateDate == other.CreateDate &&
                   AuthorName == other.AuthorName &&
                   AuthorId == other.AuthorId;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Id, Title, Note, Type, Date, CreateDate, AuthorName, AuthorId);
        }
    }
}
