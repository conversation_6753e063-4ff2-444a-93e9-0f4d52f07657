using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Utility;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Integrations.Quickbooks;
using Extric.Towbook.Management;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models.Admin.Companies;
using Extric.Towbook.Company.Accounting;

namespace Extric.Towbook.API.Admin.Controllers.Companies
{
    [Route("admin/companies")]
    public class FeaturesController : ControllerBase
    {
        //routes.MapHttpRoute(
        //    name: "Admin_Companies_Api_withId",
        //    routeTemplate: "admin/companies/{companyId}/{controller}/{id}")
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers.Companies" } } };
        [Route("{companyId}/features")]
        [HttpGet]
        public async Task<CompanyContract> Get(int companyId)
        {
            var u = WebGlobal.ImpersonationUser ?? WebGlobal.CurrentUser;

            if (!await u.HasAccessToCompanyAsync(companyId))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Company not found or you don't have access.")
                });
            }
            var cc = CompanyContract.GetByCompanyId(companyId);
            return cc;
        }

        //routes.MapHttpRoute(
        //    name: "Admin_Companies_Api",
        //    routeTemplate: "admin/companies/{companyId}/{controller}")
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers.Companies" } } };
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        [Route("{companyId}/features")]
        [HttpPost]
        public async Task<object> Post(int companyId, FeatureBody body)
        {
            var cc = CompanyContract.GetExistingOrCreateNew(companyId, WebGlobal.CurrentUser.Id);

            if (cc.Features.Any(o => o.FeatureId == body.FeatureId && !o.IsDeleted))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Company already has the feature specified assigned.")
                });
            }

            return await InternalSaveFeature(companyId, body);
        }

        /// <summary>
        /// PUT Feature for a company.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="body"></param>
        /// <returns></returns>
        /// <exception cref="Extric.Towbook.Web.HttpResponseException"></exception>
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        [Route("{companyId}/features")]
        [HttpPut]
        public async Task<object> Put(int companyId, FeatureBody body)
        {
            var cc = CompanyContract.GetExistingOrCreateNew(companyId, WebGlobal.CurrentUser.Id);

            if (!cc.Features.Any(o => o.FeatureId == body.FeatureId))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Feature not found, did you mean to call the POST")
                });
            }

            return await InternalSaveFeature(companyId, body);
        }

        //routes.MapHttpRoute(
        //    name: "Admin_Companies_Api",
        //    routeTemplate: "admin/companies/{companyId}/{controller}")
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers.Companies" } } };
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        [Route("{companyId}/features")]
        [HttpDelete]
        public async Task<object> Delete(int companyId, FeatureBody body)
        {
            var cc = CompanyContract.GetByCompanyId(companyId);

            if (cc == null)
                return null;

            var ccf = cc.Features.FirstOrDefault(o => o.FeatureId == body.FeatureId && o.IsDeleted == false);
            if (ccf != null)
            {
                // If we remove the Quickbooks feature flag from a company, also remove their connection info to Quickbooks
                if (body.FeatureId == (int)Generated.Features.QuickBooks)
                {
                    var qbOnline = await QuickbooksUtility.GetConnector(companyId);
                    await Integration.Accounting.Providers.QuickBooks.Controllers.DisconnectController.RemoveQuickbooksConnectionAsync(companyId, qbOnline);
                }

                ccf.IsDeleted = true;
                await ccf.Save(companyId, WebGlobal.CurrentUser.Id);

                #region Closed Period Accounting    
                if (ccf.FeatureId == (int)Generated.Features.AdvancedBilling_ClosedAccountingPeriod)
                {
                    // delete company settings on disable of FF
                    var cpa = await ClosedPeriodOption.GetByCompanyIdAsync(companyId);
                    if (cpa != null)
                    {
                        cpa.Enabled = false;
                        await cpa.SaveAsync(WebGlobal.CurrentUser, this.GetRequestingIp());
                    }

                }
                #endregion
            }

            return body;
        }

        //public class FeatureBody
        //{
        //    public int FeatureId { get; set; }
        //    public decimal? Price { get; set; }
        //}

        /// <summary>
        /// This method is inteded to find impounds that are marked as auctions but
        /// do not currently have an auction item object prepared for use with the 
        /// auction feature flag (FeatureId=43)
        /// </summary>
        /// <param name="companyIds"></param>
        public static async Task FindAndPrepareAuctionedImpoundsByCompanyIds(int[] companyIds)
        {
            var impounds = await Towbook.Impounds.Impound.GetByCompanyAsync(companyIds, false, false, true, 0, 250, null);
            var details = Towbook.Auctions.EntryAuctionDetail.GetByDispatchEntryIds(impounds.Select(s => s.DispatchEntry.Id).ToArray()).ToList();

            foreach (var imp in impounds)
            {
                var detail = details.FirstOrDefault(f => f.DispatchEntryId == imp.DispatchEntry.Id);
                if (detail == null) {
                    detail = new Towbook.Auctions.EntryAuctionDetail()
                    {
                        DispatchEntryId = imp.DispatchEntry.Id,
                    };

                    await detail.SaveAsync(null, null);

                    await Dispatch.Entry.UpdateInAzure(imp.DispatchEntry);
                }
            }
        }


        private static async Task<FeatureBody> InternalSaveFeature(int companyId, FeatureBody body)
        {
            var cc = CompanyContract.GetExistingOrCreateNew(companyId, WebGlobal.CurrentUser.Id);

            var ccnew = new CompanyContractFeature();

            ccnew.FeatureId = body.FeatureId;
            ccnew.CompanyContractId = cc.Id;

            await ccnew.Save(companyId, WebGlobal.CurrentUser.Id);

            #region Auctions feature - prepare impounds marked as auction
            if (ccnew.FeatureId == (int)Generated.Features.Impounds_ImpoundAuctions)
            {
                var companyIds = new List<int>();

                companyIds.Add(companyId);
                companyIds.AddRange(Company.SharedCompany.GetByCompanyId(companyId).Where(o => o.SharedCompanyId != companyId).Select(o => o.SharedCompanyId));

                await FindAndPrepareAuctionedImpoundsByCompanyIds(companyIds.ToArray());
            }
            #endregion

            #region Undelete
            else if (ccnew.FeatureId == (int)Generated.Features.Undelete)
            {
                // Invalidate driver and truck caches
                var company = await Extric.Towbook.Company.Company.GetByIdAsync(companyId);
                var driversInCompany = Driver.GetByCompany(company, true);
                foreach (var d in driversInCompany)
                {
                    await d.InvalidateCompanyDriverCacheAsync();
                    await d.InvalidateRedisAsync();
                }

                var trucks = await Truck.GetByCompanyAsync(company, true);
                foreach (var t in trucks)
                {
                    t.InvalidateCache();
                }
            }
            #endregion

            #region Closed Period Accounting    
            else if (ccnew.FeatureId == (int)Generated.Features.AdvancedBilling_ClosedAccountingPeriod)
            {
                // delete company settings on disable of FF
                var company = await Company.Company.GetByIdAsync(companyId);
                await Caching.CacheWorkerUtility.UpdateCompany(company);
            }
            #endregion

            return body;
        }
    }
}
