using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.WebShared;
using Extric.Towbook.Company;
using Extric.Towbook.API.Models;
using System.Collections.ObjectModel;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    /// <summary>
    /// Exposes the Towbook Address Book for retrieving, listing, updating, creating and deleting address book entries.
    /// </summary>
    [Route("addressbook")]
    public class AddressBookController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<AddressBookEntryModel>> Get([FromQuery]int? accountId = null)
        {
            return await InternalGet((await this.GetCompaniesForRequestAsync()), accountId);
        }

        /// <summary>
        /// Retrieve a specific address book entry
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{id}")]
        public async Task<IActionResult> GetAsync(int id)
        {
            var addressBookEntry = await AddressBookEntry.GetByIdAsync(id, WebGlobal.CurrentUser.Company);

            if (addressBookEntry != null && addressBookEntry.CompanyId > 0)
            {
                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(addressBookEntry.CompanyId))
                {
                    addressBookEntry = null;
                }
            }

            if (addressBookEntry != null)
            {
                return Ok(AddressBookEntryModel.Map(addressBookEntry));
            }
            else
            {
                return NotFound("AddressBookEntry " + id + " does not exist.");
            }
        }

        /// <summary>
        /// Create a new Address Book entry
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        public async Task<AddressBookEntryModel> Post(AddressBookEntryModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include JSON content in your request?")
                });
            }

            if (model.Id > 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You shouldn't set the ID in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            var addressBookEntry = new AddressBookEntry();

            AddressBookEntryModel.Map(model, addressBookEntry);

            if (addressBookEntry.IsProblemCustomer && string.IsNullOrWhiteSpace(addressBookEntry.Phone))
                throw new TowbookException("Contacts that are marked as a problem customer require a phone number for searching reasons.  Please add a phone number.");

            addressBookEntry.CompanyId = WebGlobal.CurrentUser.CompanyId;
            await addressBookEntry.Save();

            return AddressBookEntryModel.Map(addressBookEntry);
        }

        /// <summary>
        /// Update an existing address book entry
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{id}")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        public async Task<AddressBookEntryModel> Put(int id, AddressBookEntryModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.Id < 1)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You must set the Id in a PUT request. If you're trying to create a new addressBookEntry, use the POST method instead.")
                });
            }

            if (model.Id != id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("The ID in the body of the request doesn't match the ID passed via the incoming URL.")
                });
            }

            var addressBookEntry = await AddressBookEntry.GetByIdAsync(id, WebGlobal.CurrentUser.Company);

            if (addressBookEntry == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(addressBookEntry.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("The specified addressBookEntry either doesn't exist or you don't have access to it.") });

            AddressBookEntryModel.Map(model, addressBookEntry);

            if (addressBookEntry.IsProblemCustomer && string.IsNullOrWhiteSpace(addressBookEntry.Phone))
                throw new TowbookException("Contacts that are marked as a problem customer require a phone number for searching reasons.  Please add a phone number.");

            await addressBookEntry.Save();

            return AddressBookEntryModel.Map(addressBookEntry);
        }

        /// <summary>
        /// Permanently delete an address book entry
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{id}")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var addressBookEntry = await AddressBookEntry.GetByIdAsync(id, WebGlobal.CurrentUser.Company);

            if (addressBookEntry == null)
                return new HttpResponseMessage(HttpStatusCode.OK);

            await addressBookEntry.Delete(WebGlobal.CurrentUser);

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        internal static async Task<IEnumerable<AddressBookEntryModel>> InternalGet(Company.Company[] companies = null, int? accountId = null)
        {
            var x = new Collection<AddressBookEntry>();

            if (companies == null)
            {
                companies = new Company.Company[] { WebGlobal.CurrentUser.Company };
            }

            if (accountId == null)
            {
                if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser)
                {
                    return new AddressBookEntryModel[0];
                }

                foreach(var ra in companies)
                {
                    var t = await AddressBookEntry.GetByCompanyAsync(ra);
                    x = x.Union(t).ToCollection();
                }

                //Add accounts as Address Book Entries
                var accounts = await Account.GetByCompanyAsync(WebGlobal.CurrentUser.Company, false, null);
                x = x.Union(AddressBookEntry.Map(accounts)).ToCollection();
                
            }
            else
            {
                if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser ||
                    WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountManager)
                {
                    if (accountId.Value != WebGlobal.CurrentUser.AccountId && 
                        !AccountUser.GetByUserId(WebGlobal.CurrentUser.Id).Any(o => o.AccountId == accountId.Value))
                        throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                        {
                            Content = new StringContent("You don't have access to that account.")
                        });
                }
                var account = await Account.GetByIdAsync(accountId.Value);
                if (account == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(account.CompanyId))
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified account either doesn't exist or you don't have access to it, thus no address book entries could be returned for it.")
                    });
                }

                x = await AddressBookEntry.GetByAccountIdAsync(accountId.Value);
            }


            return x.Select(o => AddressBookEntryModel.Map(o));
        }
    }
}
