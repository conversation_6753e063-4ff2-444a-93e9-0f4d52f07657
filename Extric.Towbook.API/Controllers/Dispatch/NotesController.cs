using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Integration;
using Extric.Towbook.WebShared;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;

using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.Utility;
using System.Threading.Tasks;
using System;
using Extric.Towbook.Dispatch;

namespace Extric.Towbook.API.Controllers
{
    [Route("calls")]
    public class NotesController : ControllerBase
    {
        [HttpGet("{callId}/notes")]
        public async Task<IEnumerable<NoteModel>> GetAsync(int callId)
        {
            var e = await Dispatch.Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");

            if (!WebGlobal.CurrentUser.HasPermissionToViewInternalNotes())
            {
                return Array.Empty<NoteModel>();
            }

            var list = (await Task.WhenAll((await Dispatch.EntryNote.GetByDispatchEntryIdAsync(callId)).Select(async s =>
            {
                var nm = await NoteModel.MapAsync(s);

                // never show delete for drivers
                if (WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Driver)
                    nm.ShowDelete = nm.OwnerUserId == WebGlobal.CurrentUser.Id;

                return nm;
            }))).ToCollection();

            if (CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "AllowDriversToViewInternalNotes") != "1")
            {
                if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                {
                    list = list.Where(o => o.OwnerUserId == WebGlobal.CurrentUser.Id).ToCollection();
                }
            }

            return list;
        }

        [Route("{callId}/notes/{id}")]
        [HttpGet]
        public async Task<NoteModel> GetAsync(int callId, int id)
        {
            var e = await Dispatch.Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");

            NoteModel result = await NoteModel.MapAsync(await Dispatch.EntryNote.GetByIdAsync(id));
            result.ShowDelete = result.OwnerUserId == WebGlobal.CurrentUser.Id;

            return result;
        }

        private async Task<object> PostSolver(int callId, NoteModel model) 
        {
            if (model.Id > 0)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You shouldn't set the note.id in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            if (string.IsNullOrWhiteSpace(model.Content))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NoContent)
                {
                    Content = new StringContent("You must provide the contents of the note when sending a POST request.")
                });
            }

            var e = await Dispatch.Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");

            var n = NoteModel.Map(model);
            n.OwnerUserId = WebGlobal.CurrentUser.Id;
            n.DispatchEntryId = callId;
            await n.SaveAsync();

            await PushNotificationProvider.InsertEntryNote(WebGlobal.CurrentUser.CompanyId, n.Id, n.DispatchEntryId);

            return n.Id;
        }

        [Route("{callId}/notes")]
        [HttpPost]
        [Consumes("application/x-www-form-urlencoded")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<object> PostForm(int callId, [FromForm] NoteModel model)
        {
            return await PostSolver(callId, model);
        }

        [Route("{callId}/notes")]
        [HttpPost]
        [Consumes("application/json")]
        public async Task<object> Post(int callId, [FromBody] NoteModel model)
        {
            return await PostSolver(callId, model);
        }

        [HttpDelete("{callId}/notes/{id}")]
        public async Task<HttpResponseMessage> Delete(int callId, int id)
        {
            var n = await EntryNote.GetByIdAsync(id);
            if (n != null)
            {
                if (WebGlobal.CurrentUser.Id != n.OwnerUserId)
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to delete this note.") });

                await n.DeleteAsync();

                var e = await Dispatch.Entry.GetByIdAsync(n.DispatchEntryId);
                if (e == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(e.CompanyId))
                {
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("You don't have access or permission to perform this action.")
                    });
                }

                await PushNotificationProvider.InsertEntryNote(WebGlobal.CurrentUser.CompanyId, n.Id, n.DispatchEntryId, "delete");
            }

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }
    }
}
