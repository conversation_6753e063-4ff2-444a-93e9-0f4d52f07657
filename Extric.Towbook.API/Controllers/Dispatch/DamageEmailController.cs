using System;
using System.Collections.Generic;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integrations.Email;
using Extric.Towbook.Utility;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Net.Mime;
using System.Threading.Tasks;
using Extric.Towbook.API.Models;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.API.ApiUtility;
using HttpContextHelper = Extric.Towbook.Web.HttpContext;

namespace Extric.Towbook.API.Controllers
{
    // calls/{callId}/damages/{damageReportId}/email
    [Route("calls/{callId}/damages/{damageReportId}/email")]
    public class DamageEmailController : ControllerBase
    {
        [HttpPost]
        public async Task<object> Email(int callId, int damageReportId, EmailInput emailInput)
        {
            var notifications = new List<string>();
            notifications.AddRange(emailInput.emails);

            var call = await Entry.GetByIdAsync(callId);

            if (call == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(call.CompanyId))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You don't have access to this call or it doesn't exist.")
                });
            }

            if (!WebGlobal.CurrentUser.HasPermissionToEmailCall(call))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your company is set to not allow you to email calls. Contact your supervisor/manager if you have questions regarding this.")
                });
            }

            var damageReport = EntryDamage.GetById(damageReportId);
            EntryAsset asset = null;

            foreach (var a in call.Assets)
            {
                if (a.Id == damageReport.AssetId)
                {
                    asset = a;
                    break;
                }
            }

            if (asset == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Invalid assetId " + damageReport.AssetId + ", it is not an asset of the requested call.")
                });
            }

            if (string.IsNullOrEmpty(emailInput.optionalMessage))
            {
                var company = await Company.Company.GetByIdAsync(call.CompanyId);

                emailInput.optionalMessage = "Attached is a PDF copy of the Damage Report for tow ticket (#" + call.CallNumber + ")." +
                                            "<br/><br/>" +
                                            "Vehicle: " + asset.Make + " " + asset.Model + " " + asset.Year + "<br/>" +
                                            "Location: " + call.TowSource +
                                            "<br/><br/>" +
                                            "If you have any questions, please feel free to contact us." +
                                            "<br/><br/>" +
                                            "Thank you for your business," +
                                            "<br/><br/>" +
                                            company.Name + "<br/>" + company.Phone +
                                            "<br/><br/>" + "Sent using Towbook";
            }

            if (notifications.Count > 0)
            {
                string output = "";

                foreach (string xemail in notifications)
                {
                    if (Core.IsEmailValid(xemail))
                    {
                        if (WebGlobal.CurrentUser.PrimaryCompanyId == 1376)
                            await SendEmail(damageReportId, call, asset, xemail, true, emailInput.optionalMessage);
                        else
                            await SendEmail(damageReportId, call, asset, xemail, false, emailInput.optionalMessage);

                        output += "Sent Vehicle Damage Report via email to " + xemail + "\n";
                    }
                }

                return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(output) };
            }
            else
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent("No emails or phone numbers specified.") };
            }
        }


        private static async Task SendEmail(int damageReportId,
            Entry call,
            EntryAsset asset,
            string xemail,
            bool sendFromUserInsteadOfCompany = false,
            string optionalMessage = "",
            bool sendAsFax = false)
        {
            if (string.IsNullOrWhiteSpace(xemail))
                return;

            var sendFromEmail = (sendFromUserInsteadOfCompany ? WebGlobal.CurrentUser.Email : WebGlobal.CurrentUser.Company.Email);
            var sendFromName = (sendFromUserInsteadOfCompany ? (WebGlobal.CurrentUser.FullName + "(" + WebGlobal.CurrentUser.Company + ")") : WebGlobal.CurrentUser.Company.Name);

            if (!Core.IsEmailValid(sendFromEmail))
            {
                if (sendFromUserInsteadOfCompany)
                    throw new TowbookException("User doesn't have a valid email address associated with user profile.");
                else
                    throw new TowbookException($"{WebGlobal.CurrentUser.Company} does not have a valid email address for it's profile. Go to Settings/Company to fix it.");
            }

            var assetName = asset.Make + " " + asset.Model + " " + asset.Year;


            using (var mm = new MailMessage(new MailAddress(EmailAddress.GetTowbookDotNetEmailAddressForCompany(call.CompanyId),
                (sendFromUserInsteadOfCompany ? (WebGlobal.CurrentUser.FullName + "(" + WebGlobal.CurrentUser.Company + ")") : WebGlobal.CurrentUser.Company.Name)),
                new MailAddress(xemail)))
            {
                mm.Subject = call.Company.Name.Trim() + ": Vehicle Damage Report #" + damageReportId + " / Call #" + call.CallNumber.ToString() + " / " + assetName;
                mm.ReplyToList.Add(new MailAddress(sendFromEmail, sendFromName));
                mm.IsBodyHtml = true;
                mm.Body = optionalMessage;

                // add link to public access
                var photos = EntryDamagePhoto.GetPhotosByDispatchEntryDamageId(damageReportId);
                if (photos.Count > 0)
                {
                    string hash = EncryptionHelper.ToHashId(damageReportId);
                    string link = "http://app.towbook.com/PublicAccess/DamagePhotos.aspx?id=" + hash;
                    string md5 = Extric.Towbook.Core.ProtectId(damageReportId, call.OwnerUserId);

                    link += "&sc=" + md5;

                    if (photos.Count > 0)
                        mm.Body += "<br/><br/>To view the photos of this job, visit: <a href=" + link + ">" + link + "</a>";
                    else
                        mm.Body += "<br/><br/>To check for photos of this job, visit: <a href=" + link + ">" + link + "</a>";

                }

                // get html contents
                int statusCode = 0;
                var url = WebGlobal.GetAppDomain() + "/dispatch/vdr.aspx?id=" + damageReportId;
                var html = WebGlobal.GetResponseFromUrl(url, out statusCode, false, false, WebGlobal.CookieDomain);

                if (statusCode != 200)
                {
                    throw new Exception("Error retrieving Damage Form. HttpStatusCode:" + statusCode + ", Response:" + html);
                }

                // generate pdf
                using (var ms = await GeneratePdf(html, noMarginBottom: true, request: HttpContextHelper.Current.Request))
                {
                    ms.Position = 0;

                    ContentType ct = new ContentType(MediaTypeNames.Application.Pdf);
                    Attachment attach = new Attachment(ms, ct);
                    var filename = String.Format("VehicleDamageReport_{0}_Call#{1} - {2} {3} {4}.pdf",
                        damageReportId,
                        call.CallNumber,
                        asset.Year,
                        asset.Make,
                        asset.Model);

                    attach.ContentDisposition.FileName = filename;

                    mm.Attachments.Add(attach);

                    using (SmtpClient smtp = new SmtpClient().Get())
                    {
                        await smtp.SendMailAsync(mm);
                    }
                }
            }
        }
    }

    public sealed class EmailInput
    {
        public string[] emails { get; set; }
        public int[] files { get; set; }
        public string optionalMessage { get; set; }
        public string optionalSubject { get; set; }
    }
}
