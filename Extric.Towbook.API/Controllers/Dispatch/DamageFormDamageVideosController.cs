using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.API.Models;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Storage;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Controllers
{
    [Route("calls")]
    public class DamageFormVideosController : ControllerBase
    {
        /// <summary>
        /// Retrieve a list of videos associated with the specified <paramref name="damageId"/>.
        /// api/calls/{callId]/damageForms/{damageId}/videos
        /// </summary>
        /// <param name="callId">The call/entry number to which the videos belongs to</param>
        /// <param name="damageId">The damage form number you want to collect the videos for</param>
        /// <returns>List of Videos for the specified damage form</returns>
        /// 
        //routes.MapHttpRoute(
        //    name: "CallDamageFormVideos",
        //    routeTemplate: "calls/{callId}/damageForms/{damageId}/videos/{videoId}",
        //    defaults: new { controller = "DamageFormVideos", videoId = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpGet]
        [Route("{callId}/damageForms/{damageId}/videos")]
        public async Task<IEnumerable<DamageVideoModel>> Get(int callId, int damageId)
        {
            var returns = new List<DamageVideoModel>();

            var e = await Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Video");

            var d = EntryDamage.GetById(damageId);
            if (d == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage Form with id <damageFormId> = {damageId} does not exist.")
                });
            }

            foreach (var Video in EntryDamageVideo.GetByDispatchEntryDamageId(damageId))
            {
                string path = await FileUtility.GetFileAsync(Video.Location.Replace("%1", e.CompanyId.ToString()).Replace("%2", e.Id.ToString()));

                if (path != null)
                {
                    returns.Add(DamageVideoModel.Map(Video, e.CompanyId, e.Id));
                }
            }

            return returns;
        }

        /// <summary>
        /// Retrieve a list of videos associated with the specified <paramref name="regionId"/>.
        /// api/calls/{callId]/damageForms/{damageId}/damages/{regionId}/videos
        /// </summary>
        /// <param name="callId">The call/entry number to which the videos belongs to</param>
        /// <param name="damageId">The damage form number you want to collect the videos for</param>
        /// <param name="regionId">The damage region that you want to collect the vidoes from</param>
        /// <returns>List of Videos for the specified damage form</returns>
        /// 
        //routes.MapHttpRoute(
        //    name: "CallDamageFormDamageVideos",
        //    routeTemplate: "calls/{callId}/damageForms/{damageId}/damages/{regionId}/videos/{videoId}",
        //    defaults: new { controller = "DamageFormVideos", videoId = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpGet]
        [Route("{callId}/damageForms/{damageId}/damages/{regionId}/videos/{videoId?}")]
        public async Task<IEnumerable<DamageVideoModel>> Get(int callId, int damageId, int regionId)
        {
            List<DamageVideoModel> returns = new List<DamageVideoModel>();

            var e = await Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Video");

            var d = Dispatch.EntryDamage.GetById(damageId);
            if (d == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage Form with id <damageFormId> = {damageId} does not exist.")
                });
            }

            foreach (var video in EntryDamageVideo.GetByDispatchEntryDamageRegionId(damageId, regionId))
            {
                string path = await FileUtility.GetFileAsync(video.Location.Replace("%1", e.CompanyId.ToString()).Replace("%2", e.Id.ToString()));

                if (path != null)
                {
                    returns.Add(DamageVideoModel.Map(video, e.CompanyId, e.Id));
                }
            }

            return returns;
        }

        /// <summary>
        /// Upload a Video for a damage form
        /// api/calls/{callId}/damageForms/{damageId}/videos
        /// </summary>
        /// <param name="damageId">The damage form that the Video is associated with.</param>
        /// <param name="model"></param>
        /// <returns>If successful, returns HTTP Status 201. 
        /// 
        /// Returns a JSON object equilivant to calling DamageFormDamageVideosController.Get(id). 
        /// </returns>
        /// 
        //routes.MapHttpRoute(
        //    name: "CallDamageFormVideos",
        //    routeTemplate: "calls/{callId}/damageForms/{damageId}/videos/{videoId}",
        //    defaults: new { controller = "DamageFormVideos", videoId = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpPost]
        [Route("{callId}/damageForms/{damageId}/videos/{videoId?}")]
        public async Task<VideoUploadResponseModel> PostAsync(int damageId, VideoUploadModel model)
        {
            return await PostAsync(damageId, null, model);
        }


        /// <summary>
        /// Upload a Video for a damage form region
        /// api/calls/{callId}/damageForms/{damageId}/damages/{regionId}/videos
        /// </summary>
        /// <param name="damageId">The damage form that the Video is associated with.</param>
        /// <param name="regionId">The damage form region that the Video is associated with.</param>
        /// <param name="model"></param>
        /// <returns>If successful, returns HTTP Status 201. 
        /// 
        /// Returns a JSON object equilivant to calling DamageFormDamageVideosController.Get(id). 
        /// </returns>
        /// 
        //routes.MapHttpRoute(
        //    name: "CallDamageFormDamageVideos",
        //    routeTemplate: "calls/{callId}/damageForms/{damageId}/damages/{regionId}/videos/{videoId}",
        //    defaults: new { controller = "DamageFormVideos", videoId = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };

        [HttpPost]
        [Route("{callId}/damageForms/{damageId}/damages/{regionId}/videos/{videoId?}")]
        public async Task<VideoUploadResponseModel> PostAsync(int damageId, int? regionId, VideoUploadModel model)
        {
            var u = WebGlobal.CurrentUser;
            var d = Dispatch.EntryDamage.GetById(damageId);

            if (d == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage Form with id <damageFormId> = {damageId} does not exist.")
                });
            }

            var e = await Entry.GetByIdAsync(d.DispatchEntryId);

            if (e == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(e.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("Either the damage form Videos you tried to access does not exist, or you don't have access to view it.") });

            if (model.ContentType != "video/mp4")
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Invalid mime type. Only video/mp4 is supported currently.")
                });

            var p = new EntryDamageVideo();
            p.DispatchEntryDamageId = damageId;
            p.DispatchEntryDamageRegionId = regionId > 0 ? regionId : (int?)null;
            p.ContentType = model.ContentType;
            p.Description = model.Description;
            p.OwnerUserId = u.Id;
            p.RemoteIp = this.GetRequestingIp();
            p.FileSize = model.SizeBytes;
            p.Latitude = RecentLatitude();
            p.Longitude = RecentLongitude();

            p.Save(WebGlobal.CurrentUser);

            return new VideoUploadResponseModel()
            {
                VideoUrl = FileUtility.GetPresignedUrlForUploadFromClient(
                    p.Location.Replace("%1", e.CompanyId.ToString()).Replace("%2", e.Id.ToString()), model.ContentType, 10),
                ThumbnailUrl = FileUtility.GetPresignedUrlForUploadFromClient(
                    p.ThumbnailLocation.Replace("%1", e.CompanyId.ToString()).Replace("%2", e.Id.ToString()), "image/jpg", 10),
            };
        }
    }
}
