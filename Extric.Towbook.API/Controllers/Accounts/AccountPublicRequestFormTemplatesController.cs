using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models.Accounts;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static Extric.Towbook.API.ApiUtility;
using static Extric.Towbook.Utility.SqlBuilder;

namespace Extric.Towbook.API.Controllers
{
    [Route("accountPublicRequestFormTemplates")]
    public class AccountPublicRequestFormTemplatesController : ControllerBase
    {
        [Route("")]
        [HttpGet]
        public async Task<IEnumerable<AccountPublicRequestFormTemplateModel>> Get()
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Accounts_AdvancedWebForms);

            var templates = AccountPublicRequestFormTemplate.GetByCompanyIds(new int[] { WebGlobal.CurrentUser.PrimaryCompanyId });
            var links = AccountPublicRequestLink.GetByTemplateIds(templates.Select(s => s.Id).ToArray());
            var accounts = await Account.GetByIdsAsync(links.Select(s => s.AccountId).ToArray());

            return await Task.WhenAll(
                templates
                .Select(async o =>
                    await AccountPublicRequestFormTemplateModel.MapAsync(
                        o,
                        accounts.Where(w => links.Any(a => a.AccountId == w.Id && a.TemplateId == o.Id))
                    ))
            );
        }

        [Route("{id}")]
        [Route("{id}/get")]
        [HttpGet]
        public async Task<AccountPublicRequestFormTemplateModel> Get(int id)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Accounts_AdvancedWebForms);

            var template = AccountPublicRequestFormTemplate.GetById(id);

            await ThrowIfNoCompanyAccessAsync(template?.CompanyId, "template");

            var setting = AccountPublicRequestFormTemplateSetting.GetByTemplateId(template.Id);
            var templateReasons = AccountPublicRequestFormTemplateReason.GetByTemplateIds(new[] { template.Id });
            var links = AccountPublicRequestLink.GetByTemplateIds(new[] {template.Id });
            var accounts = await Account.GetByIdsAsync(links.Select(s => s.AccountId).ToArray());
            var reasons = (await Dispatch.Reason.GetByCompany(WebGlobal.CurrentUser.Company)).Where(w => templateReasons.Any(a => a.ReasonId == w.Id));
            var defaultSections = AccountPublicRequestFormTemplateModel.DefaultSections();
            var customValues = AccountPublicRequestFormTemplateCustomField.GetByTemplateId(template.Id);
            var disclaimers = AccountPublicRequestFormTemplateDisclaimer.GetByTemplateIds(new[] { template.Id });
            var operationalHours = await AccountPublicRequestFormTemplateOfficeHour.GetByTemplateId(template.Id);

            var attributes = (await Dispatch.Attribute.GetByCompanyAsync(WebGlobal.CurrentUser.Company))
                .Where(w => customValues.Any(a => a.DispatchEntryAttributeId == w.Id));

            var model = await AccountPublicRequestFormTemplateModel.MapAsync(
                template,
                accounts.Where(w => links != null && links.Any(a => a.AccountId == w.Id && a.TemplateId == template.Id)),
                reasons,
                setting,
                defaultSections,
                disclaimers,
                operationalHours
            );

            // add assigned custom fields to "other" section
            if (attributes != null)
            {
                var customFields = attributes.Select(s => new TemplateFieldModel()
                {
                    AttributeId = s.Id,
                    Name = s.Name,
                    DefaultValue = customValues?.FirstOrDefault(f => f.DispatchEntryAttributeId == s.Id)?.DefaultValue,
                    Option = TemplateFieldOptionModel.Map(customValues?.FirstOrDefault(f => f.DispatchEntryAttributeId == s.Id)?.IncludeOption ?? 0),
                    FieldType = TemplateFieldOptionModel.MapAttributeType(s.Type)
                });

                if (model.Sections == null)
                    model.Sections = Array.Empty<TemplateSectionModel>();

                var otherSection = model.Sections
                                    .FirstOrDefault(f => f.Name.Equals("Other Information")) ??  new TemplateSectionModel() { 
                                        Name = "Other Information", 
                                        Fields = new TemplateFieldModel[0] 
                                    };

                otherSection.Fields = otherSection.Fields.Union(customFields).ToArray();
            }

            return model;
        }

        [Route("")]
        [HttpPost]
        public async Task<object> Post(AccountPublicRequestFormTemplateModel model)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Accounts_AdvancedWebForms);

            if (model.Id > 0)
            {
                return new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent(
                        "Id was provided.  Did you mean to do a PUT request?")
                };
            }

            if (string.IsNullOrEmpty(model.Name))
            {
                return new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent(
                        "A template name must be provided.  Please enter a name to continue.")
                };
            }

            var t = new AccountPublicRequestFormTemplate()
            {
                Name = Newtonsoft.Json.JsonConvert.DeserializeObject<string>(model.Name.ToJson()),
                CompanyId = WebGlobal.CurrentUser.CompanyId,
                IsEnabled = model.IsEnabled,
                LayoutId = (TemplateLayoutType)(model.Layout?.Id ?? 0)
            };

            t.Save(WebGlobal.CurrentUser);

            model.Id = t.Id;
            model.CompanyId = t.CompanyId;

            await Put(t.Id, model);

            return Created(model.Id.ToString(), new { model.Id });
        }

        [Route("")]
        [HttpPut]
        public async Task<ActionResult> Put(int id, AccountPublicRequestFormTemplateModel model)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Accounts_AdvancedWebForms);

            var template = AccountPublicRequestFormTemplate.GetById(id);
            
            await ThrowIfNoCompanyAccessAsync(template?.CompanyId, "template");

            var setting = AccountPublicRequestFormTemplateSetting.GetByTemplateId(template.Id);

            #region sections and fields
            setting = AccountPublicRequestFormTemplateModel.Map(model, setting);
            #endregion

            setting.AccountPublicRequestFormTemplateId = template.Id;
            setting.WelcomeEmailBody = model.WelcomeEmailBody;
            setting.Save();

            var saveTemplate = false;
            
            if (template.Name != model.Name)
            {
                template.Name = model.Name;
                saveTemplate = true;
            }

            if(model.Layout?.Id >= 0 && template.LayoutId != (TemplateLayoutType)model.Layout.Id)
            {
                template.LayoutId = (TemplateLayoutType)model.Layout.Id;
                saveTemplate = true;
            }

            if(model.IsEnabled != template.IsEnabled)
            {
                template.IsEnabled = model.IsEnabled;
                saveTemplate = true;
            }

            if (template.AccountPublicRequestFormTemplateSettingId == 0)
            {
                template.AccountPublicRequestFormTemplateSettingId = setting.Id;
                saveTemplate = true;
            }

            if (saveTemplate)
                template.Save(WebGlobal.CurrentUser);

            #region Accounts
            var accounts = await Account.GetByIdsAsync(model.Accounts.Select(s => s.Id).ToArray());
            var templateLinks = AccountPublicRequestLink.GetByTemplateIds(new[] { template.Id });
            var accountLinks = AccountPublicRequestLink.GetByAccountIds(model.Accounts.Select(s => s.Id).ToArray());

            // delete links that are not assigned
            foreach(var link in templateLinks.Where(a => !model.Accounts.Select(s => s.Id).ToCollection().Contains(a.AccountId)))
            {
                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(link.CompanyId))
                    continue;

                link.TemplateId = null;
                link.Save();
            }

            // create new links and assign to template Id
            foreach(var account in model.Accounts)
            {
                var acc = accounts.FirstOrDefault(f => f.Id == account.Id);

                if (acc == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(acc.CompanyId))
                    continue;

                var link = accountLinks
                    .FirstOrDefault(f => f.AccountId == account.Id) ??
                    new AccountPublicRequestLink() {
                        CompanyId = acc.CompanyId,
                        AccountId = acc.Id,
                        OwnerUserId = WebGlobal.CurrentUser.Id,
                        CreateDate = DateTime.Now
                    };

                link.TemplateId = template.Id;
                
                link.Save();
            }
            #endregion

            #region Reasons
            var templateReasons = AccountPublicRequestFormTemplateReason.GetByTemplateIds(new[] { template.Id });

            // delete all
            foreach (var reason in templateReasons)
                reason.Delete();
            
            // assign new reasons
            foreach(var reason in model.Reasons)
            {
                var r = await Dispatch.Reason.GetByIdAsync(reason.Id);
                if (r == null || (r.CompanyId > 1 && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(r.CompanyId)))
                    continue;

                var tr = new AccountPublicRequestFormTemplateReason()
                {
                    TemplateId = template.Id,
                    ReasonId = r.Id
                };

                tr.Save();
            }
            #endregion

            #region custom fields
            var templateCustomFields = AccountPublicRequestFormTemplateCustomField.GetByTemplateId(template.Id);

            foreach (var cf in templateCustomFields)
                cf.Delete();

            var customFieldModels = model.Sections.FirstOrDefault(f => f.Name == "Custom Fields")?.Fields;
            if(customFieldModels != null)
            {
                var companyAttributes = await Dispatch.Attribute.GetByCompanyAsync(WebGlobal.CurrentUser.Company);

                foreach (var customField in customFieldModels)
                {
                    var attribute = companyAttributes.FirstOrDefault(f => f.Id == customField.AttributeId.GetValueOrDefault());
                    if (attribute == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(attribute.CompanyId))
                        continue;

                    var defaultOption = DefaultValueOption.Unspecified;

                    if (!string.IsNullOrEmpty(customField.DefaultValue))
                    {
                        if (customField.Option?.Id == (int)SettingOption.Unspecified)
                            customField.Option.Id = (int)SettingOption.Hidden;

                        defaultOption = DefaultValueOption.SpecifyDefault;
                    }

                    if (customField.Option?.Id > (int)SettingOption.Unspecified)
                    {
                        var cf = new AccountPublicRequestFormTemplateCustomField()
                        {
                            AccountPublicRequestFormTemplateId = template.Id,
                            DispatchEntryAttributeId = customField.AttributeId.Value,
                            IncludeOption = (SettingOption)customField.Option.Id,
                            DefaultOption = defaultOption,
                            DefaultValue = customField.DefaultValue
                        };

                        cf.Save();
                        
                    }
                }
            }
            #endregion

            #region disclaimer

            var disclaimers = AccountPublicRequestFormTemplateDisclaimer.GetByTemplateIds(new[] { template.Id });

            if(model.Disclaimer != null || (model.Disclaimer?.Id > 0 && string.IsNullOrEmpty(model.Disclaimer?.Disclaimer)))
            {
                var existing = disclaimers?.Where(w => w.Id == model.Disclaimer?.Id)?.FirstOrDefault();

                var d = new AccountPublicRequestFormTemplateDisclaimer()
                {
                    AccountPublicRequestFormTemplateId = template.Id
                };

                if (existing != null)
                {
                    if (string.IsNullOrEmpty(model.Disclaimer?.Disclaimer) || (model.Disclaimer?.Id > 0 && existing.Disclaimer != model.Disclaimer.Disclaimer))
                    {
                        // delete to keep existing disclaimer text for historical uses.
                        await existing.Delete(WebGlobal.CurrentUser);
                    }
                    else
                    {
                        d = existing;
                    }
                }

                // assign new disclaimer text
                d.Disclaimer = Core.HtmlEncode(model.Disclaimer?.Disclaimer ?? String.Empty);

                // should only be one, but delete all previous disclaimers.
                foreach (var disc in disclaimers.Where(w => existing == null || w != existing))
                {
                    await disc.Delete(WebGlobal.CurrentUser);
                }

                // save disclaimer
                if(!string.IsNullOrEmpty(d.Disclaimer))
                {
                    await d.Save(WebGlobal.CurrentUser);
                }
            }
            #endregion

            #region Operational Hours

            if(model.OperationalHours.Any())
            {
                // delete all pervious
                AccountPublicRequestFormTemplateOfficeHour.Delete(template.Id);

                foreach(var oh in model.OperationalHours)
                {
                    if (oh.StartTime == null || oh.EndTime == null)
                        continue;

                    if (Enum.TryParse<DayOfTheWeek>(oh.DayOfWeekId.ToString(), out var day))
                    {
                        var operationDay = new AccountPublicRequestFormTemplateOfficeHour()
                        {
                            AccountPublicRequestFormTemplateId = template.Id,
                            DayOfWeek = day,
                            StartTime = oh.StartTime.TimeOfDay,
                            EndTime = oh.EndTime.TimeOfDay
                        };

                        await operationDay.Save();
                    }
                }
            }

            #endregion

            return NoContent();
        }


        [Route("{id}")]
        [Route("{id}/get")]
        [HttpDelete]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Accounts_AdvancedWebForms);

            var template = AccountPublicRequestFormTemplate.GetById(id);

            await ThrowIfNoCompanyAccessAsync(template?.CompanyId, "template");

            // unassign account links from the template
            var links = AccountPublicRequestLink.GetByTemplateIds(new[] { template.Id });
            foreach(var link in links)
            {
                link.TemplateId = null;
                link.Save();
            }

            // remove any reasons
            var templateReasons = AccountPublicRequestFormTemplateReason.GetByTemplateIds(new[] { template.Id });
            foreach (var r in templateReasons)
                r.Delete();

            // remove any custom fields
            var customFields = AccountPublicRequestFormTemplateCustomField.GetByTemplateId(template.Id);
            foreach (var cf in customFields)
                cf.Delete();

            // should only be one, but delete all template disclaimers.
            var disclaimers = AccountPublicRequestFormTemplateDisclaimer.GetByTemplateIds(new[] { template.Id });
            foreach (var d in disclaimers)
                await d.Delete(WebGlobal.CurrentUser);

            // delete it
            template.Delete(WebGlobal.CurrentUser);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }
    }
}
