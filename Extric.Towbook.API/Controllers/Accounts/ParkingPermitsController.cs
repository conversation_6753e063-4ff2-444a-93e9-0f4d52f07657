using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.WebShared;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using System;
using System.Collections.ObjectModel;
using Extric.Towbook.API.Models;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.ActivityLogging;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Accounts.Controllers
{

    [Route("accounts/{accountId}/parkingpermits")]
    public class ParkingPermitsController : ControllerBase
    {
        /// <summary>
        /// Retrieve a list of parking permits associated with <paramref name="accountId"/>.
        /// </summary>
        [Route("")]
        [HttpGet]
        public async Task<object> Get(int accountId, [FromQuery] ParkingPermitRequestQuery request)
        {
            if (accountId == 1 || accountId == 0)
                return null;

            await ThrowIfFeatureNotAssignedAsync(Generated.Features.ParkingPermits);

            var acc = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(acc?.CompanyId, "Account");

            Collection<ParkingPermit> ret = null;

            var statusIds = request.StatusIds?.ToList() ?? new List<int>();

            if (!string.IsNullOrWhiteSpace(request.Plate))
            {
                return await GetByPlate(accountId, request.Plate);
            }
            else if (request.ListId != null)
            {
                ret = ParkingPermit.GetByListId(accountId, request.ListId.Value).ToCollection();

                if (statusIds.Any())
                    ret = ret.Where(w => w.StatusId != null && statusIds.Contains(w.StatusId.Value)).ToCollection();
            }
            else if (statusIds.Any())
            {
                ret = ParkingPermit.GetByStatusIds(accountId, statusIds.ToArray(), request.Page, request.PageSize).ToCollection();
            }
            else
            {
                ret = ParkingPermit.GetByAccountId(accountId).ToCollection();
            }

            if (ret == null)
                return new object[0];

            var insights = ParkingPermitInsightHelper.GetByParkingPermitIds(ret.Select(z => z.Id).ToArray());
            var addresses = AccountParkingPermitAddress.GetByParkingPermitIds(ret.Select(z => z.Id).ToArray());

            var list = new List<ParkingPermitModel>();
            foreach (var item in ret)
            {
                list.Add(await ParkingPermitModel.Map(item, insights, addresses));
            }

            return list.ToCollection();
        }

        /// <summary>
        /// Retrieve parking permits for the specified account with an explicit parking permit ID.
        /// </summary>
        /// <param name="accountId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        //routes.MapHttpRoute(
        //    name: "AccountParkingPermits_actions",
        //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/{action}",
        //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Get" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}")]
        [Route("{id}/get")]
        [HttpGet]
        public async Task<ParkingPermitModel> Get(int accountId, int id)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.ParkingPermits);

            var x = await Account.GetByIdAsync(accountId);
            var d = ParkingPermit.GetById(id);

            await ThrowIfNoCompanyAccessAsync(x?.Companies);
            if (d?.AccountId != x.Id)
                d = null;

            if (d?.Deleted == true)
                d = null;

            ThrowIfNotFound(d, "permit");            
            
            var insights = ParkingPermitInsightHelper.GetByParkingPermitIds(new[] { d.Id });
            var addresses = AccountParkingPermitAddress.GetByParkingPermitIds(new[] { d.Id });

            var p = await ParkingPermitModel.Map(d, insights, addresses);

            return p;
        }
        
        private static async Task<object> GetByPlate(int accountId, string plate)
        {
            if (accountId == 0 || accountId == 1)
                return null;

            if (string.IsNullOrWhiteSpace(plate))
                return null;
                        
            var x = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(x?.Companies);
            
            var d = ParkingPermit.GetByLicensePlate(accountId, plate); //.Where(o => o.StatusId == PermitStatus.Valid.Id); -- not all valid permits have a status of Valid...

            var list = new List<ParkingPermitModel>();
            foreach(var ppm in d)
            {
                list.Add(await ParkingPermitModel.Map(ppm));
            }

            var p = list
                .OrderBy(a => a.StatusId == PermitStatus.Valid.Id) // float valid status to the bottom
                .ThenBy(a => a.StatusId == PermitStatus.Invalid.Id) // float waiting for approvals next to the bottom
                .ThenBy(o => o.Id) // then by latest created
                .LastOrDefault();

            if (p == null)
                return null;

            return new
            {
                p.Id,
                p.AccountId,
                p.Addresses,
                p.AvailableActions,
                p.CellPhone,
                p.CreateDate,
                p.CustomPermitNumber,
                p.DecalColorId,
                p.Email,
                p.ExpirationDate,
                p.FullName,
                p.Insights,
                p.IsHandicap,
                p.LeaseEndDate,
                p.ListId,
                p.Notes,
                p.OwnerUserId,
                p.Plate,
                p.PlateState,
                p.SpaceNumber,
                p.StatusId,
                p.StatusName,
                StatusMessage = ParkingPermitModel.GetPermitStatusMessage(p),
                p.UnitNumber,
                p.VehicleColor,
                p.VehicleMake,
                p.VehicleModel,
                p.VehicleYear,
                p.VIN
            };
        }

        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitsFind",
        //    routeTemplate: "accounts/{accountId}/parkingpermits/find",
        //    defaults: new { controller = "ParkingPermits", action = "Find" })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("find")]
        [HttpGet]
        public async Task<Collection<ParkingPermitModel>> Find(int accountId, [FromQuery] ParkingPermitSearchQueryModel q, [FromQuery] int? pageNumber)
        {
            ThrowIfNotFound(q, "Parking Permit Search Query");

            var x = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(x?.Companies);

            var list = ParkingPermit.Find(
                new int[] { accountId }.ToArray(),
                q.ListIds,
                q.StatusIds,
                q.CustomPermitNumber,
                q.UnitNumber,
                q.LicensePlate,
                q.LicenseState,
                q.CellPhone,
                q.Email,
                q.FullName,
                q.VehicleYear,
                q.VehicleMake,
                q.VehicleModel,
                q.VIN,
                null, pageNumber);


            var list2 = new List<ParkingPermitModel>();
            foreach(var item in list)
            {
                list2.Add(await ParkingPermitModel.Map(item));
            }

            return list2.ToCollection();
        }

        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitsSearch",
        //    routeTemplate: "accounts/{accountId}/parkingpermits/search",
        //    defaults: new { controller = "ParkingPermits", action = "Search" })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("search")]
        [HttpGet]
        public async Task<Collection<ParkingPermitModel>> Search(int accountId,
            [FromQuery] string qs,
            [FromQuery] int? pageNumber = 1,
            [FromQuery] int? pageSize = 50)
        {
            if (accountId < 2)
                return new Collection<ParkingPermitModel>();

            var x = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(x?.Companies);

            if(!Web.HttpContext.Current.Request.Query.ContainsKey("qs") ||
                string.IsNullOrEmpty(Web.HttpContext.Current.Request.Query["qs"]))
                return new Collection<ParkingPermitModel>();

            var search = Web.HttpContext.Current.Request.Query["qs"];

            List<ParkingPermit> newList = new List<ParkingPermit>();

            if (int.TryParse(search, out var searchInt))
            {
                newList = ParkingPermit.Search(accountId, null, searchInt, pageNumber.GetValueOrDefault(1), pageSize.GetValueOrDefault(50)).ToList();
            }
            else
            {
                newList = ParkingPermit.Search(accountId, search, null, pageNumber.GetValueOrDefault(1), pageSize.GetValueOrDefault(50)).ToList();
            }

            var list2 = new List<ParkingPermitModel>();

            foreach (var item in newList)
            {
                list2.Add(await ParkingPermitModel.Map(item));
            }


            // Return approval info for any items needing approval
            var items = ParkingPermitApprovalSessionItem.GetByPermitIds(list2.Select(s => s.Id).ToArray());
            if (items != null)
            {
                foreach (var m in list2.Where(w => items.Any(a => a.ParkingPermitId == w.Id)))
                {
                    m.ApprovalSessionId = items.FirstOrDefault(f => f.ParkingPermitId.GetValueOrDefault() == m.Id)?.ApprovalSessionId;
                    m.ApprovalSessionOrderItemIds = items.Where(w => w.ParkingPermitId == m.Id).Select(s => s.Id);
                }
            }


            return list2.ToCollection();
        }

        /// <summary>
        /// Create a new parking permit.
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "AccountParkingPermits_POST",
        //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/{action}",
        //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id?}")]
        [Route("{id?}/post")]
        [HttpPost]
        public async Task<ParkingPermitModel> Post(int accountId, ParkingPermitModel model)
        {
            var act = await Account.GetByIdAsync(accountId);

            await ThrowIfNoCompanyAccessAsync(act?.CompanyId);

            var pp = ParkingPermitModel.Map(model);

            if (!ValidateParkingPermit(pp, model.Addresses, WebGlobal.CurrentUser.CompanyId, accountId, out var message))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent(message)
                });
            }

            // validate that no other permit already exists with license plate, vin, 
            // custom permit number, and unit number
            if (!ParkingPermit.ValidateAgainstDuplicatePermit(pp, out var message2))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent($"{message2}")
                });
            }


            pp.StatusId = PermitStatus.Valid.Id;
            pp.AccountId = act.Id;
            await pp.Save(WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp());

            // save addresses
            if (model.Addresses != null && model.Addresses.Any())
            {
                // save each address
                foreach (var address in model.Addresses)
                {
                    var ppa = ParkingPermitAddressModel.Map(address);
                    ppa.ParkingPermitId = pp.Id;
                    ppa.Save(WebGlobal.CurrentUser);
                }
            }

            return await ParkingPermitModel.Map(pp);
        }

        /// <summary>
        /// Update a parking permit.
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "AccountParkingPermits_PUT",
        //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}",
        //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Put" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}")]
        [HttpPut]
        public async Task<ParkingPermitModel> Put(int accountId, int id, ParkingPermitModel model)
        {
            var act = await Account.GetByIdAsync(accountId);
            var pp = ParkingPermit.GetById(id);

            if (act == null || pp == null || pp.AccountId != act.Id || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(act.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));
            
            pp = ParkingPermitModel.Map(model, pp);

            await pp.Save(WebGlobal.CurrentUser, await this.GetCurrentTokenAsync(), this.GetRequestingIp());

            // update addresses
            if (model.Addresses.Count > 0)
            {
                var current = AccountParkingPermitAddress.GetByParkingPermitId(pp.Id).ToCollection();

                // delete missing
                foreach (var address in current.Where(w => !model.Addresses.Select(s => s.Id).Contains(w.Id)))
                    address.Delete(WebGlobal.CurrentUser);

                // save each address
                foreach (var address in model.Addresses)
                {
                    AccountParkingPermitAddress orig = null;
                    if (address.Id > 0)
                        orig = AccountParkingPermitAddress.GetById(address.Id);

                    var ppa = ParkingPermitAddressModel.Map(address, orig);
                    ppa.ParkingPermitId = pp.Id;
                    ppa.Save(WebGlobal.CurrentUser);
                }
            }

            var addresses = AccountParkingPermitAddress.GetByParkingPermitId(pp.Id).ToCollection();

            return await ParkingPermitModel.Map(pp, null, addresses);
        }

        //routes.MapHttpRoute(
        //    name: "AccountParkingPermits_POST",
        //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/{action}",
        //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}/revoke")]
        [HttpPost]
        public async Task<HttpResponseMessage> Revoke(int accountId, int id)
        {
            var a = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(a?.CompanyId, "permits");

            var pp = ParkingPermit.GetById(id);
            if (pp?.AccountId != pp.AccountId)
                pp = null;

            ThrowIfNotFound(pp, "permit");

            await pp.Revoke(WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp());

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        //routes.MapHttpRoute(
        //    name: "AccountParkingPermits_POST",
        //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/{action}",
        //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}/unrevoke")]
        [HttpPost]
        public async Task<HttpResponseMessage> Unrevoke(int accountId, int id)
        {
            var a = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(a?.CompanyId, "permits");

            var pp = ParkingPermit.GetById(id);

            if (pp?.AccountId != pp.AccountId)
                pp = null;

            ThrowIfNotFound(pp, "permit");

            await pp.Unrevoke(WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp());

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        //routes.MapHttpRoute(
        //    name: "AccountParkingPermits_POST",
        //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/{action}",
        //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}/extend")]
        [HttpPost]
        public async Task<HttpResponseMessage> Extend(int accountId, int id, ExtendPermitModel model)
        {
            var a = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(a?.CompanyId, "permits");

            var pp = ParkingPermit.GetById(id);

            if (pp?.AccountId != pp.AccountId)
                pp = null;

            ThrowIfNotFound(pp, "permit");

            if (pp.ExpirationDate == null)
                pp.ExpirationDate = pp.CreateDate;

            if (model.ExtentionDate < pp.ExpirationDate.Value)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent($"The new extended date must be greater than {WebGlobal.OffsetDateTime(pp.ExpirationDate.Value, true, WebGlobal.CurrentUser.Company).ToString("G")}")
                });
            }

            await pp.Extend(model.ExtentionDate, WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp());

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        /// <summary>
        /// Deletes a parking permit associated with an account.
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "AccountParkingPermits_DELETE",
        //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}",
        //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "Delete" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "Delete" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}")]
        [HttpDelete]
        public async Task<HttpResponseMessage> Delete(int accountId, int id)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.SystemAdministrator)
            {
                var e = await Account.GetByIdAsync(accountId);
                var p = ParkingPermit.GetById(id);

                if (e == null || p == null)
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(e.CompanyId) || e.Id != p.AccountId)
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("You don't have access to that permit.")
                    });

                if (WebGlobal.CurrentUser.AccountId > 0 && 
                    (accountId != WebGlobal.CurrentUser.AccountId &&
                    p.AccountId != WebGlobal.CurrentUser.AccountId))
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented)
                    {
                        Content = new StringContent("You don't have access to that permit.")
                    });
                }

                await p.Delete(WebGlobal.CurrentUser);

                return new HttpResponseMessage(HttpStatusCode.NoContent);
            }
            else
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden));
            }
        }

        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitHistory",
        //    routeTemplate: "accounts/{accountId}/parkingPermits/{id}/history",
        //    defaults: new { controller = "ParkingPermits", id = RouteParameter.Optional, action = "History" })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}/history")]
        [HttpGet]
        public async Task<IEnumerable<AccountParkingPermitHistoryModel>> History(int accountId, int id)
        {
            var acc = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(acc?.CompanyId, "Account");

            var permit = ParkingPermit.GetById(id);

            if (permit == null || permit.AccountId != acc.Id)
                ThrowIfNotFound(null, "permit");

            var items = await ActivityLogCloudService.GetByObject(ActivityLogType.ParkingPermit, permit.Id);
            foreach (var x in items)
                x.OffsetDatesToCompanyLocal(WebGlobal.CurrentUser.Company);

            return items.Select(s => AccountParkingPermitHistoryModel.Map(s));
        }

        private bool ValidateParkingPermit(ParkingPermit pp, IEnumerable<ParkingPermitAddressModel> addresses, int companyId, int? accountId, out string message)
        {
            var pps = ParkingPermitSetting.GetByCompanyId(companyId, accountId);
            message = string.Empty;

            if (pps == null) 
                return true;

            if (pps.RequireVin && string.IsNullOrWhiteSpace(pp.VIN))
            {
                message = "The VIN number is required to create a parking permit.";
                return false;
            }

            if (pps.RequireColor && string.IsNullOrWhiteSpace(pp.VehicleColor))
            {
                message = "The vehicle color is required to create a parking permit.";
                return false;
            }

            if (pps.RequireAddress && (addresses == null || !addresses.Any()))
            {
                message = "An address is required in order to create a parking permit.";
                return false;
            }

            return true;
        }

    }
}
