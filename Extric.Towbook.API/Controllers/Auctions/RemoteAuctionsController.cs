using Extric.Towbook.API.Models.Auctions;
using Extric.Towbook.Auctions;
using Extric.Towbook.Auctions.Joyride;
using Extric.Towbook.WebShared;
using HashidsNet;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Auctions.Controllers
{

    [Route("remoteauctions")]
    public class RemoteAuctionsController : ControllerBase
    {
        public class RemoteAuctionModel
        {
            public string Id { get; set; }
            public string Title { get; set; }
            public string Provider { get; set; }
            public DateTime StartDate { get; set; }
            public DateTime? EndDate { get; set; }
        }

        //routes.MapHttpRoute(
        //    name: "RemoteAuctions_default_POST",
        //    routeTemplate: "remoteauctions/{action}",
        //    defaults: new { controller = "RemoteAuctions", id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };
        [HttpGet]
        [Route("")]
        [Route("get")]
        public IEnumerable<RemoteAuctionModel> Get()
        {
            var jrc = new JoyrideRestClient();

            var jc = JoyrideConnection.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);
            if (jc != null)
            {
                // todo get from db. 

                return jrc.HandleRefresh(jc).GetAuctions(jc.AccessToken)
                    .Select(o => new RemoteAuctionModel()
                    {
                        Id = o.AuctionSeries,
                        Title = o.Title,
                        Provider = "Joyride",
                        StartDate = o.Settings.Start.ToLocalTime()
                    });
            }

            // todo: peak connection

            return Array.Empty<RemoteAuctionModel>();
        }


        public class RemoteAuctionPostModel
        {
            public string RemoteId { get; set; }

            /// <summary>
            /// ImpoundIds
            /// </summary>
            public int[] Items { get; set; }
        }

        //routes.MapHttpRoute(
        //    name: "RemoteAuctions_default_POST",
        //    routeTemplate: "remoteauctions/{action}",
        //    defaults: new { controller = "RemoteAuctions", id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };
        [HttpPost]
        [Route("")]
        [Route("post")]
        public async Task<AuctionModel> Post(RemoteAuctionPostModel model)
        {
            if (model == null)
                throw new ArgumentNullException(nameof(model));

            if (model.Items == null || model.Items.Length == 0)
                throw new ArgumentException(nameof(model.Items));

            var auction = Auction.GetByCompanyIds((await WebGlobal.GetCompaniesAsync()).Select(o => o.Id).ToArray())
                .FirstOrDefault(o => o.RemoteId == model.RemoteId);

            var jrc = new JoyrideRestClient();

            // todo get from db. 
            var jc = JoyrideConnection.GetByCompanyId(WebGlobal.CurrentUser.PrimaryCompanyId);

            jrc.HandleRefresh(jc);

            string token = jc?.AccessToken;

            var remoteAuction = jrc.GetAuctions(token)
                .FirstOrDefault(r => r.AuctionSeries == model.RemoteId);

            if (auction == null)
            {
                auction = new Auction();

                auction.CompanyId = WebGlobal.CurrentUser.CompanyId;

                auction.RemoteSystem = "Joyride";
                auction.RemoteId = model.RemoteId;
                auction.OwnerUserId = WebGlobal.CurrentUser.Id;
                auction.StartDate = remoteAuction.Settings.Start.ToLocalTime();

                auction.Save();
            }

            var me = await jrc.GetMeAsync(token);
            var sellerId = me.Sellers.First().Id;

            foreach (var vehicle in model.Items)
            {
                var impound = await Towbook.Impounds.Impound.GetByIdAsync(vehicle);
                if (impound == null)
                    continue;
                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(impound.Company.Id))
                    continue;

                var call = impound.DispatchEntry;

                var ead = await EntryAuctionDetail.GetByDispatchEntryIdAsync(call.Id);
                var sendPhotos = true;
                if (ead == null)
                {
                    ead = new EntryAuctionDetail()
                    {
                        DispatchEntryId = call.Id,
                    };
                    sendPhotos = false;
                }

                var joyride = JoyrideRestClient.FromEntry(call, impound.Id, ead, remoteAuction.AuctionSeries, 
                     auction.StartDate.Value);

                joyride.RegionId = remoteAuction.RegionId;

                jrc.PostItem(
                    token,
                    sellerId,
                    joyride);


                ead.AuctionId = auction.Id;
                ead.RemoteId = joyride.ItemId;

                await ead.SaveAsync(this.GetCurrentToken(), this.GetRequestingIp());

                if (sendPhotos)
                {
                    var ap = AuctionPhoto.GetByAuctionDetailId(ead.Id);
                    if (ap.Any())
                    {
                        foreach (var p in ap)
                        {
                            var dp = await Dispatch.Photo.GetByIdAsync(p.DispatchEntryPhotoId);
                            
                            if (dp == null)
                                continue;

                            jrc.PostMedia(token,
                                sellerId,
                                joyride.ItemId,
                                dp.HttpLocation,
                                "IMAGE", "extra");
                        }
                    }
                }
            }
            
            return await AuctionModel.MapAsync(WebGlobal.CurrentUser.Company, auction);
        }
    }
}
