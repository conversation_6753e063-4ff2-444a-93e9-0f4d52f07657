using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.API.Models;
using System.IO;
using System.Threading.Tasks;
using Extric.Towbook.Storage;
using static Extric.Towbook.API.ApiUtility;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Multipart;
using SkiaSharp;
using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Net.Http.Headers;
using PhotoModel = Extric.Towbook.API.Models.PhotoModel;

namespace Extric.Towbook.API.Impounds.Controllers
{
    //routes.MapHttpRoute(
    //    name: "ImpoundPhotos",
    //    routeTemplate: "impounds/{impoundId}/photos/{id}",
    //    defaults: new { controller = "Photos", id = RouteParameter.Optional })
    //   .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Impounds.Controllers" } } };
    [Route("impounds")]
    public class ImpoundsPhotosController : ControllerBase
    {
        /// <summary>
        /// Retrieve a list of photos associated with <paramref name="impoundId"/>.
        /// </summary>
        /// <param name="impoundId">The call/entry that the photo is associated with.</param>
        /// <returns>List of Photos for the specified call</returns>
        [HttpGet]
        [Route("{impoundId}/photos")]
        public async Task<IEnumerable<PhotoModel>> Get(int impoundId)
        {
            List<PhotoModel> returns = new List<PhotoModel>();
            
            var d = await Extric.Towbook.Impounds.Impound.GetByIdAsync(impoundId);

            foreach(var photo in Extric.Towbook.Impounds.Photo.GetByImpoundId(impoundId))
            {
                string path = await FileUtility.GetFileAsync(photo.Location.Replace("%1", d.Company.Id.ToString()));
                if (path != null)
                {
                    returns.Add(PhotoModel.Map(photo));
                }
            }

            return returns;
        }
        [HttpGet]
        [Route("{impoundId}/photos/{id}")]
        public async Task<HttpResponseMessage> Get(int impoundId, int id)
        {
            var x = Extric.Towbook.Impounds.Photo.GetById(id);
            var d = await Extric.Towbook.Impounds.Impound.GetByIdAsync(impoundId);

            if (x == null || d == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(d.Company.Id))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to that companies photos.") });

            
            HttpResponseMessage result = new HttpResponseMessage(HttpStatusCode.OK);
            result.Content = new StreamContent(new FileStream(await FileUtility.GetFileAsync(x.Location.Replace("%1", d.Company.Id.ToString())), FileMode.Open, FileAccess.Read, FileShare.ReadWrite));
            result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(x.ContentType);


            
            return result;
        }

        /// <summary>
        /// Upload a photo for a impound
        /// </summary>
        /// <param name="impoundId">The impound that the photo is associated with.</param>
        /// <param name="description"></param>
        /// <returns>If successful, returns HTTP Status 201. 
        /// 
        /// Returns a JSON object equilivant to calling PhotosController.Get(impoundId, id). 
        /// </returns>
        [HttpPost]
        [Route("{impoundId}/photos")]
        [DisableFormValueModelBinding]
        public async Task<ObjectResult> Post(
            [FromQuery] int impoundId,
            [FromQuery] string description = "")
        {
            var u = await Extric.Towbook.User.GetByIdAsync(WebGlobal.CurrentUser.Id);
            var imp = await Extric.Towbook.Impounds.Impound.GetByIdAsync(impoundId);

            if (imp == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(imp.Company.Id))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Either the impound doesn't exist, or you don't have access to it, so your photo could not be uploaded.")
                });

            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }

            // Save file
            string path = System.IO.Path.GetTempPath();

            MultipartFormDataStreamProvider provider = new MultipartFormDataStreamProvider(path);

            FileInfo fileInfo = null;

            FormOptions _defaultFormOptions = new FormOptions();

            var boundary = MultipartRequestHelper.GetBoundary(
                MediaTypeHeaderValue.Parse(Web.HttpContext.Current.Request.ContentType),
                _defaultFormOptions.MultipartBoundaryLengthLimit);

            var reader = new MultipartReader(boundary, Web.HttpContext.Current.Request.Body);

            var section = await reader.ReadNextSectionAsync();

            string targetFilePath = "";

            if (section != null)
            {
                var hasContentDispositionHeader =
                    ContentDispositionHeaderValue.TryParse(
                        section.ContentDisposition, out var contentDisposition);

                if (hasContentDispositionHeader)
                {
                    if (MultipartRequestHelper.HasFileContentDisposition(contentDisposition))
                    {
                        targetFilePath = Path.GetTempFileName();
                        using (var targetStream = System.IO.File.Create(targetFilePath))
                        {
                            await section.Body.CopyToAsync(targetStream);
                            fileInfo = new FileInfo(targetFilePath);
                            Console.WriteLine($"Copied the uploaded file '{targetFilePath}'");
                        }
                    }
                }
            }

            string savedFile = targetFilePath;
            string originalFile = section.GetContentDispositionHeader().FileName.Value.TrimStart('"').TrimEnd('"');

            // Copy file and rename with new file name and correct extension
            FileInfo file = new FileInfo(savedFile);

            try
            {
                string fn = "";
                int photoId = -1;

                using (var i = SKBitmap.Decode(file.FullName))
                {
                    Extric.Towbook.Impounds.Photo p = new Extric.Towbook.Impounds.Photo();
                    p.ImpoundId = Convert.ToInt32(impoundId);
                    p.ContentType = "image/jpg";
                    p.Description = description;
                    p.Save(u);

                    fn = p.Location.Replace("%1", imp.Company.Id.ToString());

                    if (!Directory.Exists(Path.GetDirectoryName(fn)))
                    {
                        Directory.CreateDirectory(Path.GetDirectoryName(fn));
                    }

                    using (var resized = i.ResizeProportionately(1920))
                        resized.Save(fn, SKEncodedImageFormat.Jpeg);

                    photoId = p.Id;
                }

                var result = await FileUtility.SendFileAsync(fn);
                if (result.IsHttpSuccess())
                    System.IO.File.Delete(fn);

                return StatusCode((int)HttpStatusCode.Created, await PhotoModel.TranslateDomainToModelAsync(await Dispatch.Photo.GetByIdAsync(photoId), WebGlobal.CurrentUser));
            }
            catch (Exception e)
            {
                throw new Web.HttpResponseException(
                    new HttpResponseMessage(HttpStatusCode.NotImplemented)
                    {
                        Content = new StringContent("Invalid Image File. Only JPG, PNG, BMP and GIF files are supported.\n\n" + e.Message)
                    });
            }
            finally
            {
                file.Delete();
            }

        }

        /// <summary>
        /// Deletes a photo associated with an impound.
        /// </summary>
        /// <param name="impoundId">The call/entry that the photo is associated with.</param>
        /// <param name="id">The ID of the photo to delete</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{impoundId}/photos/{id}")]
        public async Task<HttpResponseMessage> DeleteAsync(int impoundId, int id)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.SystemAdministrator)
            {
                var e = await Extric.Towbook.Impounds.Impound.GetByIdAsync(impoundId);
                var p = Extric.Towbook.Impounds.Photo.GetById(id);

                if (e == null || p == null)
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(e.Company.Id))
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to that companies photos.") });

                p.Delete(WebGlobal.CurrentUser);

                return new HttpResponseMessage(HttpStatusCode.NoContent); // { Content = new StringContent(Guid.NewGuid().ToString("N") + ": Deleted DispatchEntry #" + id + ", using user " + WebGlobal.CurrentUser.Id + " succesfully.") };
            }
            else
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(Forbidden()));
            }
        }

    }
}
