using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Commissions;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    [Route("commissionDriverReportOptions")]
    public class CommissionDriverReportOptionsController : ControllerBase
    {
        /// <summary>
        /// Get commission driver report options for the current user's company.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public async Task<CommissionDriverReportOptionsModel> Get()
        {
            return CommissionDriverReportOptionsModel.Map(
                await CommissionDriverReportOptions.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId));
        }

        /// <summary>
        /// Override default driver report options for the current user's company.
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("")]
        public async Task<HttpResponseMessage> Post([FromBody] CommissionDriverReportOptionsModel model)
        {
            await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.CompanyId);
            
            if (model.Id > 0)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("The Id in the payload is greater than zero.  Did you mean to do a PUT request?")
                });
            }

            var reportOptions = await CommissionDriverReportOptions.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);
            // Disallow if the companyId on the model isn't the current company
            // and if there already exists settings for that company noted by options id being > 0
            if (model.CompanyId != WebGlobal.CurrentUser.CompanyId || reportOptions.Id > 0)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
            
            var mappedOptions = CommissionDriverReportOptionsModel.Map(model, WebGlobal.CurrentUser.CompanyId);
            await mappedOptions.Save(WebGlobal.CurrentUser.Id);
            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        /// <summary>
        /// Update driver report options for the current user's company.
        /// </summary>
        /// <param name="id">The id for the commission driver report options</param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("{id}")]
        public async Task<HttpResponseMessage> Put(int id, [FromBody] CommissionDriverReportOptionsModel model)
        {
            if (model.Id == 0)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("The ID provided in the request is zero.  Did you mean to do a POST request?")
                });
            }

            var reportOptions = await CommissionDriverReportOptions.GetById(id);
            if (reportOptions == null
                || id != reportOptions.Id
                || id != model.Id
                || model.CompanyId != reportOptions.CompanyId)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }

            await WebGlobal.CurrentUser.HasAccessToCompanyAsync(reportOptions.CompanyId);

            if (model.Id < 0)
                return new HttpResponseMessage(HttpStatusCode.BadRequest);

            var updatedOptions = CommissionDriverReportOptionsModel.Map(model, WebGlobal.CurrentUser.CompanyId);
            await updatedOptions.Save(WebGlobal.CurrentUser.Id);
            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }
    }
}
