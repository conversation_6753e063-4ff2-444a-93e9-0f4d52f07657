using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Stickering.Models;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Impounds;
using Extric.Towbook.Stickering;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Multipart;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Stickering.Controllers
{
    /// <summary>
    /// Exposes functionality related to stickering of vehicles for private property parking enforcement purposes.
    /// </summary>
    [Route("stickering/stickers")]
    public class StickersController : ControllerBase
    {

        [HttpGet]
        [Route("")]
        public async Task<object> GetSolver([FromQuery] int? statusId = null, [FromQuery] int? accountId = null, [FromQuery] int? page = 1, [FromQuery] int? startAtId = null, [FromQuery] int? w = null) =>
            (startAtId != null) ? await Search((int)startAtId, w) : ((statusId != null) ? await Get((int)statusId, accountId, page) : await Get(accountId));

        ///<summary>Get a group of stickers based on their status for a company.</summary>
        ///<returns>A collection of sticker models</returns>
        ///<param name="statusId">The status of the stickers to retrieve.</param>
        ///<param name="accountId">Limit the stickers to a specific account.</param>
        ///<param name="page">Used for pagination of results.</param
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IEnumerable<StickerModel>> Get([FromQuery] int statusId, [FromQuery] int? accountId = null, [FromQuery] int? page = 1)
        {
            var list = new List<StickerModel>();

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser)
            {
                list.AddRange(await Task.WhenAll(Sticker.GetByStatusId(WebGlobal.CurrentUser.CompanyId, statusId, WebGlobal.CurrentUser.AccountId, page.Value)
                    .Select(async o => await StickerModel.Map(WebGlobal.CurrentUser, o))));
            }
            else
            {
                if (accountId != null)
                    await ThrowIfNoCompanyAccessAsync((await Account.GetByIdAsync(accountId.Value))?.CompanyId);

                list.AddRange(await Task.WhenAll(Sticker.GetByStatusId(WebGlobal.CurrentUser.CompanyId, statusId, accountId, page.Value)
                    .Select(async o => await StickerModel.Map(WebGlobal.CurrentUser, o))));
            }

            StickerModel.AddInsightsToModels(WebGlobal.CurrentUser, list.ToCollection());

            return list;
        }


        /// <summary>Retrieves the current stickers for a company or account.</summary>
        /// <returns>All stickers that are in waiting, approved, towable and converted will be returned. 
        /// The first page size of rejected, resolved, towed and expired stickers will be returned.</returns>
        /// <param name="accountId">Optional accountId allows for results to be filtered by an account.</param>
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IEnumerable<StickerModel>> Get([FromQuery] int? accountId = null)
        {
            var list = new List<StickerModel>();

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser)
                list.AddRange(await Task.WhenAll(Sticker.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, WebGlobal.CurrentUser.AccountId)
                    .Select(async o => await StickerModel.Map(WebGlobal.CurrentUser, o))));
            else
            {
                if (accountId != null)
                {
                    var account = await Towbook.Accounts.Account.GetByIdAsync(accountId.Value);
                    await ThrowIfNoCompanyAccessAsync(account?.CompanyId, "Sticker");

                    list.AddRange(await Task.WhenAll(Sticker.GetByCompanyId(account.CompanyId, accountId.Value)
                        .Select(async o => await StickerModel.Map(WebGlobal.CurrentUser, o))));
                }
                else
                {
                    await ThrowIfNoCompanyAccessAsync(WebGlobal.CurrentUser.CompanyId, "Sticker");

                    list.AddRange(await Task.WhenAll(Sticker.GetByCompanyIds((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray())
                        .Select(async o => await StickerModel.Map(WebGlobal.CurrentUser, o))));
                }
            }

            // add reasons
            var reasons = Extric.Towbook.Stickering.Reason.GetByStickerIds(list.Select(s => s.Id).ToArray());
            foreach (var s in list)
            {
                if (!reasons.ContainsKey(s.Id))
                    continue;

                var stickerReasons = reasons.Where(w => w.Key == s.Id).Select(x => x.Value).FirstOrDefault();
                if (stickerReasons != null)
                    s.Reasons = stickerReasons.Select(y => y.Id).ToArray();
            }

            int[] callIds = list.Where(w => w.CallId != null).Select(s => s.CallId.Value).ToArray();
            if (callIds.Count() > 0)
            {
                var entries = await Dispatch.Entry.GetByIdsAsync(callIds, null, false);
                foreach (var e in entries)
                {
                    if (list.Where(w => w.CallId == e.Id).Any())
                        list.Where(w => w.CallId == e.Id).First().CallNumber = e.CallNumber;
                }
            }

            StickerModel.AddInsightsToModels(WebGlobal.CurrentUser, list.ToCollection());

            return list
                .OrderByDescending(a => a.ExpirationDate ?? a.TowableDate ?? a.CreateDate)
                .ToList();
        }

        /// <summary>
        /// Retrieve the requested stickerId.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("{id}")]
        [HttpGet]
        public async Task<StickerModel> Get(int id)
        {
            var result = await StickerModel.Map(WebGlobal.CurrentUser, Sticker.GetById(id));

            await ThrowIfNoCompanyAccessAsync(result?.CompanyId, "Sticker");

            result.StickerNotes = await new StickeringNotesController().GetAsync(id);

            if (result.CallId != null)
            {
                var e = await Dispatch.Entry.GetByIdAsync(result.CallId.Value);
                if (e != null)
                    result.CallNumber = e.CallNumber;
            }

            return result;
        }

        [HttpGet]
        [Route("Search")]
        public async Task<Collection<StickerModel>> Search([FromQuery] int startAtId, [FromQuery] int? w)
        {
            var nvc = HttpUtility.ParseQueryString(Web.HttpContext.Current.Request.QueryString.Value);

            int? accountId = null;
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser)
                accountId = (int?)WebGlobal.CurrentUser.AccountId;
            else
            {
                if (!string.IsNullOrEmpty(nvc["accountId"]))
                    accountId = Convert.ToInt32(nvc["accountId"]);
            }

            int? year = null;
            if (!string.IsNullOrEmpty(nvc["year"]))
                year = Convert.ToInt32(nvc["year"]);

            string make = !string.IsNullOrEmpty(nvc["make"]) ? nvc["make"] : null;
            string model = !string.IsNullOrEmpty(nvc["model"]) ? nvc["model"] : null;
            string vin = !string.IsNullOrEmpty(nvc["vin"]) ? nvc["vin"] : null;
            string plate = !string.IsNullOrEmpty(nvc["plate"]) ? nvc["plate"] : null;

            int? reasonId = null;
            if (!string.IsNullOrEmpty(nvc["reasonId"]))
                reasonId = Convert.ToInt32(nvc["reasonId"]);

            DateTime searchStartDate = DateTime.Now.AddYears(-50);
            DateTime searchEndDate = DateTime.MaxValue;

            if (!string.IsNullOrEmpty(nvc["startDate"]))
                searchStartDate = WebGlobal.OffsetDateTime(Convert.ToDateTime(nvc["startDate"]));

            if (!string.IsNullOrEmpty(nvc["endDate"]))
                searchEndDate = WebGlobal.OffsetDateTime(Convert.ToDateTime(nvc["endDate"])).AddHours(23).AddMinutes(59).AddSeconds(59);

            var list = Sticker.GetBySearchCriteria(
                (await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray(),
                accountId,
                year, make, model, vin, plate,
                reasonId,
                searchStartDate, searchEndDate);

            // add reasons
            var reasons = Extric.Towbook.Stickering.Reason.GetByStickerIds(list.Select(s => s.Id).ToArray());
            foreach (var s in list)
            {
                if (!reasons.ContainsKey(s.Id))
                    continue;

                var stickerReasons = reasons.Where(a => a.Key == s.Id).Select(x => x.Value).FirstOrDefault();
                if (stickerReasons != null)
                    s.Reasons = stickerReasons.Select(y => y.Id).ToArray();
            }

            return await StickerModel.Map(WebGlobal.CurrentUser, list.ToCollection());
        }


        /// <summary>
        /// Converts a sticker into a Call/DispatchEntry.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="onScene"></param>
        /// <returns></returns>
        [Route("{id}/createcall")]
        [HttpPost]
        public async Task<object> CreateCall(int id, [FromQuery] bool onScene = false)
        {
            var sr = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(sr?.CompanyId, "Sticker");

            if (sr.DispatchEntryId != null)
            {
                var existing = await Entry.GetByIdAsync(sr.DispatchEntryId.Value);
                if (existing != null)
                {
                    return new
                    {
                        CallId = existing.Id,
                        CallNumber = existing.CallNumber,
                        AlreadyExists = true,
                        CreateDate = existing.CreateDate
                    };
                }
            }

            if (sr.ExpirationDate != null && sr.ExpirationDate <= DateTime.Now)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker has already expired and cannot be towed.")
                });
            }

            // check for permit
            if (!string.IsNullOrWhiteSpace(sr.LicenseNumber))
            {
                var pp = Extric.Towbook.Accounts.ParkingPermit.GetByLicensePlate(sr.AccountId, sr.LicenseNumber).FirstOrDefault();

                if (pp != null &&
                    pp.AccountId == sr.AccountId &&
                    !pp.IsTowable())
                {
                    var acc = await Account.GetByIdAsync(pp.AccountId);
                    var vehicle = pp.VehicleFormatted();

                    var type = string.Empty;
                    if (pp.ParkingPermitListId != null)
                        type = "(" + ((ParkingPermitListBuiltInTypes)pp.ParkingPermitListId.Value).ToString() + ")";

                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent($"This sticker cannot be converted to a call. A permit {type} exists with {acc.Company} for a {vehicle}.")
                    });
                }
            }

            var reasons = Extric.Towbook.Stickering.Reason.GetByStickerId(sr.Id);
            if (reasons == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker cannot be converted to a call. At least one sticker reason must exist.")
                });
            }

            var e = new Entry();
            e.CompanyId = sr.CompanyId;
            e.AccountId = sr.AccountId;
            e.OwnerUserId = WebGlobal.CurrentUser.Id;
            e.ReasonId = 1635; // see notes
            e.Notes = $"Created from Sticker #{sr.StickerNumber}\r\n" +
                        "Sticker reason(s): " + string.Join(",", reasons.Select(r => r.Name));
            e.Assets.Add(new EntryAsset());
            e.Assets.First().Make = sr.VehicleMake;
            e.Assets.First().Model = sr.VehicleModel;
            e.Assets.First().Year = sr.VehicleYear;
            e.Assets.First().Vin = sr.VehicleVIN;
            e.Assets.First().ColorId = sr.ColorId;
            e.Assets.First().LicenseNumber = sr.LicenseNumber;
            e.Assets.First().LicenseState = sr.LicenseState;
            e.Assets.First().BodyTypeId = 1;

            // Stores the sticker # in call attributes
            var av = new Dispatch.AttributeValue()
            {
                Value = sr.StickerNumber.ToString(),
                DispatchEntryAttributeId = Dispatch.AttributeValue.BUILTIN_DISPATCH_STICKER_NUMBER
            };

            e.Attributes.Add(Dispatch.AttributeValue.BUILTIN_DISPATCH_STICKER_NUMBER, av);

            e.TowSource = (e.Account.Address + ", " + e.Account.City + " " + e.Account.State + " " +
                e.Account.Zip).Trim().Trim(',');
            e.Impound = true;

            if (onScene)
            {
                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Dispatching_OnSceneCallCreation))
                {
                    e.Status = Dispatch.Status.AtSite;

                    // set arrival/on scene time to current time.
                    if (e.ArrivalTime == null)
                    {
                        e.ArrivalTime = DateTime.Now;
                        e.DispatchTime = DateTime.Now;
                        e.EnrouteTime = DateTime.Now;
                    }
                }
                else
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent("This company does not have access to OnScene call creation.")
                    });
                }
            }

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
            {
                var driver = Driver.GetByUserId(WebGlobal.CurrentUser.Id).FirstOrDefault();
                if (driver != null)
                {
                    var ead = new EntryAssetDriver();
                    ead.DriverId = driver.Id;

                    var dtp = DriverTruckDefault.GetByDriverId(driver.Id);
                    if (dtp != null)
                    {
                        ead.TruckId = dtp.TruckId;
                    }

                    e.Assets.First().Drivers.Add(ead);
                }
            }

            await e.Save();

            var imp = new Impound() { DispatchEntry = e, Invoice = e.Invoice, Company = e.Company };

            if (e.Account.ImpoundDestinationStorageLotId.GetValueOrDefault(0) > 0)
            {
                imp.Lot = await Lot.GetByIdAsync(e.Account.CompanyId, e.Account.ImpoundDestinationStorageLotId.Value);
            }
            else
            {
                imp.Lot = (await Lot.GetByCompanyAsync(e.Company)).FirstOrDefault() ?? new Lot();
            }

            imp.Company = e.Company;
            imp.OwnerUserId = WebGlobal.CurrentUser.Id;
            imp.ImpoundDate = e.CompletionTime ?? e.CreateDate;
            await imp.Save(WebGlobal.CurrentUser);

            sr.DispatchEntryId = e.Id;
            sr.StatusId = StickerStatus.Towed.Id;
            sr.StatusName = StickerStatus.Towed.Name;
            await sr.Save(false);

            await sr.AddStatusEvent(StickerStatus.Converted.Id, WebGlobal.CurrentUser.Id, false);
            await sr.AddStatusEvent(StickerStatus.Towed.Id, WebGlobal.CurrentUser.Id, true);

            return new
            {
                CallId = e.Id,
                CallNumber = e.CallNumber
            };
        }

        /// <summary>
        /// Create a new Sticker
        /// </summary>
        /// <param name="stikerModel"></param>
        /// <returns></returns>
        [Route("")]
        [HttpPost]
        public async Task<StickerModel> Post(StickerModel stikerModel)
        {
            if (stikerModel.Id > 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You shouldn't set the sticker.id in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountManager)
            {
                stikerModel.AccountId = WebGlobal.CurrentUser.AccountId;
            }

            if (stikerModel.AccountId.GetValueOrDefault(0) == 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("AccountId must be specified.")
                });
            }

            var account = await Account.GetByIdAsync(stikerModel.AccountId.Value);
            await ThrowIfNoCompanyAccessAsync(account.CompanyId);

            // Force the sticker to go to the company of the account
            stikerModel.CompanyId = account.CompanyId;

            if (stikerModel.Reasons == null || stikerModel.Reasons.Count() == 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Missing reason. There must be at least one sticker reason specified to create a sticker.")
                });
            }

            if (stikerModel.VehicleLatitude.GetValueOrDefault() == 0)
                stikerModel.VehicleLatitude = RecentLatitude();

            if (stikerModel.VehicleLongitude.GetValueOrDefault() == 0)
                stikerModel.VehicleLongitude = RecentLongitude();

            var s = StickerModel.Map(stikerModel);

            s.StatusId = StickerStatus.Waiting.Id;
            s.OwnerUserId = WebGlobal.CurrentUser.Id;

            var ss = StickerSession.GetOrCreateSessionForAccountId(s.CompanyId, s.AccountId, s.OwnerUserId);

            s.StickerSessionId = ss?.Id ?? throw new TowbookException("Couldn't find StickerSession to attach sticker to.");

            await s.Save();

            return await StickerModel.Map(WebGlobal.CurrentUser, s);
        }

        /// <summary>
        /// Modify an existing sticker
        /// </summary>
        /// <param name="id"></param>
        /// <param name="stickerModel"></param>
        /// <returns></returns>
        [Route("{id}")]
        [HttpPut]
        public async Task<StickerModel> Put(int id, StickerModel stickerModel)
        {
            var s = Sticker.GetById(id);
            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "Sticker");

            var acc = await Account.GetByIdAsync(s.AccountId);
            await ThrowIfNoCompanyAccessAsync(acc?.CompanyId, "Sticker Account");

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountManager)
            {
                if (s.AccountId != WebGlobal.CurrentUser.AccountId)
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent($"Your user account doesn't have access to this sticker.")
                    });

                stickerModel.AccountId = WebGlobal.CurrentUser.AccountId;
            }

            bool changed = stickerModel.StatusId != null && s.StatusId != stickerModel.StatusId.Value;

            s = StickerModel.Map(stickerModel, s);

            s.CompanyId = acc.CompanyId;
            s.OwnerUserId = WebGlobal.CurrentUser.Id;

            await s.Save();

            if (changed)
                await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id);

            return await StickerModel.Map(WebGlobal.CurrentUser, s);
        }

        /// <summary>
        /// Deletes the specified sticker.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{id}")]
        [ApiPermission(Towbook.User.TypeEnum.Manager)]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var s = Sticker.GetById(id);

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(s.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(Forbidden()) { Content = new StringContent("You don't have access to that companies data.") });

            await s.Delete();

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }


        public class StickerResponseModel
        {
            public int Id { get; set; }
            public string Notes { get; set; }
        }

        /// <summary>
        /// Marks the sticker as resolved. The owner moved it, fixed the problem, its no longer in violation.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("{id}/resolve")]
        [HttpPost]
        public async Task<HttpResponseMessage> Resolve(int id, [FromForm] StickerResponseModel model)
        {
            var s = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "sticker");

            s.StatusId = StickerStatus.Resolved.Id;
            s.ResolvedDate = DateTime.Now;
            s.ResolvedUserId = WebGlobal.CurrentUser.Id;
            await s.Save();
            await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        /// <summary>
        /// This is how a user can unresolve a sticker.
        /// </summary>
        /// <param name="id">The sticker id to unresolved</param>
        /// <param name="model"></param>
        /// <returns></returns>
        [Route("{id}/unresolve")]
        [HttpPost]
        public async Task<HttpResponseMessage> Unresolve(int id, [FromForm] StickerResponseModel model)
        {
            var s = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "sticker");

            if (s.ExpirationDate != null && s.ExpirationDate >= DateTime.Now)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker has already expired and cannot be unresolved.")
                });
            }

            if (s.StatusId != StickerStatus.Resolved.Id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker status is not listed as resovled. This sticker cannot be unresolved.")
                });
            }

            s.StatusId = StickerStatus.Waiting.Id;
            s.ResolvedDate = null;
            s.ResolvedUserId = null;

            Sticker.CheckIfApproved(s);
            Sticker.CheckIfTowable(s);
            await s.Save();
            await s.AddStatusEvent(StickerStatus.Unresolved.Id, WebGlobal.CurrentUser.Id, false);
            await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }



        /// <summary>
        /// To be called only by an account user; this is how a user can approve a sticker so that it can be towed.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.AccountUser)]
        [Route("{id}/approve")]
        [HttpPost]
        public async Task<HttpResponseMessage> Approve(int id, [FromForm] StickerResponseModel model)
        {
            var s = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "sticker");

            if (WebGlobal.CurrentUser.AccountId > 0 && WebGlobal.CurrentUser.AccountId != s.AccountId)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You do not have permission to approve the sticker.")
                });
            }

            if (s.ExpirationDate != null && s.ExpirationDate >= DateTime.Now)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker has already expired and cannot be approved.")
                });
            }

            // check settings to see if a signature is required to approve
            var setting = StickerSetting.GetByCompanyId(s.CompanyId, s.AccountId);

            if (setting.RequireApprovalSignature)
            {
                bool signatureFound = false;

                // Check for saved signature
                var us = UserSignature.GetByUserId(WebGlobal.CurrentUser.Id);
                if (us != null)
                {
                    // check for user agreement is in place to use the user's digital signature
                    var usa = UserSignatureAgreement.GetByUserId(WebGlobal.CurrentUser.Id);
                    var type = SignatureType.GetByName(s.CompanyId, "Tow Authorization");

                    if (usa != null && type != null && usa.Select(a => a.SignatureTypeId).Contains(type.SignatureTypeId))
                    {
                        // associate the userSignatureId to the sticker
                        s.AuthorizationUserSignatureId = us.UserSignatureId;
                        signatureFound = true;
                    }
                }

                if (!signatureFound)
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("Signature is required to approve this sticker.")
                    });
                }
            }

            s.StatusId = StickerStatus.Approved.Id;
            s.AuthorizationDate = DateTime.Now;
            s.AuthorizationUserId = WebGlobal.CurrentUser.Id;

            await s.Save();
            await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }


        /// <summary>
        /// To be called only by an account user; this is how a user can approve a sticker so that it can be towed.
        /// </summary>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.AccountUser)]
        [Route("{id}/approvewithsignature")]
        [DisableFormValueModelBinding]
        [HttpPost]
        public async Task<HttpResponseMessage> ApproveWithSignature(int id, [FromRoute] int? disclaimerId = null)
        {
            var s = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "sticker");

            if (s.ExpirationDate != null && s.ExpirationDate >= DateTime.Now)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker has already expired and cannot be approved.")
                });
            }

            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }

            var us = new UserSignature();

            // Save file
            var postFiles = await GetFilesFromHttpPostRequest(Request);
            var tempFileInfo = postFiles.First();
            try
            {
                us.UserId = WebGlobal.CurrentUser.Id;
                us.ContentType = "image/jpg";
                us.RemoteIp = this.GetRequestingIp();
                us.Latitude = RecentLatitude();
                us.Longitude = RecentLongitude();

                string location = us.Location;
                //Console.WriteLine(tempFileInfo.FullName);
                await CreateaPhotoFile(tempFileInfo.FullName, location);

                us.FileSize = new FileInfo(location).Length;
                us.Save();

                s.AuthorizationUserSignatureId = us.UserSignatureId;
                s.AuthorizationUserId = us.UserId;
                s.AuthorizationDate = DateTime.Now;
                s.StatusId = StickerStatus.Approved.Id;
                await s.Save();
                await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id);
            }
            catch
            {
                throw;
            }
            finally
            {
                tempFileInfo.Delete();
            }

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        /// <summary>
        /// To be called only by an account user or tow manager; this is how a user can unapprove a sticker.
        /// </summary>
        /// <param name="id">The sticker id to unapprove</param>
        /// <param name="model"></param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.AccountUser)]
        [Route("{id}/unapprove")]
        [HttpPost]
        public async Task<HttpResponseMessage> Unapprove(int id, [FromForm] StickerResponseModel model)
        {
            var s = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "sticker");

            if (s.ExpirationDate != null && s.ExpirationDate >= DateTime.Now)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker has already expired and cannot be unapproved.")
                });
            }

            if (s.AuthorizationDate == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker has not been approved and cannot be unapproved.")
                });
            }

            s.StatusId = StickerStatus.Waiting.Id;
            s.AuthorizationDate = null;
            s.AuthorizationUserId = null;
            s.AuthorizationUserSignatureId = null;
            await s.Save();

            await s.AddStatusEvent(StickerStatus.Unapproved.Id, WebGlobal.CurrentUser.Id, false);
            await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }


        /// <summary>
        /// To be called only by an account user; this is how a user can reject a sticker so that the company knows not to tow it.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.AccountUser)]
        [Route("{id}/reject")]
        [HttpPost]
        public async Task<HttpResponseMessage> Reject(int id, [FromForm] StickerResponseModel model)
        {
            var s = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "sticker");

            s.StatusId = StickerStatus.Rejected.Id;
            s.RejectedDate = DateTime.Now;
            s.RejectedUserId = WebGlobal.CurrentUser.Id;
            await s.Save();

            await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        /// <summary>
        /// This is how a user can unreject a sticker.
        /// </summary>
        /// <param name="id">The sticker id to unrejected</param>
        /// <param name="model"></param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.AccountUser)]
        [Route("{id}/unreject")]
        [HttpPost]
        public async Task<HttpResponseMessage> Unreject(int id, [FromForm] StickerResponseModel model)
        {
            var s = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "sticker");

            if (s.ExpirationDate != null && s.ExpirationDate >= DateTime.Now)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker has already expired and cannot be unrejected.")
                });
            }

            if (s.StatusId != StickerStatus.Rejected.Id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("This sticker status is not listed as rejected. This sticker cannot be unrejected.")
                });
            }

            s.StatusId = StickerStatus.Waiting.Id;
            s.RejectedDate = null;
            s.RejectedUserId = null;

            Sticker.CheckIfApproved(s);
            Sticker.CheckIfTowable(s);
            await s.Save();

            await s.AddStatusEvent(StickerStatus.Unrejected.Id, WebGlobal.CurrentUser.Id, false);
            await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }



        public sealed class StickerExtendModel
        {
            public int Id { get; set; }
            public int Hours { get; set; }
        }

        /// <summary>
        /// To be called only by an account user; this is how a user can reject a sticker so that the company knows not to tow it.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.AccountUser)]
        [Route("{id}/extend")]
        [HttpPost]
        public async Task<HttpResponseMessage> Extend(int id, StickerExtendModel model)
        {
            var s = Sticker.GetById(id);

            await ThrowIfNoCompanyAccessAsync(s?.CompanyId, "sticker");

            var ss = StickerSetting.GetByCompanyId(s.CompanyId, s.AccountId);
            if (!ss.AllowExtensions)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(Forbidden())
                {
                    Content = new StringContent($"Extensions are not permitted due to a company restrictions.")
                });
            }


            if (s.StatusId == StickerStatus.Unsaved.Id ||
                s.StatusId == StickerStatus.Waiting.Id ||
                s.StatusId == StickerStatus.Approved.Id)
            {
                // extend grace period if in waiting or approved status
                var currDate = s.ExtendedExpirationDate ?? s.GracePeriodExpirationDate;
                if (currDate != null)
                    s.ExtendedExpirationDate = currDate.Value.AddHours(model.Hours);
                else
                    s.ExtendedExpirationDate = DateTime.Now.AddHours(model.Hours);
            }
            else
            {
                // extend from now
                s.ExtendedExpirationDate = DateTime.Now.AddHours(model.Hours);
            }

            s.StatusId = StickerStatus.Waiting.Id;
            s.StatusName = StickerStatus.Waiting.Name;
            await s.Save(false);

            await s.AddStatusEvent(StickerStatus.Extended.Id, WebGlobal.CurrentUser.Id, false);
            await s.AddStatusEvent(s.StatusId, WebGlobal.CurrentUser.Id, true);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }
    }
}
