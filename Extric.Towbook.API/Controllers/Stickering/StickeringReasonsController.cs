using Extric.Towbook.API.Stickering.Models;
using Extric.Towbook.Stickering;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Stickering.Controllers
{
    [Route("stickering/reasons")]
    public class StickeringReasonsController: ControllerBase
    {
        [Route("")]
        [HttpGet]
        public async Task<IEnumerable<ReasonModel>> Get()
        {
            if (WebGlobal.CurrentUser.AccountId > 0) 
                return Towbook.Stickering.Reason.GetByAccountId(WebGlobal.CurrentUser.AccountId).Select(o => ReasonModel.Map(o));

            return await Get(WebGlobal.CurrentUser.CompanyId, false);
        }

        /// <summary>
        /// Retrieves built in and custom reasons for a company. This should be thought of as the 'GetByCompanyId' method.
        /// </summary>
        /// <param name="id">The Company Id</param>
        /// <param name="includeAll">Include deleted reasons</param>
        /// <returns>The stickering reasons that belong to a given company. Note: a companyId of null are built-in reasons.</returns>
        [Route("{id}")]
        [HttpGet]
        public async Task<IEnumerable<ReasonModel>> Get(int id, [FromQuery] bool includeAll = false)
        {
            if (id != 0 && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(id))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to that companies data.") });

            var ret = new Collection<ReasonModel>();

            if (id != 0)
                ret = Reason.GetByCompanyId(id, includeAll).Select(o => ReasonModel.Map(o)).ToCollection();
            else
                ret = Reason.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, includeAll).Select(o => ReasonModel.Map(o)).ToCollection();

            // Get all reasons by accounts and flatten to an array
            var stickeringAccounts = StickerAccount.GetByCompanyIds((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray());
            if (stickeringAccounts?.Count() > 0)
            {
                var accountStickerReasonLinks = Towbook.Stickering.AccountStickerReasonLink.GetByAccountIds(stickeringAccounts.Select(s => s.AccountId).ToArray());
                foreach (var reason in ret)
                {
                    var accountReasons = accountStickerReasonLinks.Where(w => w.StickerReasonId == reason.Id);
                    if (accountReasons.Any())
                        reason.AccountIds = accountReasons.OrderBy(a => a.AccountId).Select(s => s.AccountId).ToArray();
                }
            }

            return ret.OrderBy(o => o.Name);
        }

        [Route("")]
        [HttpPost]
        public async Task<object> Post(ReasonModel model)
        {
            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.CompanyId.GetValueOrDefault(0)))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to that companies data.") });

            if (model.Id > 0)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You shouldn't set the reason.id in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            var r = ReasonModel.Map(model);
            r.Save();

            return r;
        }

        [Route("{id}")]
        [HttpPut]
        public async Task<object> Put(int id, ReasonModel model)
        {
            var r = Towbook.Stickering.Reason.GetById(id);

            if (r == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(r.CompanyId.GetValueOrDefault(0)))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to that companies data.") });

            r = ReasonModel.Map(model, r);
            r.Save();

            return r;
        }

        [Route("{id}")]
        [HttpDelete]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var r = Towbook.Stickering.Reason.GetById(id);

            if (r == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(r.CompanyId.GetValueOrDefault(0)))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to that companies data.") });

            if (!r.Deleted)
                r.Delete();

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }
        
    }
}
