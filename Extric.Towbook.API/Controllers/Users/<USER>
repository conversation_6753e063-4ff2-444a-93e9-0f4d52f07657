using Extric.Towbook.API.Models;
using Extric.Towbook.WebShared;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Company;
using Extric.Towbook.Utility;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Collections.ObjectModel;
using OfficeOpenXml;
using System.IO;
using System.Net.Http.Headers; 
using System.Net.Mail;
using NLog;
using Extric.Towbook.Caching;
using Extric.Towbook.Storage;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Web;
using Microsoft.AspNetCore.Http;
using SkiaSharp;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.WebShared.Multipart;

namespace Extric.Towbook.API.Controllers
{
    [Route("users")]
    public class UsersController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        
        //Storage/userProfilePhotos/{companyId}/";
        private static string StorageProfilePath(int companyId) => Path.Combine(
            "Storage", "userProfilePhotos", companyId.ToString());

        /// <summary>
        /// Get Config User Model
        /// </summary>
        /// <param name="accountId"></param>
        /// <param name="includeDeleted"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public async Task<Collection<ConfigUserModel>> Get(
            [FromQuery] int? accountId = null,
            [FromQuery] bool? includeDeleted = false)
        {
            return await InternalGetAsync(await this.GetCompaniesForRequestAsync(), accountId, includeDeleted, WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager);
        }

        /// <summary>
        /// Get Internal User Model
        /// </summary>
        /// <param name="accountId"></param>
        /// <param name="includeDeleted"></param>
        /// <param name="includeAllCompanies"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("full")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        public async Task<Collection<InternalUserModel>> Full(
            [FromQuery] int? accountId = null,
            [FromQuery] bool? includeDeleted = false,
            [FromQuery] bool includeAllCompanies = false)
        {
            var companies = await this.GetCompaniesForRequestAsync(includeAllCompanies);
            var uk = await UserKey.GetByProviderIdAsync(Provider.Towbook.ProviderId, "ReferenceNumber");
            var userKeys = companies.SelectMany(r => UserKeyValue.GetByCompany(r.Id, Provider.Towbook.ProviderId))
                .Where(o => o.KeyId == uk.Id).ToCollection();

            return (await InternalGetFullAsync(companies, accountId, includeDeleted))
                .Select(u => new InternalUserModel
                {
                    Id = u.Id,
                    CompanyId = u.PrimaryCompanyId,
                    FullName = u.FullName,
                    Username = u.Username,
                    Type = u.Type,
                    Disabled = u.Disabled,
                    Deleted = u.Deleted,
                    LastLogin = u.LastLogin,
                    LastLoginIOS = u.LastLoginIOS,
                    LastLoginAndroid = u.LastLoginAndroid,
                    Email = u.Email,
                    ProfilePhotoUrl = u.HasProfilePhoto ? $"/users/{u.Id}/profileImage" : null,
                    ReferenceNumber = userKeys.FirstOrDefault(r => r.UserId == u.Id)?.Value,
                })
                .GroupBy(u => u.Id)
                .Select(u => u.First())
                .OrderBy(u => u.Type)
                .ThenBy(u => u.FullName)
                .ToCollection();
        }

        /// <summary>
        /// Internal Model Get
        /// </summary>
        /// <param name="companies"></param>
        /// <param name="accountId"></param>
        /// <param name="returnDeleted"></param>
        /// <param name="includePhones"></param>
        /// <returns></returns>
        public static async Task<Collection<ConfigUserModel>> InternalGetAsync(
            Company.Company[] companies = null,
            int? accountId = null,
            bool? returnDeleted = false,
            bool includePhones = true)
        {
            var uk = await UserKey.GetByProviderIdAsync(Provider.Towbook.ProviderId, "ReferenceNumber");
            var userKeys = companies.SelectMany(r => UserKeyValue.GetByCompany(r.Id, Provider.Towbook.ProviderId))
                .Where(o => o.KeyId == uk.Id).ToCollection();

            return (await InternalGetFullAsync(companies, accountId, returnDeleted)).Select(o => new ConfigUserModel()
            {
                Id = o.Id,
                CompanyId = o.PrimaryCompanyId,
                Name = o.FullName,
                Type = o.Type,
                Disabled = o.Disabled,
                Deleted = o.Deleted == true ? (bool?)o.Deleted : null,
                MobilePhone = includePhones && !string.IsNullOrWhiteSpace(o.MobilePhone) ? o.MobilePhone : null,
                ProfilePhotoUrl = o.HasProfilePhoto ? $"/users/{o.Id}/profileImage" : null,
                ReferenceNumber = userKeys.FirstOrDefault(r => r.UserId == o.Id)?.Value,
            }).GroupBy(o => o.Id)
              .Select(o => o.First())
              .ToCollection();
        }

        /// <summary>
        /// Internal Full Model
        /// </summary>
        /// <param name="companies"></param>
        /// <param name="accountId"></param>
        /// <param name="returnDeleted"></param>
        /// <returns></returns>
        private static async Task<Collection<User>> InternalGetFullAsync(
            Company.Company[] companies = null,
            int? accountId = null,
            bool? returnDeleted = false)
        {
            var users = await Towbook.User.GetByCompanyIdsAsync(
                (companies ?? new Company.Company[] { WebGlobal.CurrentUser.Company })
                    .Select(o => o.Id).ToArray(),
                returnDeleted.GetValueOrDefault());

            if (accountId != null)
            {
                var accUsers = Extric.Towbook.User.GetByAccountId(accountId.Value, returnDeleted);
                if (accUsers != null)
                {
                    foreach (var accUser in accUsers)
                    {
                        if (await WebGlobal.CurrentUser.HasAccessToCompanyAsync(accUser.CompanyId))
                            users.Add(accUser);
                    }
                }
            }
            return users;
        }

        /// <summary>
        /// Internal Get (used by AJAX)
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [NonAction]
        public async Task<UserModel> InternalGetUser(int id)
        {
            var user = await Towbook.User.GetByIdAsync(id);
            var userModel = new UserModel();

            if (user != null)
            {
                userModel = await UserModel.Map(user, false);
            
                //Map user address
                var detail = UserDetail.GetByUserId(id);
                userModel = await UserModel.Map(user, detail);
            }
            else
            {
                userModel = null;
            }

            return userModel;
        }

        /// <summary>
        /// Get UserModel for a User.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            var userModel = await InternalGetUser(id);

            if (userModel == null || !(await WebGlobal.CurrentUser.HasAccessToCompanyAsync(userModel.CompanyId)))
            {
                throw new TowbookException("Invalid userId or you don't have access to it.");
            }

            return Ok(userModel);
        }

        /// <summary>
        /// Get User History - Towbook stores records about each user 
        /// containing information such as when the user logs in and when it is modified.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("history/{id}")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        public async Task<IEnumerable<UserHistoryModel>> HistoryAsync(int id)
        {
            var user = await Extric.Towbook.User.GetByIdAsync(id);

            if (user == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(user.CompanyId))
            {
                throw new TowbookException("Invalid userId or you don't have access to it.");
            }

            var history = await Extric.Towbook.User.HistoryItem.GetByUserDestinationAsync(user);

            return UserHistoryModel.Map(history);
        }

        /// <summary>
        /// Update a user
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model">>Form data passed as UserModel</param>
        /// <param name="createDriver"></param>
        /// <returns>UserModel</returns>
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPut("{id}")]
        public async Task<UserModel> Put(int id, UserModel model, bool createDriver = false)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(Forbidden())
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.Id < 1)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("You must set the Id in a PUT request. If you're trying to create a new user, use the POST method instead.")
                });
            }

            if (model.Id != id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("The ID in the body of the request doesn't match the ID passed via the incoming URL.")
                });
            }

            if (WebGlobal.CurrentUser.Id == model.Id && model.Disabled)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("You can't disable yourself.")
                });
            }

            // Note: User.CompanyId is current vs. Model (companies & company) are the companies the user is changing to.
            var user = await Towbook.User.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(user.CompanyId);
            await ThrowIfNoCompanyAccessAsync(user.PrimaryCompanyId);

            // This check is specifically for a Manager editing their own access. If a manager has access to the primary company
            // they can add access for child companies. This bypasses the ThrowIfNoCompanyAccess() check since they may not have access yet.
            var editorHasPrimaryCompanyAccess = false;

            if (WebGlobal.CurrentUser.Id == id)
                editorHasPrimaryCompanyAccess = EditorHasMultiCompanyAccessToPrimaryCompany(user.PrimaryCompanyId, model.PrimaryCompanyId, model.Companies);

            // Core checks will run for a manager of a single company, or if a multi-company manager does not pass EditorHasMultiCompanyAccessToPrimaryCompany()
            if (!editorHasPrimaryCompanyAccess)
            {
                if (model.Companies != null)
                    await ThrowIfNoCompanyAccessAsync(model.Companies);

                if (model.PrimaryCompanyId != 0)
                    await ThrowIfNoCompanyAccessAsync(model.PrimaryCompanyId);

                if (model.CompanyId != 0)
                    await ThrowIfNoCompanyAccessAsync(model.CompanyId);
            }

            if (model.Username.ToLowerInvariant() != user.Username.ToLowerInvariant())
            {
                if (await Towbook.User.IsUsernameTaken(model.Username))
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent("The username specified is already in use. Please choose a different one.")
                    });
                }
            }

            if (model.Id == WebGlobal.CurrentUser.Id && model.Type != WebGlobal.CurrentUser.Type)
                model.Type = WebGlobal.CurrentUser.Type;

            var companies = CompanyUser.GetByUserId(user.Id);
            bool companyChanged = (model.PrimaryCompanyId != user.PrimaryCompanyId);

            user = UserModel.Map(model, user);

            if (WebGlobal.CurrentUser.AccountId > 0)
                user.AccountId = WebGlobal.CurrentUser.AccountId;

            //Update companies before saving
            companyChanged = UpdateCompanies(model, user, companies, companyChanged);
            await CacheWorkerUtility.UpdateUser(user);

            await user.Save();

            //Logic from AJAX - if the user was assigned to another company, verify the driver. 
            if (companyChanged)
            {
                await DriverCompanyChanges(user);
            }

            //Save User Details (address)
            var userDetail = UserModel.Map(model, UserDetail.GetByUserId(id));
            if (userDetail != null && !string.IsNullOrEmpty(userDetail.AddressFormatted))
            {
                userDetail.UserId = id;
                userDetail.Save();
            }

            UpdatePermissions(id, model);

            if (createDriver)
                await HandleUserToDriverSetup(user, userDetail);

            // Method will check if the user is assigned to a driver. 
            await UpdateDriverContactInformation(user);

            Towbook.User.HistoryItem.RecordAction(
                Towbook.User.HistoryItem.TypeEnum.Modified,
                user.Id, WebGlobal.CurrentUser.Id, this.GetRequestingIp());

            return await UserModel.Map(user);
        }

        /// <summary>
        /// If the user is a driver, and if so, checks if there is a Driver object associated. 
        /// If not, it creates one. If it can find one with the same name, it links to that driver account instead of creating a new Driver.
        /// </summary>
        /// <param name="u"></param>
        /// <param name="userDetail"></param>
        private async Task HandleUserToDriverSetup(User u, UserDetail userDetail)
        {
            if (u == null)
                throw new ArgumentException("user is null", "u");

            // Only handle this setup for drivers or spotters
            if (!IsDriver(u))
                return;

            var createDriver = false;

            var drivers = Driver.GetByUserId(u.Id);

            //No Driver found by UserId - Find Drivers with names that match (for Phone# Update)
            if (drivers == null || !drivers.Any())
            {
                //Check any existing driver names that match a user
                var existingDriversWithName = Driver.GetByCompany(u.Company)
                       .Where(o => o.Name.ToLowerInvariant().Trim() == u.FullName.ToLowerInvariant().Trim()
                                   && o.UserId == 0);

                //Driver phone# update
                if (existingDriversWithName.Any())
                {
                    foreach (var existingDriver in existingDriversWithName)
                    {
                        //If driver phone# is empty, update it to user phone#
                        if (!string.IsNullOrWhiteSpace(u.MobilePhone)
                            && string.IsNullOrWhiteSpace(existingDriver.MobilePhone))
                        {
                            existingDriver.MobilePhone = Core.FormatPhone(u.MobilePhone, true);
                        }

                        existingDriver.UserId = u.Id;

                        await existingDriver.Save();

                        Driver.UpdateGlobalDriverCache(existingDriver, await WebGlobal.GetCompaniesAsync());
                    }
                }
                else
                {
                    //None found, create a driver
                    createDriver = true;
                }
            }

            if (!createDriver)
                return;

            //New Driver Add
            Driver d = new Driver
            {
                UserId = u.Id,
                CompanyId = u.CompanyId,
                Name = u.FullName,
                Email = u.Email,
                Active = true,
                MobilePhone = u.MobilePhone
            };

            if (u.Type == Towbook.User.TypeEnum.Driver)
            {
                d.Type = DriverType.Driver;
            }
            else if (u.Type == Towbook.User.TypeEnum.Spotter)
            {
                d.Type = DriverType.Spotter;
            }

            if (Core.IsPhoneValidStandard(d.MobilePhone))
            {
                var mms = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                    Provider.Towbook.ProviderId, "FreeTexting_UseMmsForLookups") == "1";

                var smsEmail = await SmsEmailHelper.GetEmailFromPhone(d.MobilePhone, mms);

                if (smsEmail != null)
                {
                    d.DispatchingNotificationType = DriverDispatchingNotificationType.Email;
                    d.DispatchingNotificationValue = smsEmail;
                }
                else
                {
                    d.DispatchingNotificationType = DriverDispatchingNotificationType.None;
                    d.DispatchingNotificationValue = null;
                }
            }

            // Set driver contact information from user
            d.Address = userDetail.Address;
            d.City = userDetail.City;
            d.State = userDetail.State;
            d.Zip = userDetail.Zip;

            d.EmergencyContactName = u.EmergencyContactName;
            d.EmergencyContactPhone = u.EmergencyContactPhone;

            var model = DriverModel.Map(d);

            //Call Driver Post API
            var api = new DriversController();
            await api.Post(model);
        }

        /// <summary>
        /// Create a new user
        /// </summary>
        /// <param name="model">form data passed as UserModel</param>
        /// <param name="createDriver">Create a driver</param>
        /// <returns>UserModel</returns>
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPost]
        public async Task<UserModel> Post(UserModel model, bool createDriver=false)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.Id > 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You shouldn't set the ID in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            if (await Towbook.User.IsUsernameTaken(model.Username))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("The username specified is already in use. Please choose a different one.")
                });
            }

            if (!Towbook.User.IsUsernameValid(model.Username))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Invalid username.")
                });
            }

            if (model.PrimaryCompanyId > 0)
                await ThrowIfNoCompanyAccessAsync(model.PrimaryCompanyId);

            if (model.CompanyId > 0)
                await ThrowIfNoCompanyAccessAsync(model.CompanyId);

            var user = new User();

            if (model.CompanyId == 0)
            {
                model.CompanyId = WebGlobal.CurrentUser.CompanyId;
            }

            //Defaults for new user
            var companies = Array.Empty<CompanyUser>();
            bool companyChanged = false;

            user.CompanyId = model.CompanyId;
            user = UserModel.Map(model, user);

            if (WebGlobal.CurrentUser.AccountId > 0)
                user.AccountId = WebGlobal.CurrentUser.AccountId;

            await user.Save();

            companyChanged = UpdateCompanies(model, user, companies, companyChanged);
            await CacheWorkerUtility.UpdateUser(user);

            if (companyChanged)
            {
                await DriverCompanyChanges(user);
            }

            //Save User Details (address)
            var userDetail = UserModel.Map(model, UserDetail.GetByUserId(user.Id));
            if (userDetail != null && !string.IsNullOrEmpty(userDetail.AddressFormatted))
            {
                userDetail.UserId = user.Id;
                userDetail.Save();
            }

            // Driver Contact Info handled in the POST to driver
            if (createDriver)
                await HandleUserToDriverSetup(user, userDetail);

            // Method will check if the user is assigned to a driver. 
            await UpdateDriverContactInformation(user);

            //Record history
            Towbook.User.HistoryItem.RecordAction(
                Towbook.User.HistoryItem.TypeEnum.Created,
                user.Id, WebGlobal.CurrentUser.Id, this.GetRequestingIp());

            //Logic from AJAX
            UpdatePermissions(user.Id, model);

            //Send email
            await SendEmail(user.Email, user.FullName, user.Username, user.Password,
                 $"Download the free Towbook mobile app.\n" +
                 $"Apple Store: https://itunes.apple.com/us/app/towbook/id670258292?mt=8 \n" +
                 $"Google Play: https://play.google.com/store/apps/details?id=com.towbook.mobile");

            //Send Text
            if (!string.IsNullOrEmpty(user.MobilePhone))
            {
                await SendText(user);
            }

            return await UserModel.Map(user);
        }

        /// <summary>
        /// Delete a user
        /// </summary>
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.SystemAdministrator)]
        [HttpDelete("{id}")]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var user = await Extric.Towbook.User.GetByIdAsync(id);

            if (user == null)
                return new HttpResponseMessage(HttpStatusCode.OK);
      
            await ThrowIfNoCompanyAccessAsync(user.PrimaryCompanyId);
            await ThrowIfNoCompanyAccessAsync(user.CompanyId);

            if (user.Id == WebGlobal.CurrentUser.Id)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Deleting of the account that you're logged into is not allowed.")
                });
            }

            await user.Delete(WebGlobal.CurrentUser, WebGlobal.GetRequestingIp());

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        /// <summary>
        /// Restore a deleted user
        /// </summary>
        [HttpPost("{id}/undelete")]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager,
            Extric.Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<HttpResponseMessage> Undelete(int id)
        {
            var user = await Extric.Towbook.User.GetByIdAsync(id);
            
            await ThrowIfNoCompanyAccessAsync(user.CompanyId);
            await ThrowIfNoCompanyAccessAsync(user.PrimaryCompanyId);

            await user.Undelete(WebGlobal.CurrentUser, WebGlobal.GetRequestingIp());

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        /// <summary>
        /// Checks in the specified user.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("{id}/checkin")]
        [ApiPermission(Towbook.User.TypeEnum.Manager,
            Towbook.User.TypeEnum.Dispatcher,
            Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<HttpResponseMessage> CheckIn(int id)
        {
            var u = await Towbook.User.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(u?.CompanyId, "user");

            decimal latitude = 0, longitude = 0;

            var uli = await UserLocationHistoryItem.GetCurrentByUserIdAsync(u.Id, DateTime.Now,
                DateTime.Now.AddMinutes(-10));

            if (uli != null)
            {
                if (uli.Timestamp > DateTime.Now.AddMinutes(-10))
                {
                    latitude = uli.Latitude;
                    longitude = uli.Longitude;

                }
            }

            if (await UserController.CheckUserInOrOut(true,
                latitude,
                longitude,
                WebGlobal.CurrentUser, u))
            {
                return new HttpResponseMessage() { StatusCode = HttpStatusCode.Created };
            }
            else
            {
                return new HttpResponseMessage() { StatusCode = HttpStatusCode.OK };
            }
        }

        /// <summary>
        /// Checks out the specified User.
        /// </summary>
        /// <param name="id"></param>
        [HttpPost("{id}/checkout")]
        [ApiPermission(Towbook.User.TypeEnum.Manager,
            Towbook.User.TypeEnum.Dispatcher,
            Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<HttpResponseMessage> CheckOut(int id)
        {
            var u = await Towbook.User.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(u?.CompanyId, "user");

            decimal latitude = 0, longitude = 0;

            var uli = await UserLocationHistoryItem.GetCurrentByUserIdAsync(u.Id, DateTime.Now,
                DateTime.Now.AddMinutes(-10));

            if (uli != null)
            {
                if (uli.Timestamp > DateTime.Now.AddMinutes(-10))
                {
                    latitude = uli.Latitude;
                    longitude = uli.Longitude;

                }
            }

            if (await UserController.CheckUserInOrOut(false,
                latitude,
                longitude,
                WebGlobal.CurrentUser, u))
            {
                return new HttpResponseMessage() { StatusCode = HttpStatusCode.Created };
            }
            else
            {
                return new HttpResponseMessage() { StatusCode = HttpStatusCode.OK };
            }
        }

        /// <summary>
        /// Excel Export Drivers
        /// </summary>
        /// <returns></returns>
        [HttpGet("export")]
        public async Task<HttpResponseMessage> Export()
        {
            ExcelPackage.LicenseContext = LicenseContext.Commercial;
            var exportData = new List<IDictionary<string, object>>();

            //Get users 
            List<UserModel> model = null;

            if (WebGlobal.CurrentUser.AccountId > 0)
                model = (await UserModel.Map(Extric.Towbook.User.GetByAccountId(WebGlobal.CurrentUser.AccountId))).OrderBy(u => u.FullName).ToList();
            else
                model = (await UserModel.Map(Extric.Towbook.User.GetByCompanyId(WebGlobal.CurrentUser.Company.Id))).OrderBy(u => u.FullName).ToList();

            var uk = await UserKey.GetByProviderIdAsync(Provider.Towbook.ProviderId, "ReferenceNumber");
            var userKeys = UserKeyValue.GetByCompany(WebGlobal.CurrentUser.Company.Id, Provider.Towbook.ProviderId)
                .Where(o => o.KeyId == uk.Id).ToCollection();

            // Create a new Excel package
            using (var excelPackage = new ExcelPackage())
            {
                // Add a worksheet to the workbook
                var worksheet = excelPackage.Workbook.Worksheets.Add("Drivers");

                // Define header names and corresponding property names
                var headers = new string[]
                {
                    "Name", "Type", "Username", "Address", "City", "State", "Zip", "Mobile Phone", "Work Phone",
                    "Email", "Last Login", "Last Login Android","Last Login iPhone", "Notes", "ReferenceNumber"
                };

                if ((await WebGlobal.GetCompaniesAsync()).Count() > 1)
                {
                    Array.Resize(ref headers, headers.Length + 2);
                    headers[headers.Length - 2] = "Primary Company";
                    headers[headers.Length - 1] = "Access To";
                }

                //Make the header row bold
                using (var range = worksheet.Cells["A1:P1"])
                {
                    range.Style.Font.Bold = true;
                }

                // Add headers to the first row of the worksheet
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                }
   
                var userTypes = Enum.GetValues(typeof(User.TypeEnum))
                    .Cast<User.TypeEnum>()
                    .Select(x => new ExportEnumWrapper { Id = (int)x, Name = x });

                var companies = (await Task.WhenAll(
                SharedCompany.GetByCompanyId(WebGlobal.CurrentUser.CompanyId)
                .Select(async o => await Extric.Towbook.Company.Company.GetByIdAsync(o.SharedCompanyId))
                    ))
                .Where(o => o != null)
                .Select(y => new ExportEnumIdName
                {
                    Id = y.Id,
                    Name = y.ShortName ?? y.Name
                })
                .GroupBy(g => g.Id)
                .Select(y => y.First())
                .ToArray();

                int rowId = 0;
                foreach (var user in model.Where(w => w.Disabled != true))
                {
                    var detail = UserDetail.GetByUserId(user.Id);

                    worksheet.Cells[rowId + 2, 1].Value = user.FullName;
                    worksheet.Cells[rowId + 2, 2].Value = Enum.GetName(typeof(User.TypeEnum), user.Type) ?? string.Empty;
                    worksheet.Cells[rowId + 2, 3].Value = user.Username;
                    worksheet.Cells[rowId + 2, 4].Value = detail?.Address ?? string.Empty;
                    worksheet.Cells[rowId + 2, 5].Value = detail?.City ?? string.Empty;
                    worksheet.Cells[rowId + 2, 6].Value = detail?.State ?? string.Empty;
                    worksheet.Cells[rowId + 2, 7].Value = detail?.Zip ?? string.Empty;
                    worksheet.Cells[rowId + 2, 8].Value = Core.FormatPhone(user.MobilePhone, WebGlobal.CurrentUser.Company);
                    worksheet.Cells[rowId + 2, 9].Value = Core.FormatPhone(user.OfficePhone, WebGlobal.CurrentUser.Company);
                    worksheet.Cells[rowId + 2, 10].Value = user.Email;
                    worksheet.Cells[rowId + 2, 11].Value = user.LastLogin != System.DateTime.MinValue ? Core.OffsetDateTime(WebGlobal.CurrentUser.Company, user.LastLogin, false).ToShortDateString() : string.Empty;
                    worksheet.Cells[rowId + 2, 12].Value = user.LastLoginAndroid != null ? Core.OffsetDateTime(WebGlobal.CurrentUser.Company, user.LastLoginAndroid.Value, false).ToShortDateString() : string.Empty;
                    worksheet.Cells[rowId + 2, 13].Value = user.LastLoginIOS != null ? Core.OffsetDateTime(WebGlobal.CurrentUser.Company, user.LastLoginIOS.Value, false).ToShortDateString() : string.Empty;
                    worksheet.Cells[rowId + 2, 14].Value = user.Notes;
                    worksheet.Cells[rowId + 2, 15].Value = userKeys.FirstOrDefault(f => f.UserId == user.Id)?.Value ?? string.Empty;

                    if ((await WebGlobal.GetCompaniesAsync()).Count() > 1)
                    {
                        worksheet.Cells[rowId + 2, 15].Value = (await WebGlobal.GetCompaniesAsync()).FirstOrDefault(f => f.Id == user.PrimaryCompanyId)?.Name ?? string.Empty;

                        worksheet.Cells[rowId + 2, 16].Value = string.Join(", ", user.Companies
                                .Where(w => companies.Any(a => a.Id == w))
                                .Select(s => companies.First(f => f.Id == s).Name)
                                .GroupBy(g => g)
                                .Select(y => y.First()));
                    }
                    rowId++;
                }

                // Auto fit columns for better readability
                worksheet.Cells.AutoFitColumns();

                //Prepare to export
                using var stream = new MemoryStream();
                excelPackage.SaveAs(stream);
                stream.Position = 0;

                var result = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new ByteArrayContent(stream.ToArray())
                };

                result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
                {
                    FileName = "Export-Users.xlsx"
                };

                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                return result;
            }
        }


        #region Export Private Models
        private class ExportEnumIdName
        {
            public int Id { get; set; }
            public string Name { get; set; }
        }
        private class ExportEnumWrapper
        {
            public int Id { get; set; }
            public User.TypeEnum Name { get; set; }
        }
        #endregion

        /// <summary>
        /// Notify user that a user account was created.  Send loging credentials.
        /// Note: Exceptions are logged silently, per the original AJAX controller
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("{id}")]
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<HttpResponseMessage> UserAccountNotification(int id)
        {
            var user = await Towbook.User.GetByIdAsync(id);

            if (user == null)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The user doesn't exist.")
                });
            }

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(user.CompanyId))
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified companyId doesn't exist or you don't have access to it.")
                });
            }

            var additional = "";

            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.SystemAdministrator)
            {
                additional = " by " + WebGlobal.CurrentUser.FullName;
            }

            if (!string.IsNullOrWhiteSpace(user.Email))
            {
                try
                {
                    var msg = new MailMessage();

                    msg.From = new MailAddress("<EMAIL>", "Towbook");
                    msg.To.Add(user.Email);
                    msg.Priority = MailPriority.High;
                    msg.Subject = "Your Towbook Login for " + WebGlobal.CurrentUser.Company.Name;

                    msg.IsBodyHtml = false;
                    msg.Body = user.FullName + ",\n\n";
                    msg.Body += "You have been setup to access " + WebGlobal.CurrentUser.Company.Name + "'s Towbook" + additional + ".\n\n";

                    msg.Body += "Username: " + user.Username + "\n";
                    msg.Body += "Password: " + user.Password + "\n\n";

                    msg.Body += "To login, download the free Towbook mobile app for your device " +
                        "by visiting https://towbook.com/app from your phone.\n\n";

                    msg.Body += "Let us know if you have any questions!\n\n";

                    msg.Body += "---\n";
                    msg.Body += "Towbook\n";
                    msg.Body += "<EMAIL>\n";

                    var metaData = new Dictionary<string, string>()
                    {
                        { "userId", user.Id.ToString() },
                        { "userName", user.Username }
                    };

                    using (var sc = new SmtpClient().Get())
                    {
                        await sc.Send(msg, WebGlobal.CurrentUser, metaData, "new_user_confirmation");
                    }

                }
                catch (Exception e)
                {
                    logger.LogExceptionEvent("Driver Deliver New Message Exception", e, WebGlobal.CurrentUser);
                }
            }

            //Send Text Message
            if (!string.IsNullOrWhiteSpace(user.MobilePhone))
            {
                var message = $"Your Towbook login has been setup{additional} to access {WebGlobal.CurrentUser.Company.Name}.\n" +
                    $"Username: {user.Username}\nPassword: {user.Password}\n" +
                    $"Download the free Towbook mobile app.\nhttps://towbook.com/app";

                var destination = user.MobilePhone;
                if (!string.IsNullOrWhiteSpace(destination))
                {
                    try
                    {
                        await DispatchNotificationMessage.SendAsync(user, destination, message);
                    }
                    catch (Exception e)
                    {
                        logger.LogExceptionEvent("Driver SendText Exception", e, WebGlobal.CurrentUser);
                    
                        // Fail silently....
                        // Keep the queue item going.  We don't want to cause a restart and send multiple messages until abandoned.
                        throw;
                    }
                }
            }

            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent("Notification sent")
            };
        }

        /// <summary>
        /// Update User Permissions
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        private void UpdatePermissions(int id, UserModel model)
        {
            if (model.CallRequestCompanies != null)
            {
                UserKeyValue.InsertOrUpdate(model.CompanyId, id, Provider.Towbook.ProviderId,
                    "CallRequests_CompanyIds", model.CallRequestCompanies.ToJson());
            }

            if (model.ReferenceNumber != null)
            {
                UserKeyValue.InsertOrUpdate(model.CompanyId, id, Provider.Towbook.ProviderId,
                    "ReferenceNumber", model.ReferenceNumber);
            }

            UserKeyValue.InsertOrUpdate(model.CompanyId, id, Provider.Towbook.ProviderId,
                "Permission_Calls_Audit", (model.PermissionCallsAudit == true) ? "1" : "0");

            UserKeyValue.InsertOrUpdate(model.CompanyId, id, Provider.Towbook.ProviderId,
                "Permission_Calls_Unaudit", (model.PermissionCallsUnaudit == true) ? "1" : "0");

            UserKeyValue.InsertOrUpdate(model.CompanyId, id, Provider.Towbook.ProviderId,
                "Permission_Calls_Unlock", (model.PermissionCallsUnlock == true) ? "1" : "0");

            UserKeyValue.InsertOrUpdate(model.CompanyId, id, Provider.Towbook.ProviderId,
                "Permission_Calls_Lock", (model.PermissionCallsLock == true) ? "1" : "0");
        }

        /// <summary>
        /// Logic from AJAX. This replaces the logic in the PUT and POST. 
        /// The PUT API had part of this logic but it was not complete. 
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <param name="companies"></param>
        /// <param name="companyChanged"></param>
        private bool UpdateCompanies(UserModel model, User user, CompanyUser[] companies, bool companyChanged)
        {
            if (model.Companies != null)
            {
                // model.Companies - new list of companies to have access. 
                // companies - list from the database

                // delete any that aren't selected
                foreach (var companyUser in companies.Where(o => !model.Companies.Contains(o.CompanyId)))
                {
                    companyUser.Delete();
                    companyChanged = true;
                }

                // add ones that are missing.
                foreach (int companyId in model.Companies.Where(newCo => companies.All(existingCo => existingCo.CompanyId != newCo)))
                {
                    var companyUser = new CompanyUser
                    {
                        UserId = user.Id,
                        CompanyId = companyId
                    };

                    companyUser.Save();
                    companyChanged = true;
                }
            }
            else if (companies != null && companies.Length > 0)
            {
                // no companies selected, see if any exist and delete them.
                foreach (var companyUser in companies)
                {
                    companyUser.Delete();
                    companyChanged = true;
                }
            }

            return companyChanged;
        }

        /// <summary>
        /// Check if the user is a driver
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        private bool IsDriver(User u)
        {
            return (u.Type == Towbook.User.TypeEnum.Driver || u.Type == Towbook.User.TypeEnum.Spotter);
        }

        /// <summary>
        /// Update driver contact information from user. 
        /// The user can be of any type, but must be associated to a driver. 
        /// </summary>
        /// <param name="user"></param>
        private async Task UpdateDriverContactInformation(User user)
        {
            var driver = Driver.GetByUserId(user.Id).FirstOrDefault();

            if (driver == null)
                return;

            var userDetails = UserDetail.GetByUserId(user.Id);
            if (userDetails != null)
            {
                driver.Address = userDetails.Address;
                driver.City = userDetails.City;
                driver.State = userDetails.State;
                driver.Zip = userDetails.Zip;
            }

            if (user.EmergencyContactName != null)
                driver.EmergencyContactName = user.EmergencyContactName;

            if (user.EmergencyContactPhone != null)
                driver.EmergencyContactPhone = user.EmergencyContactPhone;

            if (user.BirthDate != null)
                driver.BirthDate = user.BirthDate;

            await driver.Save();

            Driver.UpdateGlobalDriverCache(driver, await WebGlobal.GetCompaniesAsync());
        }

        /// <summary>
        /// Handle driver company changes
        /// </summary>
        /// <param name="u"></param>
        /// <returns></returns>
        private async Task DriverCompanyChanges(User u)
        {
            var drivers = Driver.GetByUserId(u.Id);
            if (drivers.Any())
            {
                foreach (var driver in drivers)
                {
                    if (driver.CompanyId != u.PrimaryCompanyId)
                    {
                        driver.CompanyId = u.PrimaryCompanyId;
                        await driver.Save(WebGlobal.CurrentUser);
                    }
                }
            }
        }

        #region Send User Email and Text
        /// <summary>
        /// Send Email
        /// </summary>
        /// <param name="email"></param>
        /// <param name="name"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="optionalMessage"></param>
        /// <returns></returns>
        private static async Task SendEmail(string email, string name, string username, string password, string optionalMessage)
        {
            try
            {
                using (var msg = new MailMessage())
                {
                    var additional = "";

                    if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.SystemAdministrator)
                        additional = " by " + WebGlobal.CurrentUser.FullName;

                    msg.From = new MailAddress("<EMAIL>", "Towbook");
                    msg.To.Add(email);
                    msg.Priority = MailPriority.High;
                    msg.Subject = "Your Towbook Login for " + WebGlobal.CurrentUser.Company.Name;

                    msg.IsBodyHtml = false;
                    msg.Body = name + ",\n\n";
                    msg.Body += "You have been setup to access " + WebGlobal.CurrentUser.Company.Name + "'s Towbook" +
                                additional + ".\n\n";

                    msg.Body += "Username: " + username + "\n";
                    msg.Body += "Password: " + password + "\n\n";

                    if (string.IsNullOrWhiteSpace(optionalMessage))
                        msg.Body +=
                            "To login, visit https://towbook.com and click on Login at the top of the page. On your phone or tablet? Find Towbook for your device from your iPhone App Store or Google Play store.\n\n";
                    else
                        msg.Body += optionalMessage + "\n\n";

                    msg.Body += "Let us know if you have any questions!\n\n";

                    msg.Body += "---\n";
                    msg.Body += "Towbook\n";
                    msg.Body += "<EMAIL>\n";

                    var metaData = new Dictionary<string, string>()
               {
                   { "name", name },
                   { "userName", username }
               };

                    using (var sc = new SmtpClient().Get())
                    {
                        await sc.Send(msg, WebGlobal.CurrentUser, metaData, "new_user_confirmation");
                    }
                }
            }
            catch (Exception e)
            {
                logger.LogExceptionEvent($"Could not send email to user: {username}", e, WebGlobal.CurrentUser);
            }
        }

        /// <summary>
        /// Send Text
        /// </summary>
        /// <param name="user"></param>
        private static async Task SendText(User user)
        {
            try
            {
                var additional = "";

                if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.SystemAdministrator)
                    additional = " by " + WebGlobal.CurrentUser.FullName;

                var message =
                    $"Your Towbook login has been setup{additional} to access {WebGlobal.CurrentUser.Company.Name}.\n" +
                    $"Username: {user.Username}\n" +
                    $"Password: {user.Password}\n" +
                    "Download the free Towbook mobile app.\n" +
                    "Apple Store: https://itunes.apple.com/us/app/towbook/id670258292?mt=8 \n" +
                    "Google Play: https://play.google.com/store/apps/details?id=com.towbook.mobile";

                // get destination (phone number)
                var destination = user.MobilePhone;
                
                if (!string.IsNullOrWhiteSpace(destination))
                {
                    await DispatchNotificationMessage.SendAsync(user, destination, message);
                }
            }
            catch (Exception e)
            {
                logger.LogExceptionEvent($"Could not send text to user: {user.FullName}", e, WebGlobal.CurrentUser);
            }
        }
        #endregion


        #region User Profile Photos
        /// <summary>
        /// Get user profile photo
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Route("{userId}/profileImage")]
        [HttpGet]
        public async Task<HttpResponseMessage> ProfileImage(int userId)
        {
            var user = await Towbook.User.GetByIdAsync(userId);
            var userModel = await UserModel.Map(user);

            if (user == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(user.CompanyId))
            {
                throw new TowbookException("Invalid userId or you don't have access to it.");
            }

            if (userModel.HasProfilePhoto == false)
            {
                return new HttpResponseMessage(HttpStatusCode.NoContent)
                {
                    Content = new StringContent("Profile image not found.")
                };
            }

            var filePath = StorageProfilePath(user.PrimaryCompanyId);
            var fileName = $"{userId}.jpg";
            var imageFilePath = string.Empty;

            var isLocalHost = IsLocalHost();

            // Read local storage if localhost or S3 if deployed on the server
            // Handle any issues in a try/catch as not to break mobile or the website if there is an error.
            try
            {
                if (isLocalHost)
                {
                    // Get photo from local S3 storage folder and return a byte array
                    byte[] bytes;

                    imageFilePath = FileUtility.GetLocalPath(filePath);

                    string pathAndName = Path.Combine(imageFilePath, fileName);
                    var imageFile = await FileUtility.GetFileAsync(pathAndName);
                    bytes = System.IO.File.ReadAllBytes(imageFile);

                    var response = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = new ByteArrayContent(bytes)
                    };

                    response.Content.Headers.ContentType = new MediaTypeHeaderValue("image/jpeg");
                    return response;
                }
                else
                {
                    // Get photo path from S3 and return a redirect
                    imageFilePath = Path.Combine(Path.DirectorySeparatorChar.ToString(), filePath, fileName);

                    // Create a new response vs. Request.CreateResponse because this method is also called by AJAX directly
                    var response = new HttpResponseMessage(HttpStatusCode.MovedPermanently);

                    response.Headers.Location = new Uri(FileUtility.GetPresignedUrlForDownloadFromClient(imageFilePath, "image/jpeg", 30));
                    return response;
                }
            }
            catch (Exception e)
            {
                var msg = "Failed to read the user photo on local storage";

                // Running on the server, log the exception
                if (!isLocalHost)
                {
                    msg = $"Failed to obtain the user photo from S3";

                    logger.LogExceptionEvent(msg + $" for userId:{userId}", e, WebGlobal.CurrentUser);
                }

                return new HttpResponseMessage(HttpStatusCode.NoContent)
                {
                    Content = new StringContent(msg)
                };
            }
        }

        /// <summary>
        /// Upload a photo for a user (can be called by a manager or user)
        /// User rights checked in UploadFileToStorage()
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{userId}/photo")]
        [DisableFormValueModelBinding]
        public async Task<HttpResponseMessage> Photo(int userId)
        {
            var file = (await GetFilesFromHttpPostRequest(Web.HttpContext.Current.Request)).FirstOrDefault();

            if (file == null || !file.Exists)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Request is missing file data.")
                });
            }

            await UploadProfilePhoto(userId, file);

            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent("File uploaded successfully.")
            };
        }

        /// <summary>
        /// NonAction method called from AJAX to upload profile photo
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        

        [NonAction]
        public async Task UploadProfilePhoto(int userId, FileInfo file)
        {
            var user = await Towbook.User.GetByIdAsync(userId);
        
            if (user == null)
            {
                throw new TowbookException("Invalid userId or you don't have access to it.");
            }
        
            var userModel = await UserModel.Map(user);
        
            if (userModel == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(userModel.CompanyId))
            {
                throw new TowbookException("Invalid userId or you don't have access to it.");
            }
        
            if (file.Length == 0)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("No file is present on request to upload.")
                });
            }
        
            await UploadProfilePhotoToStorage(userId, file);
        }

        /// <summary>
        /// Shared method from POST and UploadFile file
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="file"></param>
        /// <returns></returns>
        
        private async Task UploadProfilePhotoToStorage(int userId, FileInfo file)
        {
            try
            {
                var user = await Towbook.User.GetByIdAsync(userId);
        
                // Any image file will be converted to .jpg
                var filePath = StorageProfilePath(user.PrimaryCompanyId);
                var fileName = $"{userId}.jpg";
                var imageFilePath = string.Empty;
        
                // When running on localhost get the path where S3 files are downloaded.
                // Uploaded files will be saved here and NOT sent to S3 on localhost.
                // This allows uploaded profile images to show on the UI on localhost and not pushed to S3.
                if (IsLocalHost())
                {
                    // Path could be: C:\Storage\{x}\userProfilePhotos
                    imageFilePath = FileUtility.GetLocalPath(filePath);
                }
                else
                {
                    // Mapping when running on the server (other file uploads will use this path on localhost too)
                    // C:\Towbook\app\Storage\Files\Companies\
                    imageFilePath = Path.Combine(Path.GetTempPath(), "Towbook", filePath);
                }
        
                if (!Directory.Exists(imageFilePath))
                {
                    Directory.CreateDirectory(imageFilePath);
                }
        
                //Temporary file used for resizing
                string tempPathAndName = Path.Combine(imageFilePath, "tmp-" + fileName);
                file.CopyTo(tempPathAndName, true);
                
                // Final image path and name
                string outputFileName = Path.Combine(imageFilePath, fileName);
        
                using (var img = SKBitmap.Decode(tempPathAndName))
                {
                    //Size up or down to 640
                    using (var result = img.ResizeProportionately(640))
                    {
                        //Final image size to 512x512
                        var cropped = CropImageToCenter(result);
                        cropped.Save(outputFileName, SKEncodedImageFormat.Jpeg);
        
                    }
                }
        
                //Delete the temporary file
                System.IO.File.Delete(tempPathAndName);
              
                // Check if the file is safe (virus scan)
                if (!DefenderApi.IsFileUnsafe(outputFileName))
                {
                    // Upload file to S3 and delete the local file
                    if (!IsLocalHost())
                    {
                        var result = await FileUtility.SendFileAsync(outputFileName);
                        if (result.IsHttpSuccess())
                            System.IO.File.Delete(outputFileName);
                    }
                }
                else
                {
                    System.IO.File.Delete(outputFileName);
        
                    throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                    {
                        Content = new StringContent("File upload failed virus scanning.")
                    });
                }
        
                //Update the User 
                user.HasProfilePhoto = true;
                await user.Save();
            }
            catch (Exception e)
            {
                throw new TowbookException("Error uploading file.", e);
            }
        }

        /// <summary>
        /// Delete user profile photo
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        
        
        [HttpDelete]
        [Route("{userId}/DeleteProfileImage")]
        public async Task<HttpResponseMessage> DeleteProfileImage([FromRoute]int userId)
        {
            var user = await Towbook.User.GetByIdAsync(userId);
            var userModel = await UserModel.Map(user);
        
            if (user == null || !userModel.Companies.Contains(WebGlobal.CurrentUser.CompanyId))
            {
                throw new TowbookException("Invalid userId or you don't have access to it.");
            }
        
            // Set the user profile file id to null 
            user.HasProfilePhoto = false;
            await user.Save();
        
            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        /// <summary>
        /// Image was resized Proportionately. Now crop it to 512x512 from the center.
        /// </summary>
        /// <param name="originalImage"></param>
        /// <param name="cropWidth"></param>
        /// <param name="cropHeight"></param>
        /// <returns></returns>
        public static SKBitmap CropImageToCenter(SKBitmap originalImage, int cropWidth = 512, int cropHeight = 512)
        {
            SKBitmap resizedImage = originalImage;

            // Step 1: Resize if the image is smaller than the target size
            if (originalImage.Width < cropWidth || originalImage.Height < cropHeight)
            {
                float widthScale = (float)cropWidth / originalImage.Width;
                float heightScale = (float)cropHeight / originalImage.Height;
                float scaleFactor = Math.Max(widthScale, heightScale); // Ensure both sides are larger than 512

                int newWidth = (int)(originalImage.Width * scaleFactor);
                int newHeight = (int)(originalImage.Height * scaleFactor);

                // Create a new SKBitmap for the resized image
                SKBitmap scaledBitmap = new SKBitmap(newWidth, newHeight);

                using (SKCanvas canvas = new SKCanvas(scaledBitmap))
                {
                    canvas.Clear(SKColors.Transparent);
                    SKRect destRect = new SKRect(0, 0, newWidth, newHeight);
                    canvas.DrawBitmap(originalImage, destRect);
                }

                // Dispose of the original image, as it is no longer needed
                originalImage.Dispose();
                resizedImage = scaledBitmap;
            }

            // Step 2: Calculate the starting point to crop the image from the center
            int cropX = (resizedImage.Width / 2) - (cropWidth / 2);
            int cropY = (resizedImage.Height / 2) - (cropHeight / 2);

            // Ensure cropX and cropY are not negative
            cropX = Math.Max(0, cropX);
            cropY = Math.Max(0, cropY);

            // Make sure the crop area does not exceed the bounds of the image
            cropWidth = Math.Min(cropWidth, resizedImage.Width - cropX);
            cropHeight = Math.Min(cropHeight, resizedImage.Height - cropY);

            // Step 3: Define the cropping area (rectangle)
            SKRectI cropArea = new SKRectI(cropX, cropY, cropX + cropWidth, cropY + cropHeight);

            Console.WriteLine($"Cropping image to {cropWidth}x{cropHeight} from center at position ({cropX}, {cropY}).");

            // Step 4: Crop the image
            SKBitmap croppedImage = new SKBitmap(cropWidth, cropHeight);
            resizedImage.ExtractSubset(croppedImage, cropArea);

            // Dispose of the resized image, as it is no longer needed
            resizedImage.Dispose();

            return croppedImage;
        }

        /// <summary>
        /// If running on localhost files are saved to the same folder as S3 downloaded files.
        /// This allows uploading of user profile photos to show on the site locally and not 
        /// be pushed to S3.
        /// </summary>
        /// <returns></returns>
        private static bool IsLocalHost()
        {
            var webAppUrl = Core.GetAppSetting("Towbook:WebAppUrl") != null
                ? Core.GetAppSetting("Towbook:WebAppUrl")
                : "https://app.towbook.com";

            if (webAppUrl.Contains("localhost") || webAppUrl.Contains("127.0.0.1"))
            {
                return true;
            }

            return false;
        }
        #endregion

        /// <summary>
        /// If a manager's Primary Company == Parent Company they can assign user access to any child company.
        /// 
        /// The purpose of this method is to allow a Manager with  Primary Company == Parent Company to assign their own child company access rights. 
        /// </summary>
        /// <param name="userPrimaryCompanyId"></param>
        /// <param name="userModelPrimaryCompanyId"></param>
        /// <param name="userModelCompanies"></param>
        /// <returns></returns>
        private bool EditorHasMultiCompanyAccessToPrimaryCompany(int userPrimaryCompanyId, int userModelPrimaryCompanyId, int[] userModelCompanies)
        {
            var isManagerOrAdmin = (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager || WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.SystemAdministrator);

            if (!isManagerOrAdmin)
                return false;

            // Must be a multi-company
            var sharedCompanies = SharedCompany.GetByCompanyId(WebGlobal.CurrentUser.PrimaryCompanyId)
                .Where(s => s.SharedCompanyId > 0);

            // Return false for a single company since this method only applies to Multi-Company Access. 
            // Standard ThrowIfNoCompanyAccess() checks will be executed for single company access.
            if (sharedCompanies == null || !sharedCompanies.Any())
                return false;

            var parentCompanyId = sharedCompanies.FirstOrDefault().CompanyId;

            // The parent company must match the users Primary Company Id
            if (parentCompanyId == userPrimaryCompanyId)
            {
                // Verify all companies in the model
                if (userModelCompanies != null)
                {
                    var allExist = userModelCompanies.All(c => sharedCompanies.Any(sc => sc.SharedCompanyId == c));
                    if (!allExist)
                    {
                        return false;
                    }
                }

                // Verify the new companies the user is accessing exist in sharedCompanies
                var modelPrimaryFound = sharedCompanies.Any(sc => sc.CompanyId == userModelPrimaryCompanyId || sc.SharedCompanyId == userModelPrimaryCompanyId);
                return (modelPrimaryFound);
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// If accessed outside of the controller must use UsersController.InternalUserModel to 
        /// avoid any naming collisions
        /// </summary>
        public sealed class InternalUserModel
        {
            public int Id { get; set; }
            public string FullName { get; set; }
            public string Username { get; set; }
            public User.TypeEnum Type { get; set; }
            public int CompanyId { get; set; }
            public DateTime LastLogin { get; set; }
            public DateTime? LastLoginIOS { get; set; }
            public DateTime? LastLoginAndroid { get; set; }
            public bool Disabled { get; set; }
            public bool Deleted { get; set; }
            public string Email { get; set; }
            public string ProfilePhotoUrl { get; set; }
            public string ReferenceNumber { get; set; }

        }
    }

    public sealed class ConfigUserModel
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public string Name { get; set; }
        public User.TypeEnum Type { get; set; }
        public bool Disabled { get; set; }
        public bool? Deleted { get; set; }
        public string MobilePhone { get; set; }
        public string ProfilePhotoUrl { get; set; }

        public string ReferenceNumber { get; set; }

        public ConfigUserModel() { }
        [Obsolete("Use the constructor with MobilePhone parameter.")]
        public ConfigUserModel(int id, int companyId, string name, User.TypeEnum type, bool disabled) : this(id, companyId, name, type, disabled, null) { }
        public ConfigUserModel(int id, int companyId, string name, User.TypeEnum type, bool disabled, string mobilePhone)
        {
            Id = id;
            CompanyId = companyId;
            Name = name;
            Type = type;
            Disabled = disabled;
            MobilePhone = mobilePhone;
        }
    }
}
