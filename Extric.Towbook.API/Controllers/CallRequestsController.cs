using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Agero;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Aaa.ACG;
using Extric.Towbook.Integrations.MotorClubs.Agero;
using Extric.Towbook.Integrations.MotorClubs.Allstate;
using Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy;
using Extric.Towbook.Integrations.MotorClubs.Fleetnet;
using Extric.Towbook.Integrations.MotorClubs.Gerber;
using Extric.Towbook.Integrations.MotorClubs.Honk;
using Extric.Towbook.Integrations.MotorClubs.RoadsideProtect;
using Extric.Towbook.Integrations.MotorClubs.StackThree;
using Extric.Towbook.Integrations.MotorClubs.Sykes;
using Extric.Towbook.Integrations.MotorClubs.Trx;
using Extric.Towbook.Integrations.MotorClubs.Urgently;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;
using OonAgeroClient.Data;
using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Controllers
{
    /// <summary>
    /// Exposes REST methods for accepting, rejecting and other related actions for handling call requests from third parties (motor clubs, police departments, car dealerships)
    /// </summary>
    [Route("callrequests")]
    public class CallRequestsController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private static readonly Dictionary<string, string> aliases;

        static CallRequestsController()
        {
            aliases = new Dictionary<string, string>
            {
                { "jobinfo", "Job Information" },
                { "damagedescription", "Description" },
                { "maxeta", "Maximum ETA" },
                { "cause", "Reason" },
                { "lic", "License Plate" },
                { "vehicletype", "Type" },
                { "additionalinfo", "Additional Info" },
                { "addr1", "Address" },
                { "safeloc", "Safe Location" },
                { "driverloc", "Driver Location" },
                { "custfirstname", "Customer First Name" },
                { "custlastname", "Customer Last Name" },
                { "memfirstname", "Member First Name" },
                { "memlastname", "Member Last Name" },
                { "callfromloc", "Called From" },

                { "isdriverwithvehicle", "Driver with Vehicle?" }, // Agero
                { "nightdropoff", "Night Drop Off?" }, // Agero
                { "locationtype", "Location Type" }, // Agero
                { "crossstreet", "Cross Street" }, // Agero 

                { "isgaraged", "Is Garaged" }, // Allstate
                { "neutralcapable", "Neutral Capable?" }, // Allstate
                { "estunloadedmiles", "Estimated Unloaded Miles" }, // Allstate
                { "estloadedmiles", "Estimated Loaded Miles" }, // Allstate
                { "servicedetails", "Service Details" }, // Allstate
                { "eventdetails", "Event Details" }, // Allstate
                { "equipmenttype", "Equipment Type" }, // Allstate
                { "timestamp", "Timestamp" }, // Allstate
                { "callername", "Caller Name" }, // Allstate
                { "programname", "Program Name" }, // Allstate
                { "crsstr1", "Cross Street" }, // Allstate

                { "destaddr", "Destination Location" },
                { "towdestination", "Destination Location" },
                { "disablementlocation", "Incident Location" },
                { "incaddr", "Incident Location" },
                { "paymentinfo", "Payment Information" },
                { "passengerinfo", "Passenger Information" },
                { "vehicleinfo", "Vehicle Information" },
                { "ratecard", "Rate Card" },
                { "businessname", "Business Name" },
                { "businessphone", "Business Phone #" },
                { "businesscontactname", "Business Contact Name" },
                { "notestoprovider", "Notes to Provider" },
                { "recreatnlvehcltype", "Generic Vehicle Type" },
                { "vehclkeyloctype", "Key Location Type" },
                { "allwheelsopertnlind", "All Wheels Operational" },

                { "carryngloadind", "Is Carrying Load" },
                { "engnfueltype", "Engine Fuel Type" },
                { "upfitind", "Upfit Indicator" },
                { "pullingtrailrind", "Pulling Trailer" },
                { "parkinggarageflrnbr", "Parking Garage Floor Number" },
                { "parkinggarageclearncin", "Parking Garage Clearance (inches)" },
                { "servicelocation", "Service Location" },
                { "licenseplate", "License Plate" },
                { "vehicleheightnbr", "Vehicle Height (ft)" },
                { "vehiclelengthnbr", "Vehicle Length (ft)" },
                { "payloaddesc", "Payload Description" },
                { "vehiclenoseinind", "Is Nose In" },
                { "scheduledlocal", "Scheduled Date/Time" },
                { "jobnotes", "Job Notes" },
                { "partnername", "Partner Name" },
                { "zipcode", "Zip" },
                { "vin", "VIN" },
                { "grossweightnumber", "Gross Weight Number" }
            };
        }

        [HttpPost]
        [Route("")]
        public async Task<CallRequest> Post(CallRequest model)
        {
            model.CompanyId = WebGlobal.CurrentUser.CompanyId;
            await model.SaveAsync();

            return model;
        }

        /// <summary>
        /// Retrieves a list of currently active call requests that haven't been responded to or expired yet. 
        /// </summary>
        /// <returns>Array of CallRequests</returns>
        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<Models.CallRequestModel>> Get()
        {
            var currentUser = WebGlobal.CurrentUser;
            if (!await currentUser.ShouldReceiveCallRequestsAsync())
                return Array.Empty<Models.CallRequestModel>();
            
            var companies = await WebGlobal.GetCompaniesAsync();

            if (Request?.Headers != null &&
                Request.Headers["X-Company"].Count > 0)
            {
                companies = await this.GetCompaniesForRequestAsync();
            }

            if (currentUser.CompanyId == 4817 ||
                currentUser.CompanyId == 5406)
            {
                return await Task.WhenAll((await CallRequest.GetCurrentByCompanyId(currentUser.CompanyId)).Select(CallRequestModel.MapAsync));
            }

            if (await CustomCompanyIdsPerUserAsync(currentUser))
            {
                var companyIdsJson = (await Towbook.Integration.UserKeyValue.GetByUserAsync(currentUser.CompanyId,
                    currentUser.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId,
                    "CallRequests_CompanyIds"))
                    .FirstOrDefault()?.Value;

                if (companyIdsJson != null)
                {
                    try
                    {
                        var companyIds = JsonConvert.DeserializeObject<int[]>(companyIdsJson);
                        var allCompanies = await Company.Company.GetByIdsAsync(companyIds);
                        companies = (await Task.WhenAll(allCompanies.Select(async c => await currentUser.HasAccessToCompanyAsync(c.Id) ? c : null)))
                            .Where(c => c != null)
                            .ToArray();


                    }
                    catch
                    {

                    }
                }
            }


            if (companies.Length > 1)
            {
                var ret = new List<Models.CallRequestModel>();

                foreach (var c in companies)
                    ret.AddRange(await Task.WhenAll((await CallRequest.GetCurrentByCompanyId(c.Id)).Select(CallRequestModel.MapAsync)));

                return ret;
            }
            else
            {
                return await Task.WhenAll((await CallRequest.GetCurrentByCompanyId(currentUser.CompanyId)).Select(CallRequestModel.MapAsync));
            }
        }

        private async Task<bool> CustomCompanyIdsPerUserAsync(User currentUser)
        {
            return await Extric.Towbook.Integration.CompanyKeyValue.GetFirstValueOrNullAsync(currentUser.PrimaryCompanyId,
                Extric.Towbook.Integration.Provider.Towbook.ProviderId, "CallRequests_CustomCompanyIdsDelivery") == "1";
        }

        [HttpGet("missed")]
        public async Task<IEnumerable<CallRequest>> Missed()
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser || WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                return Array.Empty<CallRequest>();

            var calls = CallRequest.GetMissedByCompanyId(WebGlobal.CurrentUser.CompanyId);
            var mappedCalls = await Task.WhenAll(calls.Select(o => MapAsync(o)));
            return mappedCalls;
        }

        [HttpGet]
        [Route("{id}")]
        public async Task<object> Get(int id)
        {
            var currentUser = WebGlobal.CurrentUser;
            var q = Web.HttpContext.Current.Request.Query;

            // 96242, 101960 special setup
            int[] companyIdsToAllow = new int[] { 
                // splendora
                10000, 536, 27875, 17898, 17950, 55201, 96741, 7625, 97322, 85331, 10909, 20420,10037,
                
                // willis
                101968,99234, 18542, 48824, 101966, 7625, 14910, 863340,

                // patton village (123861)
                85331, 7076, 55201, 17950, 10909, 20420, 108898
            };

            var allowDriver = companyIdsToAllow.Contains(currentUser.CompanyId) && currentUser.Type == Towbook.User.TypeEnum.Driver;

            if (!await currentUser.ShouldReceiveCallRequestsAsync() && !allowDriver)
            {
                if (q["rawDetails"] == "1" || q["details"] == "1")
                {
                    if (currentUser.Type != Towbook.User.TypeEnum.Manager)
                        return null;
                }
                else
                {
                    // TODO: make it so we can check if a user can receive digitals for a specific ID. 
                    return null;
                }
            }

            var cr = await CallRequest.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(cr?.CompanyId, "digital dispatch request");

            var darkMode = q["darkMode"] == "1";

            if (q["rawDetails"] == "1" || q["details"] == "1")
            {
                var ac = await Account.GetByIdAsync(cr.AccountId);

                cr.MasterAccountId = ac.MasterAccountId;
                cr.AccountName = ac.Company;

                if (allowDriver)
                {
                    // TODO: add a way to make specific accounts callrequests go to drivers. 
                    if (cr.AccountName != null && !cr.AccountName.ToLowerInvariant().Contains("splendora"))
                    {
                        return null;
                    }
                }

                if (ac.MasterAccountId == MasterAccountTypes.Geico ||
                    ac.MasterAccountId == MasterAccountTypes.RoadsideProtect ||
                    ac.MasterAccountId == MasterAccountTypes.Tesla)
                {
                    if (ac.MasterAccountId == MasterAccountTypes.RoadsideProtect &&
                        RoadsideProtectContractor.GetByAccountId(ac.Id) != null)
                    {
                        if (q["rawDetails"] == "1")
                        {
                            var html = new StringBuilder();
                            html.AppendLine(getStyle(darkMode));

                            var dsp = RoadsideProtectDispatch.GetByCallRequestId(id);
                           
                            if (string.IsNullOrEmpty(dsp?.DispatchJson))
                            {
                                return new HttpResponseMessage()
                                {
                                    Content = new StringContent("No details found.")
                                };
                            }

                            var rsd = dsp.ModelFromDispatchJson();

                            string payLine = rsd.Job.Pricing.ContractedAmount.ToString("C");

                            if (rsd.Job.Pricing.CustomerPay != 0)
                            {
                                var total = rsd.Job.Pricing.ContractedAmount + rsd.Job.Pricing.CustomerPay;
                                payLine = total.ToString("C") + " (Roadside Protect Pays: " + rsd.Job.Pricing.ContractedAmount.ToString("C") +
                                    " | Customer Pays " + rsd.Job.Pricing.CustomerPay.ToString("C") + ")";
                            }

                            html.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\">" +
                                "<strong style=\"color: #ffffff; font-size: 16px\">This Job Pays: " +
                                payLine + "</strong> | GOA Price: " + rsd.Job.Pricing.Goa.ToString("C") + "<br />");

                            return new HttpResponseMessage()
                            {
                                Content = new StringContent(html.ToString())
                            };
                        }
                        else
                        {
                            var ic = RoadsideProtectDispatch.GetByCallRequestId(id);

                            if (string.IsNullOrEmpty(ic?.DispatchJson))
                            {
                                return new HttpResponseMessage()
                                {
                                    Content = new StringContent("No details found.")
                                };
                            }

                            return new
                            {
                                callRequest = cr,
                                MasterAccountId = ac.MasterAccountId,
                                StatusName = cr.Status.ToString().FromPascalCase(),
                                providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                                contractorId = RoadsideProtectContractor.GetById(ic.RoadsideProtectContractorId).ContractorId,
                                details = ic.DispatchJson.FromJson()
                            };
                        }
                    }

                    if (q["rawDetails"] == "1")
                    {
                        return new HttpResponseMessage()
                        {
                            Content = new StringContent(
                                IsscToRawHtml(Integrations.MotorClubs.Issc.IsscDispatch.GetByCallRequestId(id).CallJson, cr, darkMode))
                        };
                    }
                    else
                    {
                        var ic = Integrations.MotorClubs.Issc.IsscDispatch.GetByCallRequestId(id);
                        return new
                        {
                            callRequest = cr,
                            MasterAccountId = ac.MasterAccountId,
                            StatusName = cr.Status.ToString().FromPascalCase(),
                            providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                            contractorId = ic.ContractorId,
                            details = ic.CallJson.FromJson()
                        };
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.AaaAce ||
                    ac.MasterAccountId == MasterAccountTypes.AaaNortheast)
                {
                    var sd = await Integrations.MotorClubs.Aaa.AaaDispatch.GetByCallRequestIdAsync(cr.CallRequestId);
                    var sj = JsonConvert.DeserializeObject<Integrations.MotorClubs.Aaa.Ace.DispatchModel>(sd.DispatchJson);

                    var sr = new StringBuilder();
                    sr.AppendLine(getStyle(darkMode));

                    if (sj.Call.ServiceStatus.GetAppointmentTime() != null)
                    {
                        var local = Core.OffsetDateTime(currentUser.Company, sj.Call.ServiceStatus.GetAppointmentTime()).Value;
                        sr.AppendLine(
                            $"<strong>This job is scheduled for: {local.ToShortDateString()} at {local.ToShortTowbookTimeString()}</strong><br /><br />");
                    }

                    if (sj.Call.Payment?.Required == true)
                        sr.AppendLine($"<strong>COD / Payment Required from Customer</strong><br />");

                    sr.AppendLine($"<strong>Call Type:</strong> {WebUtility.HtmlEncode(sj.Call.CallType)}<br />");
                    sr.AppendLine($"<strong>Trouble Codes:</strong> {string.Join(", ", sj.Call.RequestedService.TroubleCodes.Select(o => o.Code + " - " + o.Description))}<br />");

                    if (sj.Call.RequestedService.Priority != null &&
                        sj.Call.RequestedService.Priority.StartsWith("P"))
                    {
                        sr.AppendLine($"<strong>Priority Code:</strong> {WebUtility.HtmlEncode(sj.Call.RequestedService.Priority)}<br />");
                    }

                    if (!string.IsNullOrWhiteSpace(sj.DriverNotes))
                    {
                        sr.AppendLine("<strong>Driver Notes</strong><br />");
                        sr.AppendLine($"- {HttpUtility.HtmlEncode(sj.DriverNotes)}<br />");
                    }

                    if (!string.IsNullOrWhiteSpace(sj.DrivingDirection))
                    {
                        sr.AppendLine("<strong>Driving Directions</strong><br />");
                        sr.AppendLine($"- {HttpUtility.HtmlEncode(sj.DrivingDirection)}<br />");
                    }

                    if (sj.Call.Comments != null && sj.Call.Comments.Any())
                    {
                        sr.AppendLine("<strong>Comments</strong><br />");

                        foreach (var note in sj.Call.Comments.OrderBy(o => o.CommentDateTime))
                        {
                            if (note.Text == null ||
                                note.Text.StartsWith("|") ||
                                note.Text.Contains("Page/Grid") ||
                                note.Text.StartsWith("**PACESETTER SERVICE"))
                                continue;

                            sr.AppendLine($"- {WebUtility.HtmlEncode(note.Text)}<br />");
                        }
                    }

                    return new HttpResponseMessage()
                    {
                        Content = new StringContent(
                            sr.ToString()
                        )
                    };
                }
                else if (ac.MasterAccountId == MasterAccountTypes.AaaAcg ||
                    ac.MasterAccountId == MasterAccountTypes.AaaNationalFsl)
                {
                    var sd = await Integrations.MotorClubs.Aaa.AaaDispatch.GetByCallRequestIdAsync(cr.CallRequestId);
                    var sj = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(sd.DispatchJson).Payload;

                    var sr = new StringBuilder();
                    sr.AppendLine(getStyle(darkMode));

                    if (sj.IsScheduledAppointment && sj.StartDateTime != null)
                    {
                        var local = Core.OffsetDateTime(currentUser.Company, sj.StartDateTime.Value.ToLocalTime());

                        sr.AppendLine(
                            $"<strong>This job is scheduled for: {local.ToShortDateString()} at {local.ToShortTowbookTimeString()}</strong><br /><br />");
                    }

                    sr.AppendLine($"<strong>Call Type:</strong> {HttpUtility.HtmlEncode(sj.CallType)}<br />");

                    if (sj.Priority != null &&
                        sj.Priority.StartsWith("P"))
                    {
                        sr.AppendLine($"<strong>Priority Code:</strong> {HttpUtility.HtmlEncode(sj.Priority)}<br />");
                    }

                    if (!string.IsNullOrWhiteSpace(sj.Notes))
                    {
                        sr.AppendLine("<strong>Notes</strong><br />");

                        sr.AppendLine($"- {HttpUtility.HtmlEncode(sj.Notes)}<br />");

                    }

                    return new HttpResponseMessage()
                    {
                        Content = new StringContent(
                            sr.ToString()
                        )
                    };
                }
                else if (ac.MasterAccountId == MasterAccountTypes.AaaWashington ||
                    ac.MasterAccountId == MasterAccountTypes.AaaNewYork)
                {
                    var sd = await Integrations.MotorClubs.Aaa.AaaDispatch.GetByCallRequestIdAsync(cr.CallRequestId);
                    var sj = JsonConvert.DeserializeObject<SalesforceData<Integrations.MotorClubs.Aaa.WaNy.WorkOrderModel>>(sd.DispatchJson).Payload;

                    var sr = new StringBuilder();
                    sr.AppendLine(getStyle(darkMode));


                    sr.AppendLine($"<strong>Work Order Number:</strong> {HttpUtility.HtmlEncode(sj.WorkOrderNumber)}<br />");


                    if (!string.IsNullOrWhiteSpace(sj.workOrder_Type__c))
                    {
                        sr.AppendLine($"<strong>Work Order Type:</strong> {HttpUtility.HtmlEncode(sj.workOrder_Type__c)}<br />");
                    }

                    if (sj.Priority != null &&
                        sj.Priority.StartsWith("P"))
                    {
                        sr.AppendLine($"<strong>Priority Code:</strong> {HttpUtility.HtmlEncode(sj.Priority)}<br />");
                    }
                    if (!string.IsNullOrWhiteSpace(sj.erS_Priority_Description__c))
                    {
                        sr.AppendLine($"<strong>Priority Description:</strong> {HttpUtility.HtmlEncode(sj.erS_Priority_Description__c)}<br />");
                    }

                    if (!string.IsNullOrWhiteSpace(sj.DispatchCode))
                    {
                        var friendly = (await MasterAccountReason.GetByMasterAccountIdAsync(ac.MasterAccountId))
                            .FirstOrDefault(o => o.Type == MasterAccountReasonType.DispatchCodes && o.Code == sj.DispatchCode);

                        sr.AppendLine($"<strong>Dispatch Code: </strong> {HttpUtility.HtmlEncode(sj.DispatchCode)} - {HttpUtility.HtmlEncode(friendly.Name)}<br />");
                    }

                    if (!string.IsNullOrWhiteSpace(sj.Notes))
                    {
                        sr.AppendLine("<strong>Notes</strong><br />");
                        sr.AppendLine($"- {HttpUtility.HtmlEncode(sj.Notes)}<br />");
                    }

                    return new HttpResponseMessage()
                    {
                        Content = new StringContent(
                            sr.ToString()
                        )
                    };
                }

                else if (ac.MasterAccountId == MasterAccountTypes.Swoop)
                {
                    var sd = await Integrations.MotorClubs.Swoop.SwoopDispatch.GetByCallRequestIdAsync(cr.CallRequestId);
                    var sj = JsonConvert.DeserializeObject<Integrations.MotorClubs.Swoop.Model.SwoopJob>(sd.OfferJson);

                    var sr = new StringBuilder();
                    sr.AppendLine(getStyle(darkMode));

                    if (sj.Service.ScheduledFor != null)
                    {
                        var local = Core.OffsetDateTime(currentUser.Company, sj.Service.ScheduledFor.Value.ToLocalTime());
                        sr.AppendLine(
                            $"<strong>This job is scheduled for: {local.ToShortDateString()} at {local.ToShortTowbookTimeString()}</strong><br />");
                    }

                    if (!string.IsNullOrWhiteSpace(sj.Vehicle?.Equipment))
                        sr.AppendLine($"<strong>Equipment:</strong> " + sj.Vehicle.Equipment + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Location?.ServiceLocation?.LocationType))
                        sr.AppendLine($"<strong>Breakdown Location Type:</strong> {WebUtility.HtmlEncode(sj.Location.ServiceLocation.LocationType)}<br />");

                    if (!string.IsNullOrWhiteSpace(sj.VehicleReleasePacket?.StorageYardName))
                        sr.AppendLine($"<strong>Storage Yard Name:</strong> " +
                            HttpUtility.HtmlEncode(sj.VehicleReleasePacket.StorageYardName) + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.VehicleReleasePacket?.PaymentTotal))
                        sr.AppendLine($"<strong>Release Fees:</strong> " +
                            HttpUtility.HtmlEncode(sj.VehicleReleasePacket.PaymentTotal) + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.VehicleReleasePacket?.PaymentType))
                        sr.AppendLine($"<strong>Release Payment Type:</strong> " +
                            HttpUtility.HtmlEncode(sj.VehicleReleasePacket.PaymentType) + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Location?.DropoffLocation?.LocationType))
                        sr.AppendLine($"<strong>Destination Location Type:</strong> {WebUtility.HtmlEncode(sj.Location.DropoffLocation.LocationType)}<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Notes?.Customer))
                        sr.AppendLine($"<strong>Customer Notes:</strong> {System.Net.WebUtility.HtmlEncode(sj.Notes.Customer)}<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Notes?.Internal))
                        sr.AppendLine($"<strong>Internal Notes:</strong> {System.Net.WebUtility.HtmlEncode(sj.Notes.Internal)}<br />");

                    if (sj.CoverageNotes != null && sj.CoverageNotes.Any())
                        sr.AppendLine($"<strong>Coverage Notes:</strong> " +
                            $"{string.Join("<br />", sj.CoverageNotes.Select(xr => WebUtility.HtmlEncode(xr)))}<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Service?.Symptom))
                        sr.AppendLine($"<strong>Symptom:</strong> " + sj.Service.Symptom + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Vehicle?.VehicleType))
                        sr.AppendLine($"<strong>Vehicle Type:</strong> " + sj.Vehicle.VehicleType + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Vehicle?.Drivetrain))
                        sr.AppendLine($"<strong>Drivetrain:</strong> " + sj.Vehicle.Drivetrain + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Vehicle?.StrandedVehicleClassType))
                        sr.AppendLine($"<strong>Class Type:</strong> " + sj.Vehicle.StrandedVehicleClassType + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Vehicle?.LengthRange))
                        sr.AppendLine($"<strong>Total Length:</strong> " + sj.Vehicle.LengthRange + "<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Vehicle?.WeightRange))
                        sr.AppendLine($"<strong>Total Weight:</strong> " + sj.Vehicle.WeightRange + "<br />");

                    if (sj.Partner?.RateAgreement?.AnchorLocation != null)
                        sr.AppendLine($"<strong>Rate Agreement: </strong> " +
                            sj.Partner?.RateAgreement.ToString() + "<br />");

                    return new HttpResponseMessage()
                    {
                        Content = new StringContent(
                            sr.ToString()
                        )
                    };

                }
                else if (ac.MasterAccountId == MasterAccountTypes.Agero)
                {
                    var arc = new AgeroRestClient();
                    var sess = AgeroSession.GetByAccountId(cr.AccountId);
                    var acceptDispatch = await AgeroDispatch.GetByCallRequestIdAsync(cr.CallRequestId);

                    var detailedDispatch = arc.GetDispatchDetail(sess.AccessToken, acceptDispatch.DispatchId);

                    if (q["rawDetails"] == "1")
                    {
                        return new HttpResponseMessage()
                        {
                            Content = new StringContent(
                                       await ddxmlToRawHtmlAsync(detailedDispatch.ToJson(null), cr, darkMode: darkMode))
                        };
                    }
                    else
                    {

                        if (string.IsNullOrWhiteSpace(detailedDispatch.PurchaseOrderNumber))
                        {
                            detailedDispatch.DisablementLocation.ContactInfo = new Agero.Types.DispatchContactInfo();
                            detailedDispatch.Vehicle.VIN = "";
                        }

                        if (cr.Status != CallRequestStatus.Accepted)
                        {
                            if (detailedDispatch.DisablementLocation.ContactInfo != null)
                            {
                                detailedDispatch.DisablementLocation.ContactInfo.Name = "Available after the call is Accepted";
                                detailedDispatch.DisablementLocation.ContactInfo.Phone = "Available after the call is Accepted";
                            }
                        }

                        return new
                        {
                            callRequest = cr,
                            MasterAccountId = ac.MasterAccountId,
                            StatusName = cr.Status.ToString().FromPascalCase(),
                            providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                            contractorId = acceptDispatch.VendorId,
                            details = detailedDispatch
                        };
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.OonAgero)
                {
                    var oonAd = OonAgeroDispatch.GetByCallRequestId(cr.CallRequestId);
                    var oj = JsonConvert.DeserializeObject<OonAgeroClient.OonDispatch>(oonAd.DispatchJson);

                    if (cr.Status != CallRequestStatus.Accepted)
                    {
                        oj.Vehicle.Vin = "";
                        oj.DisablementLocation.ContactInfo = new OonAgeroClient.DisablementLocation.DisablementLocationContactInfo();

                        oj.DisablementLocation.ContactInfo.Name = "Available after the call is Accepted";
                        oj.DisablementLocation.ContactInfo.Phone = "Available after the call is Accepted";
                    }

                    if (q["rawDetails"] == "1")
                    {
                        return new HttpResponseMessage()
                        {
                            Content = new StringContent(
                                       await ddxmlToRawHtmlAsync(oj.ToJson(null), cr, darkMode: darkMode))
                        };
                    }
                    else
                    {
                        return new
                        {
                            callRequest = cr,
                            masterAccountId = ac.MasterAccountId,
                            statusName = cr.Status.ToString().FromPascalCase(),
                            providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                            contractorId = await Integrations.Email.EmailAddress.GetPrimaryTowbookEmailAddressAsync(ac.CompanyId),
                            details = oj
                        };
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.OonSwoop)
                {
                    var sd = await Integrations.MotorClubs.Swoop.SwoopDispatch.GetByCallRequestIdAsync(cr.CallRequestId);
                    var sj = JsonConvert.DeserializeObject<Integrations.MotorClubs.Swoop.Model.SwoopJob>(sd.OfferJson);

                    var sr = new StringBuilder();
                    sr.AppendLine(getStyle(darkMode));

                    sr.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                        (sj.OonProviderAmount.OonFullAmount.Value).ToString("C") + ".</strong> | GOA Price: " + (sj.OonProviderAmount.OonGoaAmount.Value).ToString("C") + "" +
                        "<br />Credit Card is issued automatically upon completion of the job in Towbook.</div>");

                    if (Web.HttpContext.Current.IsAppleDevice())
                    {
                        sr.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to perform the service for the offered price and to the Agero (Swoop) Out of Network Terms and Conditions displayed below. If you do not agree, refuse this dispatch. Additional info regarding this dispatch is listed below the terms and conditions.</strong>");
                        var html = System.IO.File.ReadAllText(Path.Combine(Web.HttpContext.Env.ContentRootPath, "~\\agero.htm"), Encoding.UTF8);
                        sr.Append(html);
                    }
                    else
                    {
                        sr.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to to perform the service for the offered price and the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-agero-towbook.htm\">Agero Out of Network Terms and Conditions (click to view)</a>, If you do not agree, refuse this dispatch.</strong>");
                    }

                    var jsonDm = await Core.GetRedisValueAsync("cr_loc:" + cr.CallRequestId);

                    if (jsonDm != null)
                    {
                        var dm = JsonConvert.DeserializeObject<DistanceModel>(jsonDm);

                        if (dm.ClosestDriver != dm.CompanyDistance &&
                            dm.ClosestDriver < dm.CompanyDistance)
                            sr.AppendLine("<li>Distance from your nearest driver: <span style=\"font-weight:bold\">" + dm.ClosestDriver + " miles (" +
                                dm.CompanyDistance + " miles from your office)</span></li>");
                        else
                            sr.AppendLine("<li>Distance from your office: <span style=\"font-weight:bold\">" + dm.CompanyDistance + " miles</span></li>");

                        if (dm.LoadedMileage > 0)
                            sr.AppendLine("<li>Loaded Distance: <span style=\"font-weight:bold\">" + dm.LoadedMileage + " miles</span></li>");
                    }

                    if (!string.IsNullOrWhiteSpace(sj.Location?.ServiceLocation?.LocationType))
                        sr.AppendLine($"<strong>Breakdown Location Type:</strong> {WebUtility.HtmlEncode(sj.Location.ServiceLocation.LocationType)}<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Location?.DropoffLocation?.LocationType))
                        sr.AppendLine($"<strong>Destination Location Type:</strong> {WebUtility.HtmlEncode(sj.Location.DropoffLocation.LocationType)}<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Notes?.Customer))
                        sr.AppendLine($"<strong>Customer Notes:</strong> {WebUtility.HtmlEncode(sj.Notes.Customer)}<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Notes?.Internal))
                        sr.AppendLine($"<strong>Internal Notes:</strong> {WebUtility.HtmlEncode(sj.Notes.Internal)}<br />");

                    if (!string.IsNullOrWhiteSpace(sj.Notes?.Provider))
                        sr.AppendLine($"<strong>Provider Notes:</strong> {WebUtility.HtmlEncode(sj.Notes.Provider)}<br />");

                    if (sj.CoverageNotes != null && sj.CoverageNotes.Any())
                        sr.AppendLine($"<strong>Coverage Notes:</strong> " +
                            $"{string.Join("<br />", sj.CoverageNotes.Select(xr => WebUtility.HtmlEncode(xr)))}<br />");

                    return new HttpResponseMessage()
                    {
                        Content = new StringContent(
                            sr.ToString()
                        )
                    };
                }
                else if (ac.MasterAccountId == MasterAccountTypes.OonRoadsideProtect)
                {
                    HttpResponseMessage getOonRpHtmlResponse()
                    {
                        var dispatch = RoadsideProtectDispatch.GetByCallRequestId(cr.CallRequestId);
                        var model = JsonConvert.DeserializeObject<Integrations.MotorClubs.RoadsideProtect.DispatchModel>(
                            dispatch.DispatchJson);
                        var sb = new StringBuilder();

                        string payLine = model.Oon.OfferPrice.ToString("C");

                        if (model.Oon.CustomerPay != 0)
                        {
                            var total = model.Oon.CustomerPay + model.Oon.OfferPrice;
                            payLine = total.ToString("C") + " (Roadside Protect Pays: " + model.Oon.OfferPrice.ToString("C") +
                               " | Customer Pays " + model.Oon.CustomerPay.ToString("C") + ")";
                        }

                        if (model != null && model.Oon != null)
                        {
                            sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                            payLine + ".</strong> | GOA Price: " + (model.Oon.GoaPrice).ToString("C") + "" +
                                "<br />Credit Card is issued automatically upon completion of the job in Towbook.</div>");
                        }

                        sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-roadsideprotect-towbook.htm\">Roadside Protect Out of Network Terms and Conditions (click to view)</a>, If you do not agree, refuse this dispatch.</strong>");

                        return new HttpResponseMessage()
                        {
                            Content = new StringContent(sb.ToString())
                        };
                    }

                    return getOonRpHtmlResponse();
                }
                else if (ac.MasterAccountId == MasterAccountTypes.OonDrivenSolutions)
                {
                    HttpResponseMessage getOonDrivenHtmlResponse()
                    {
                        var dispatch = StackThreeDispatch.GetByCallRequestId(cr.CallRequestId);
                        var model = JsonConvert.DeserializeObject<Towbook.Integration.MotorClubs.StackThree.Model.DispatchModel>(
                            dispatch.DispatchJson).ServiceRequest;
                        var sb = new StringBuilder();

                        if (model != null && model.Oon != null)
                        {
                            sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                            (model.Oon.OfferPrice).ToString("C") + ".</strong> | GOA Price: " + (model.Oon.GoaPrice).ToString("C") + "" +
                                "<br />Credit Card is issued automatically upon completion of the job in Towbook.</div>");
                        }


                        if (Web.HttpContext.Current.IsAppleDevice())
                        {
                            sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the Driven Solutions Out of Network Terms and Conditions displayed below. If you do not agree, refuse this dispatch. Additional info regarding this dispatch is listed below the terms and conditions.</strong>");
                            var html = System.IO.File.ReadAllText(Path.Combine(Web.HttpContext.Env.ContentRootPath, "~\\oon-drivensolutions-towbook.htm"), Encoding.UTF8);
                            sb.Append(html);
                        }
                        else
                        {
                            sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-drivensolutions-towbook.htm\">Driven Solutions Out of Network Terms and Conditions (click to view)</a>, If you do not agree, refuse this dispatch.</strong>");
                        }

                        return new HttpResponseMessage()
                        {
                            Content = new StringContent(sb.ToString())
                        };
                    }

                    return getOonDrivenHtmlResponse();
                }
                else if (ac.MasterAccountId == MasterAccountTypes.OonHonk)
                {
                    string getHtml()
                    {
                        var hjo = HonkJob.GetByCallRequestId(cr.CallRequestId);
                        var model = JsonConvert.DeserializeObject<HonkRestClient.JobModelContainer>(JsonConvert.DeserializeObject<HonkMessage>(hjo.OfferJson).JsonData)?.Job;
                        var sb = new StringBuilder();
                        if (model != null)
                        {
                            sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                            ((decimal)model.estimated_payout_cents.Value / 100).ToString("C") + ".</strong> | GOA Price: " + ((decimal)model.goa_cents / 100).ToString("C") + "" +
                                "<br />Credit Card is issued automatically upon completion of the job in Towbook.</div>");
                        }

                        sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://www.joinhonk.com/partner_agreement_short\">Honk Out of Network Terms and Conditions (click to view)</a>, If you do not agree, refuse this dispatch.</strong>");

                        return sb.ToString();
                    }

                    return new HttpResponseMessage()
                    {
                        Content = new StringContent(getHtml())
                    };
                }
                else if (ac.MasterAccountId == MasterAccountTypes.OonTrx)
                {
                    string getHtml()
                    {
                        var hjo = TrxDispatch.GetByCallRequestId(cr.CallRequestId);
                        var model = JsonConvert.DeserializeObject<Extric.Towbook.Integrations.MotorClubs.Trx.DispatchModel>(hjo.DispatchJson);
                        var sb = new StringBuilder();
                        if (model != null)
                        {
                            sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                            (model.Job.Oon.OfferPrice).ToString("C") + ".</strong> | GOA Price: " + (model.Job.Oon.GoaPrice).ToString("C") + "" +
                                "<br />Credit Card is issued automatically upon completion of the job in Towbook.</div>");
                        }

                        sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-trx.htm\">TRX Out of Network Terms and Conditions (click to view)</a>, If you do not agree, refuse this dispatch.</strong>");

                        return sb.ToString();
                    }

                    return new HttpResponseMessage()
                    {
                        Content = new StringContent(getHtml())
                    };
                }
                else if (ac.MasterAccountId == MasterAccountTypes.AlliedDispatch)
                {
                    return new HttpResponseMessage()
                    {
                        Content = new StringContent("")
                    };
                }
                else if (
                    ac.MasterAccountId == MasterAccountTypes.Allstate ||
                    ac.MasterAccountId == MasterAccountTypes.OonAllstate ||
                    ac.MasterAccountId == MasterAccountTypes.Quest ||
                    ac.MasterAccountId == MasterAccountTypes.OonQuest ||
                    ac.MasterAccountId == MasterAccountTypes.Pinnacle ||
                    ac.MasterAccountId == MasterAccountTypes.Nac ||
                    ac.MasterAccountId == MasterAccountTypes.Nsd)
                {
                    var asd = await AllstateDispatch.GetByCallRequestIdAsync(id);
                    if (asd != null)
                    {
                        var asDispatchDetails = (DSPMessageBody)DDMessage.FromXml(asd.CallXml, typeof(DSPMessageBody)).DDContent;

                        if (q["rawDetails"] == "1")
                        {
                            return new HttpResponseMessage()
                            {
                                Content = new StringContent(
                                    await ddxmlToRawHtmlAsync(asDispatchDetails.ToJson(null), cr, asd, darkMode))
                            };
                        }
                        else
                        {
                            return new
                            {
                                callRequest = cr,
                                MasterAccountId = ac.MasterAccountId,
                                StatusName = cr.Status.ToString().FromPascalCase(),
                                providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                                contractorId = asd.ContractorId,
                                details = asDispatchDetails
                            };
                        }
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.Urgently ||
                    ac.MasterAccountId == MasterAccountTypes.OonUrgently)
                {
                    var jobOffer = await UrgentlyJobOffer.GetByCallRequestIdAsync(id);
                    if (q["rawDetails"] == "1")
                    {
                        if (jobOffer != null)
                            return new HttpResponseMessage()
                            {
                                Content = new StringContent(
                                    await ddxmlToRawHtmlAsync(jobOffer.OfferJson, cr, null, darkMode: darkMode))
                            };
                    }
                    else
                    {
                        return new
                        {
                            callRequest = cr,
                            MasterAccountId = ac.MasterAccountId,
                            StatusName = cr.Status.ToString().FromPascalCase(),
                            providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                            contractorId = jobOffer.ProviderId,
                            details = jobOffer.OfferJson.FromJson()
                        };
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.Sykes)
                {
                    var skDispatch = SykesDispatch.GetByCallRequestId(id);
                    if (q["rawDetails"] == "1")
                    {
                        var sykesMessage = JsonConvert.DeserializeObject<SykesMessage>(skDispatch.DispatchJson);
                        if (skDispatch != null)
                            return new HttpResponseMessage()
                            {
                                Content = new StringContent(
                                    await ddxmlToRawHtmlAsync(sykesMessage.JsonData, cr, null, darkMode))
                            };
                    }
                    else
                    {
                        return new
                        {
                            callRequest = cr,
                            MasterAccountId = ac.MasterAccountId,
                            StatusName = cr.Status.ToString().FromPascalCase(),
                            providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                            contractorId = skDispatch.ContractorId,
                            details = skDispatch.DispatchJson.FromJson()
                        };
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.Honk)
                {
                    var jobOffer = HonkJob.GetByCallRequestId(id);

                    if (q["rawDetails"] == "1")
                    {
                        if (jobOffer != null)
                        {
                            var honkMessage = JsonConvert.DeserializeObject<HonkMessage>(jobOffer.OfferJson);

                            var honkHtml = new StringBuilder();
                            var job = JsonConvert.DeserializeObject<HonkRestClient.JobModelContainer>(honkMessage.JsonData).Job;

                            if (job != null)
                            {
                                honkHtml.AppendLine(getStyle(darkMode));

                                if (job.estimated_payout_cents != null && job.estimated_payout_cents > 0)
                                {
                                    var hideOfferPrice = false;

                                    if (currentUser.Type == Towbook.User.TypeEnum.Dispatcher)
                                    {
                                        var preventDispatchersFromViewingCharges =
                                            Towbook.Integration.CompanyKeyValue.GetFirstValueOrNull(cr.CompanyId,
                                            Towbook.Integration.Provider.Towbook.ProviderId,
                                            "PreventDispatchersFromViewingInvoiceItems");

                                        if (preventDispatchersFromViewingCharges == "1")
                                            hideOfferPrice = true;
                                    }

                                    if (!hideOfferPrice)
                                        honkHtml.AppendLine("<div class=\"honk-offer-amount jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                                            (job.estimated_payout_cents.Value / 100).ToString("C") + ".</strong></div>\n\n");
                                }

                                honkHtml.AppendLine("<ul>");

                                if (job.Notes != null && job.Notes.Any())
                                {
                                    honkHtml.Append("<li><strong>Notes</strong>");

                                    foreach (var line in job.Notes)
                                    {
                                        honkHtml.AppendLine(line.Text + "<br />\n");

                                    }
                                    honkHtml.AppendLine("</li>");
                                }

                                honkHtml.AppendLine("</ul>");
                            }
                            var html = new HttpResponseMessage()
                            {
                                Content = new StringContent(honkHtml.ToString())
                            };
                            html.Content.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("text/html");
                            return html;
                        }
                    }
                    else
                    {
                        return new
                        {
                            callRequest = cr,
                            MasterAccountId = ac.MasterAccountId,
                            StatusName = cr.Status.ToString().FromPascalCase(),
                            providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                            contractorId = jobOffer.ProviderId,
                            details = jobOffer.OfferJson.FromJson()
                        };
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.Gerber)
                {
                    var jobOffer = GerberDispatch.GetByCallRequestId(id);
                    if (q["rawDetails"] == "1")
                    {
                        if (jobOffer != null)
                        {
                            var html = new HttpResponseMessage()
                            {
                                Content = new StringContent(
                                    await ddxmlToRawHtmlAsync(jobOffer.DispatchJson, cr, darkMode: darkMode)),

                            };
                            html.Content.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("text/html");
                            return html;
                        }
                    }
                    else
                    {
                        return new
                        {
                            callRequest = cr,
                            MasterAccountId = ac.MasterAccountId,
                            StatusName = cr.Status.ToString().FromPascalCase(),
                            providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                            contractorId = jobOffer.ProviderId,
                            details = jobOffer.DispatchJson.FromJson()
                        };
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.Towbook)
                {
                    if (q["rawDetails"] == "1")
                    {
                        var en = await Entry.GetByIdAsync(Convert.ToInt32(cr.PurchaseOrderNumber));
                        var sb = new StringBuilder();
                        sb.AppendLine(getStyle(darkMode));

                        sb.AppendLine("<ul>");

                        if ((en.CompanyId == 96242 ||
                             en.CompanyId == 101960 ||
                             await en.Company.HasFeatureAsync(Features.DispatchToSubcontractors_SendtoAllProviders)) && en.Account != null)
                            sb.AppendLine($"<li><strong>Offer Type:</strong> {en.Account.Company}</li>");

                        if (!string.IsNullOrWhiteSpace(en.Notes))
                        {
                            sb.AppendLine("<li><strong>Notes:</strong>");

                            var notes = en.Notes ?? "N/A";

                            if (notes.Contains("["))
                                notes = string.Join("\n",
                                            notes.Split('\n')
                                            .Where(o => !o.StartsWith("[") && !o.EndsWith("]")));

                            sb.Append(notes ?? "N/A");

                            sb.AppendLine("</li>");
                        }

                        sb.AppendLine("</ul>");

                        var html = new HttpResponseMessage()
                        {
                            Content = new StringContent(sb.ToString())
                        };

                        html.Content.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("text/html");

                        return html;
                    }
                }
                else if (ac.MasterAccountId == MasterAccountTypes.Fleetnet ||
                         ac.MasterAccountId == MasterAccountTypes.Servicase)
                {
                    var fo = FleetnetJobOffer.GetByCallRequestId(id);

                    var fltRequest = JsonConvert.DeserializeObject<ServiceDispatchRequest>
                        (fo.OfferJson);

                    // warning: we need to remove this at a higher level
                    fltRequest.ApiKey = null;
                    fltRequest.DispatchSoftwareProvider = 0;

                    if (q["rawDetails"] == "1")
                    {
                        return new HttpResponseMessage()
                        {
                            Content = new StringContent(
                                       await ddxmlToRawHtmlAsync(fltRequest.ToJson(null), cr, darkMode: darkMode))
                        };
                    }
                    else
                    {
                        return new
                        {
                            callRequest = cr,
                            MasterAccountId = ac.MasterAccountId,
                            StatusName = cr.Status.ToString().FromPascalCase(),
                            providerName = MasterAccountTypes.GetName(ac.MasterAccountId),
                            contractorId = fltRequest.CallProviderId,
                            details = fltRequest
                        };
                    }
                }
                else
                {
                    // warning: no implementation for this motor club. 
                    var lei = new LogEventInfo();
                    lei.Level = LogLevel.Warn;
                    lei.Properties["callRequestId"] = id;
                    if (WebGlobal.CurrentUser != null)
                    {
                        lei.Properties["companyId"] = currentUser.CompanyId;
                        lei.Properties["userId"] = currentUser.Id;
                        lei.Properties["userId"] = currentUser.Id;
                    }
                    lei.Message = "Implementation Missing for " + ac.MasterAccountId + " / " + MasterAccountTypes.GetName(ac.MasterAccountId);

                    logger.Log(lei);

                    return new HttpResponseMessage()
                    {
                        Content = new StringContent("")
                    };
                }
            }

            if (await CustomCompanyIdsPerUserAsync(currentUser))
            {
                var companyIdsJson = (await Towbook.Integration.UserKeyValue.GetByUserAsync(currentUser.CompanyId,
                    currentUser.Id, Towbook.Integration.Provider.Towbook.ProviderId,
                    "CallRequests_CompanyIds")).FirstOrDefault()?.Value;

                if (companyIdsJson != null && companyIdsJson.StartsWith("[") && companyIdsJson.EndsWith("]"))
                {
                    try
                    {
                        var companyIds = JsonConvert.DeserializeObject<int[]>(companyIdsJson);

                        if (!companyIds.Contains(cr.CompanyId))
                            return null;
                    }
                    catch
                    {
                    }
                }
            }

            var ret = await CallRequestModel.MapAsync(cr);

            if (allowDriver)
            {
                // TODO: add a way to make specific accounts callrequests go to drivers. 
                if (cr.AccountName != null && 
                    !cr.AccountName.ToLowerInvariant().Contains("splendora") &&
                    !cr.AccountName.ToLowerInvariant().Contains("willis"))
                {
                    return null;
                }
            }

            return ret;
        }

        [HttpGet("{id}/events")]
        public async Task<object> Events(int id, [FromQuery] bool extended = false)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                return Array.Empty<CallRequestEvent>();

            if (extended)
            {
                var cr = await CallRequest.GetByIdAsync(id);

                await ThrowIfNoCompanyAccessAsync(cr?.CompanyId, "digital dispatch request's event history");

                var data = CallRequestEvent.GetByCallRequestId(id);

                return data.Select(o => Map2(o));
            }

            return CallRequestEvent.GetByCallRequestId(id).Select(o => new
            {
                Id = o.DispatchEntryRequestEventId,
                o.ActionId,
                o.Message,
                Data = JsonConvert.DeserializeObject<object>(o.JsonData),
                o.Source,
                o.UserId,
                o.CreateDate,
            });
        }

        [HttpGet]
        [Route("metrics")]
        public async Task<IEnumerable<CallRequestsMetric>> Metrics(
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? accountId = null,
            int? masterAccountId = null,
            string events = null,
            string groupBy = null)
        {
            if (string.IsNullOrWhiteSpace(events))
                throw new Exception("You must specify the events separated by comma, ie. 1,2,.");

            if (accountId.GetValueOrDefault() < 1) // && !masterAccountId.HasValue)
                throw new Exception("You must specify either an accountId"); // or a masterAccountId.");

            if (!string.IsNullOrEmpty(groupBy) && groupBy != "Hour" && groupBy != "Day" && groupBy != "Week" && groupBy != "Month")
                throw new Exception("Group by must be one of the following: [Hour, Day, Week, Month].");

            var a = await Account.GetByIdAsync(accountId.Value);
            await ThrowIfNoCompanyAccessAsync(a?.CompanyId, "account");

            // parses the event/action types
            List<int> actions = new List<int>();
            string[] eventsArray = events.Replace(" ", "").Split(',');

            if (eventsArray.Length > 0)
                foreach (var e in eventsArray)
                {
                    int number;
                    if (int.TryParse(e, out number))
                        actions.Add(number);
                    else
                        throw new Exception("You must specify the events separated by comma, ie. 1,2,3.");
                }
            else
                throw new Exception("You must specify at least one event type.");

            CallRequestsMetricGroupings group = CallRequestsMetricGroupings.Day;
            if (!string.IsNullOrWhiteSpace(groupBy))
                Enum.TryParse(groupBy, out group);
            
            return CallRequestsMetric.Get(actions, group, accountId, masterAccountId, startDate, endDate);
        }

        public static string TranslateAlias(Dictionary<string,string> map, string name)
        {
            if (map.ContainsKey(name))
                return AddSpacesToSentence(map[name?.ToLower()]);
            else
            {
                if (name.Count(o => char.IsUpper(o)) != name.Length)
                    return AddSpacesToSentence(name);
                else
                    return AddSpacesToSentence(Core.FormatName(name).Replace("Date/time", "Date/Time"));
            }
        }

        public static string AddSpacesToSentence(string text, bool preserveAcronyms = true)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;
            StringBuilder newText = new StringBuilder(text.Length * 2);
            newText.Append(text[0].ToString().ToUpperInvariant());
            for (int i = 1; i < text.Length; i++)
            {
                if (char.IsUpper(text[i]))
                    if ((text[i - 1] != ' ' && !char.IsUpper(text[i - 1])) ||
                        (preserveAcronyms && char.IsUpper(text[i - 1]) &&
                         i < text.Length - 1 && !char.IsUpper(text[i + 1])))
                        newText.Append(' ');
                newText.Append(text[i]);
            }
            return newText.ToString();
        }

        private static string getStyle(bool darkMode = false)
        {
            var forecolor = "#333";
            var forecolor2 = "#777";
            var backcolor = "inherit";

            if (darkMode)
            {
                forecolor = "#e6e6e6";
                forecolor2 = "#b8b8b8";
                backcolor = "#404040";
            }
            var isMobile = Web.HttpContext.Current.IsAndroidDevice() || Web.HttpContext.Current.IsAppleDevice();

            return "<style>" +
                (isMobile ? "body { background-color: " + backcolor + "}\n* {font-family:Calibri, GillSans, Arial; color: " + forecolor + "}\n" : "") +
                ".cr-dd-root * {font-family:Calibri, GillSans, Arial; color: " + forecolor + "; list-style:none} " +
                ".cr-dd-root span { color: " + forecolor2 + "; font-weight: bold; display:inline-block; min-width:100px } " +
                ".cr-dd-root { padding-left: 15px }" +
                "</style>";
        }

        public static async Task<string> ddxmlToRawHtmlAsync(string detailedDispatch, CallRequest cr, AllstateDispatch asd = null, bool darkMode = false)
        {
            var c = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(detailedDispatch,
                new Newtonsoft.Json.JsonSerializerSettings()
                {
                    Formatting = Newtonsoft.Json.Formatting.Indented,
                    NullValueHandling = NullValueHandling.Ignore
                });

            UrgentlyRestClient.JobOfferModel urgentlyJob = null;

            if (cr.MasterAccountId == MasterAccountTypes.Urgently ||
                cr.MasterAccountId == MasterAccountTypes.OonUrgently)
                urgentlyJob = JsonConvert.DeserializeObject<UrgentlyRestClient.JobOfferModel>(detailedDispatch);

            var sb = new StringBuilder();


            sb.AppendLine(getStyle(darkMode));

            sb.AppendLine("<ul style='margin:0;padding:0' class='cr-dd-root'>");

            if (cr.MasterAccountId == MasterAccountTypes.Agero)
            {
                if (c?.CallReason?.DisablementReason == "Accident" &&
                    detailedDispatch.Contains("Vehicle is at the scene of an ACCIDENT"))
                {
                    sb.AppendLine("<div style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">" +
                        "Vehicle is at the scene of an ACCIDENT and an expedited ETA is required.<br />" +
                        "You'll need to give and meet a 30-minute ETA in order to receive your accident rate.</strong></div>\n\n");
                }

                sb.AppendLine("<li>COVID-19 Safety Reminder: Follow best practices to keep you and your customers safe. " +
                    "We recommend wearing masks, carrying hand sanitizer in your truck, and washing your hands whenever possible.</li>\n");
                
            }

            string[] keysToIgnore = new string[]
            {
                "Lat", "Lon", "TimeZone", "PrimaryTask",
                "Year", "Make", "Model", "Color", "Coverage",
                "ServiceType", "poNumber", "DispatchRequestNumber", "MailAddr",
                "context", "phoneCode", "offerExpires", "offerExpiresLocal",
                "typeId", "status", "dispatchId", "customer", "phone", "assignedBy", "assignedTime",
                "maxeta", "expires", "timestamp",

                // fleetnet
                "api_key", "dispatch_software_provider_id","service_provider_BGsync_id",
                "request_type",
                "call_provider_BGsync_id",
                "BGsync_request_id",

                // OON - Agero
                "CustomerCancelledPrice",  "GoaPrice", "OfferPrice", "AgeroCallbackNumber", "ReceivedTime",
                "ContactInfo", 
                
                // OON - Allstate
                "Boost", "OfferAmount", "GoaAmount", "Terms",
                "OrderNo", 
                "OrderNoAsInt",
                "VIN", 

                "ETARevisionCount",
                "ReferenceNumber",
                "ETAStatusCode",
                "EtaInMinutes",
                "ETA",
                "RevisedETA",
                "RevisedETAInMinutes",
                "Signatures",
                "AssignedDriver",

                // sykes
                "referenceId", "serviceDate", "authorizationId", "dispositions", "destinations", "etaDate", "available", "communication", "contact",

                // urgently
                "serviceprice",

                // agero
                "FlexCallReason",

                // Allstate REST
                "PaymentInformation", 
                "ExchgVehicleInfo", 
                "ExchgPickupAddr",
                "ExchgDestAddr",
                "CallerInfo",
                "MemberInfo",
                "DestinationAddress",
                "IncidentAddress"
            };

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher)
            {
                var preventDispatchersFromViewingCharges =
                    Towbook.Integration.CompanyKeyValue.GetFirstValueOrNull(cr.CompanyId,
                    Towbook.Integration.Provider.Towbook.ProviderId,
                    "PreventDispatchersFromViewingInvoiceItems");

                if (preventDispatchersFromViewingCharges == "1")
                    keysToIgnore = keysToIgnore.Union(new[] { "calculatedCost" }).ToArray();
            }

            if (cr.MasterAccountId == MasterAccountTypes.OonUrgently)
            {
                keysToIgnore = keysToIgnore.Union(new[] { "category", "partnerName", "isUnAttenedTow", "rateCard" }).ToArray();
            }

            var hideOfferPrice = false;

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher)
            {
                var preventDispatchersFromViewingCharges =
                    Towbook.Integration.CompanyKeyValue.GetFirstValueOrNull(cr.CompanyId,
                    Towbook.Integration.Provider.Towbook.ProviderId,
                    "PreventDispatchersFromViewingInvoiceItems");

                if (preventDispatchersFromViewingCharges == "1")
                    hideOfferPrice = true;
            }

            if (cr.MasterAccountId == MasterAccountTypes.Allstate)
            {
                if (c != null &&
                    c.JobInfo != null &&
                    c.JobInfo.Boost != null &&
                    c.JobInfo.Boost.Amount != null &&
                    c.JobInfo.Boost.Amount != 0)
                    sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; margin-top: 10px; color: #ffffff; padding: 10px; margin: 0px -10px\">Additional Boost Amount: " +
                        ((decimal)c.JobInfo.Boost.Amount).ToString("C") + "</div>");
            }

            bool hasOfferPrice = false;

            OonAgeroClient.OonDispatch oond = null;
            if (cr.MasterAccountId == MasterAccountTypes.OonAgero)
            {
                oond = JsonConvert.DeserializeObject<OonAgeroClient.OonDispatch>(detailedDispatch,
                    new JsonSerializerSettings()
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    });

                if (oond != null)
                {
                    sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                        ((decimal)oond.OfferPrice).ToString("C") + ".</strong> | GOA Price: " + ((decimal)oond.GoaPrice).ToString("C") + "" +
                        "<br />Credit Card is issued automatically upon completion of the job in Towbook.</div>");
                }

                if (Web.HttpContext.Current.IsAppleDevice())
                {
                    sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the Agero Out of Network Terms and Conditions displayed below. If you do not agree, refuse this dispatch. Additional info regarding this dispatch is listed below the terms and conditions.</strong>");
                    var html = System.IO.File.ReadAllText(Path.Combine(Web.HttpContext.Env.ContentRootPath, "agero.htm"), Encoding.UTF8);
                    sb.Append(html);
                }
                else
                {
                    sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-agero-towbook.htm\">Agero Out of Network Terms and Conditions (click to view)</a>, If you do not agree, refuse this dispatch.</strong>");
                }

                var oad = OonAgeroDispatch.GetByCallRequestId(cr.CallRequestId);

                if (oad.ClosestDriverDistanceToDisablement != oad.CompanyDistanceToDisablement &&
                    oad.ClosestDriverDistanceToDisablement < oad.CompanyDistanceToDisablement)
                    sb.AppendLine("<li>Distance from your nearest driver: <span style=\"font-weight:bold\">" + oad.ClosestDriverDistanceToDisablement + " miles (" +
                        oad.CompanyDistanceToDisablement + " miles from your office)</span></li>");
                else
                    sb.AppendLine("<li>Distance from your office: <span style=\"font-weight:bold\">" + oad.CompanyDistanceToDisablement + " miles</span></li>");

                if (oad.DisablementToDestinationDistance > 0)
                    sb.AppendLine("<li>Loaded Distance: <span style=\"font-weight:bold\">" + oad.DisablementToDestinationDistance + " miles</span></li>");
            }
            else if (cr.MasterAccountId == MasterAccountTypes.OonAllstate)
            {
                if (c != null &&
                    c.JobInfo != null &&
                    c.JobInfo.OfferAmount != null &&
                    c.JobInfo.OfferAmount != 0)
                {
                    decimal totalJob = ((decimal?)c.JobInfo.OfferAmount).GetValueOrDefault();
                    decimal coveredAmt = ((decimal?)c.JobInfo.CoveredAmt).GetValueOrDefault();
                    decimal customerPay = ((decimal?)c.JobInfo.CustomerPayAmt).GetValueOrDefault();

                    var payLine = totalJob.ToString("C");

                    if (customerPay != 0)
                    {
                        payLine = totalJob.ToString("C") +
                            " (Allstate Pays: " + coveredAmt.ToString("C") +
                            " | Customer Pays " + customerPay.ToString("C") + ")";
                    }

                    sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\">" +
                        "<strong style=\"color: #ffffff; font-size: 16px\">This Job Pays: " +
                        payLine + "</strong> | GOA Price: " + ((decimal)c.JobInfo.GoaAmount).ToString("C") + "<br />" +
                        "Credit Card is issued automatically upon completion of the job in Towbook.</div>\n\n");

                    if (asd.CompanyDistanceToDisablement > 0)
                    {
                        if (asd.ClosestDriverDistanceToDisablement != asd.CompanyDistanceToDisablement &&
                            asd.ClosestDriverDistanceToDisablement < asd.CompanyDistanceToDisablement)
                            sb.AppendLine("<li>Distance from your nearest driver: <span style=\"font-weight:bold\">" + asd.ClosestDriverDistanceToDisablement + " miles (" +
                                asd.CompanyDistanceToDisablement + " miles from your office)</span></li>");
                        else
                            sb.AppendLine("<li>Distance from your office: <span style=\"font-weight:bold\">" + asd.CompanyDistanceToDisablement + " miles</span></li>");
                    }

                    if (asd.DisablementToDestinationDistance > 0)
                        sb.AppendLine("<li>Loaded Distance: <span style=\"font-weight:bold\">" + asd.DisablementToDestinationDistance + " miles</span></li>");
                    if ((await Company.Company.GetByIdAsync(cr.CompanyId))?.Country != Company.Company.CompanyCountry.Canada)
                        sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-allstate-towbook.htm\">Allstate Out of Network Terms and Conditions (click to view, updated 12/13/24)</a>. If you do not agree, refuse this dispatch.</strong><br />");
                    else
                        sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-allstate-towbook.htm\">Allstate Out of Network Terms and Conditions (click to view, updated 12/13/24)</a>. If you do not agree, refuse this dispatch.</strong><br />");
                }
            }
            else if (cr.MasterAccountId == MasterAccountTypes.Urgently && !hideOfferPrice)
            {
                if (urgentlyJob?.service?.ServicePrice?.TotalOfferPrice != null)
                {
                    if (!urgentlyJob.service.ServicePrice.HideProviderPrice.GetValueOrDefault())
                    {
                        // don't show price if its over 1250 
                        if (urgentlyJob.service.ServicePrice.TotalOfferPrice.Value < 1250)
                        {
                            sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                                urgentlyJob.service.ServicePrice.TotalOfferPrice.Value.ToString("C") + ".</strong></div>\n\n");

                            hasOfferPrice = true;
                        }
                    }
                }
            }
            else if (cr.MasterAccountId == MasterAccountTypes.OonUrgently && urgentlyJob?.service?.ServicePrice?.TotalOfferPrice != null)
            {
                sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">This Job Pays: " +
                    urgentlyJob.service.ServicePrice.TotalOfferPrice.Value.ToString("C") + ".</strong> | GOA Price: " + 
                    (urgentlyJob.service.ServicePrice.GoaPrice?.ToString("C") ?? "UNKNOWN") + "<br />" +
                    "Credit Card is issued automatically upon completion of the job in Towbook.</div>\n\n");


                if (Web.HttpContext.Current.IsAppleDevice())
                {
                    sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the Urgently Out of Network Terms and Conditions displayed below. If you do not agree, refuse this dispatch. Additional info regarding this dispatch is listed below the terms and conditions.</strong>");
                    var html = System.IO.File.ReadAllText(Path.Combine(Web.HttpContext.Env.ContentRootPath, "urgently.htm"), Encoding.UTF8);
                    sb.Append(html);
                }
                else
                {
                    sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-urgently-towbook.htm\">Urgently Out of Network Terms and Conditions (click to view)</a>, If you do not agree, refuse this dispatch.</strong>");
                }

            }
            else if ((cr.MasterAccountId == MasterAccountTypes.OonQuest) && !hideOfferPrice)
            {
                if (c.PaymentInfo?.QuotePrice != null)
                {
                    sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">This Job Pays: " +
                        ((decimal)c.PaymentInfo.QuotePrice).ToString("C") + ".</strong> | GOA Price: " +
                        ((decimal)c.PaymentInfo.GoaPrice).ToString("C") + "<br />" +
                        "Credit Card is issued automatically upon completion of the job in Towbook.</div>\n\n");
                }

                if (Web.HttpContext.Current.IsAppleDevice())
                {
                    
                    sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the Quest Out of Network Terms and Conditions displayed below. If you do not agree, refuse this dispatch. Additional info regarding this dispatch is listed below the terms and conditions.</strong>");
                    var html = System.IO.File.ReadAllText(Path.Combine(Web.HttpContext.Env.ContentRootPath, "quest.htm"), Encoding.UTF8);
                    sb.Append(html);
                }
                else
                {
                    sb.AppendLine("<strong style=\"color: #333\">By accepting this dispatch, you agree to the <a target=\"_new\" href=\"https://app.towbook.com/security/oon-quest-towbook.htm\">Quest Out of Network Terms and Conditions (click to view)</a>, If you do not agree, refuse this dispatch.</strong>");
                }
            }
            else if (cr.MasterAccountId == MasterAccountTypes.Quest && !hideOfferPrice)
            {
                if (c.PaymentInfo != null &&
                    c.PaymentInfo.QuotePrice != null)
                {
                    sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; color: #ffffff; padding: 5px 10px; margin: 0px -10px\"><strong style=\"color: #ffffff; font-size: 16px\">Job Offer Amount: " +
                        ((decimal)c.PaymentInfo.QuotePrice).ToString("C") + ".</strong></div>\n\n");
                }
            }
            
            foreach (var key in c)
            {
                var values = key as IEnumerable<dynamic>;

                if (key.Value != null)
                {
                    if ((key.Value as IEnumerable<dynamic>).Count() > 1 || 
                        key.Name == "tire_data"  ||
                        key.Name == "tow_data")
                    {
                        string zname = key.Name;
                        if (zname == "AccountInfo")
                            zname = "Contact Information";

                        if (zname == "CurrentStatus" || zname == "context" || zname == "provider" || zname == "customer" || zname == "Contact Information")
                            continue;

                        string content = null;
                        if (zname == "coverage" && cr.MasterAccountId == MasterAccountTypes.Sykes)
                        {
                            var coverage = JsonConvert.DeserializeObject<SykesRestClient.Coverage>(Convert.ToString(key.Value));
                            content = GetHtmlCoverageSykes(coverage);
                        }

                        if (keysToIgnore.Any(o => o.ToLowerInvariant() == key.Name.ToLowerInvariant()))
                            if (string.IsNullOrEmpty(content))
                            {
                                continue;
                            }

                        int beforeAdd = sb.Length;
                        sb.AppendLine("<li style='list-style:none; margin-top: 10px'>");

                        sb.AppendLine("<strong>" + TranslateAlias(aliases, zname) + "</strong>");
                        sb.AppendLine("<ul>");

                        int beforeLength = sb.Length;

                        if (zname == "Vehicle" && oond == null)
                        {
                            sb.AppendFormat("  <li style='list-style:none'>{0} {1} {2} {3}<br />{4}</li>", key.Value.Year, key.Value.Make, key.Value.Model, key.Value.Color, key.Value.AdditionalInfo);
                        }
                        else if (zname == "vehicle" && key.Value.style != null) // gerber.
                        {
                            sb.AppendFormat("  <li style='list-style:none'>{0} {1} {2} {3}<br />{4}</li>", key.Value.year, key.Value.make, key.Value.model, key.Value.color, key.Value.style);
                        }
                        else if (zname == "Comments")
                        {
                            var html = "<ul>";

                            foreach (var x in key.Value)
                            {
                                html += String.Format("  <li><span>{0}:</span> {1}</li>", x.DisplayText ?? x.CommentCode, x.CommentText);
                            }

                            html += "</ul>";
                            sb.AppendLine(String.Format("  <li><span>{0}</span> {1}</li>", "Comments", html));
                        }
                        else if (zname == "additional" && cr.MasterAccountId == MasterAccountTypes.Sykes) // sykes case
                        {
                            var html = GetHtmlAdditionalSykes(key.Value);
                            if (!String.IsNullOrEmpty(html))
                            {
                                sb.AppendLine(html);
                            }
                        }
                        else if (zname == "coverage") // sykes case
                        {
                            sb.AppendLine(content);
                        }
                        else
                        {
                            foreach (var key3 in key.Value)
                            {
                                if (key3 is IEnumerable<object> &&
                                    (key.Name == "tow_data" || key.Name == "tire_data"))
                                {
                                    foreach (var item in (key3 as IEnumerable<dynamic>))
                                    {
                                        string name = item.Name;
                                        if (name.StartsWith("tow_"))
                                            name = name.Substring(4);
                                        else if (name.StartsWith("tire_"))
                                            name = name.Substring(5);
                                        name = Core.FormatName(name);

                                        sb.AppendLine(String.Format("  <li><span>{1}</span> {0}</li>",
                                            item.Value, name));
                                    }
                                    continue;

                                }

                                if (string.IsNullOrEmpty(Convert.ToString(key3.Name)))
                                    continue;

                                string key3name = key3.Name.ToString().ToLower();

                                if (new string[] { "jobid", "requiredacknowledgetime", "scheduled", "ratecardasdollars", "dispatchid", "agerocallbacknumber", "receivedtime", "serviceprice" }.Contains(key3name))
                                    continue;
                                else if (key3name == "category" && key3.Value == "RSA_SCHEDULED_SERVICE")
                                {
                                    key3.Value = "Scheduled Service";
                                    sb.AppendLine(String.Format("  <li><span>Category</span> {0}</li>", key3.Value));
                                    continue;
                                }
                                else if (zname == "service" && key3.Name == "id")
                                {
                                    sb.AppendLine(String.Format("  <li><span>Job Number</span> {0}</li>", key3.Value));
                                    continue;
                                }
                                if (decimal.TryParse(key3.Value?.ToString(), out decimal dec))
                                {
                                    if (key3.Value.ToString().StartsWith("0"))
                                        key3.Value = dec.ToString();
                                }
                                else if (DateTime.TryParse(key3.Value?.ToString(), out DateTime date))
                                {
                                    DateTime? realDate = null;
                                    if (key3.Value.Type == Newtonsoft.Json.Linq.JTokenType.Date)
                                    {
                                        realDate = (DateTime)(key3.Value);
                                    }

                                    date = Core.OffsetDateTime(WebGlobal.CurrentUser.Company, realDate ?? date.ToLocalTime());
                                    key3.Value = date.ToShortDateString() + " " + date.ToShortTowbookTimeString();
                                }

                                if (key3name == "member")
                                {
                                    if (key3.Value.FirstName != null && key3.Value.LastName != null)
                                        sb.AppendLine(String.Format("  <li><span>{0}:</span> {1} {2}</li>", key3.Name, key3.Value.FirstName, key3.Value.LastName));
                                }
                                else if (key3name == "customer")
                                {
                                    var kv = key3.Value;
                                    sb.AppendLine(String.Format("<li><span>Customer:</span> {0} {1}</li>", kv.FirstName, kv.LastName));
                                }
                                else if (key3name == "contactinfo" && cr.Status == CallRequestStatus.Accepted)
                                {
                                    var kv = key3.Value;
                                    if (kv != null)
                                    {
                                        sb.AppendLine("  <li><strong>Contact Info</strong><ul>");
                                        sb.AppendLine(String.Format("<li><span>Name</span> {0}</li>",
                                            Core.FormatName(kv.Name)));
                                        if (kv.Phone != null)
                                            sb.AppendLine(String.Format("<li><span>Phone</span> {0}</li>",
                                                Core.FormatPhone(kv.Phone.ToString())));

                                        if (kv.CallbackNumber != null)
                                            sb.AppendLine(String.Format("<li><span>Callback Phone</span> {0}</li>",
                                                Core.FormatPhone(kv.CallbackNumber.ToString())));

                                        sb.AppendLine("    </ul>");
                                        sb.AppendLine("  </li>");
                                    }
                                }
                                else if (key3name == "member" && cr.Status == CallRequestStatus.Accepted)
                                {
                                    var kv = key3.Value;
                                    sb.AppendLine(String.Format("<li><span>Member</span> {0} {1}</li>", kv.FirstName, kv.LastName));
                                }
                                else if (key3name == "primarytasks" && key3.Value.Count == 1)
                                {
                                    sb.AppendFormat("    <li><span>Task</span> {0}", key3.Value[0].Task);
                                }
                                else if (key3name == "secondarytasks")
                                {
                                    var value = String.Join(",", key3.Value as IEnumerable<dynamic>);
                                    if (!String.IsNullOrWhiteSpace(value))
                                        sb.AppendLine(String.Format("    <li><span>{0}</span> {1}</li>", TranslateAlias(aliases, key3.Name),
                                            value));
                                }

                                else if (key3.Value != null && !String.IsNullOrWhiteSpace(key3.Value.ToString()) && key3.Value.ToString() != "0")
                                {
                                    if (!keysToIgnore.Where(o => o.ToLowerInvariant() == key3.Name.ToLowerInvariant()).Any())
                                    {
                                        if (key3name == "callbackphone" || key3name == "callbacknumber") key3name = "Phone #";
                                        if (key3name == "neutralcapable" && key3.Value == "N")
                                            key3.Value = "No";

                                        if (key3name.Contains("phone"))
                                        {
                                            key3.Value = Core.FormatPhone(key3.Value.ToString());
                                        }
                                        else if (key3name == "timestamp")
                                        {
                                            try
                                            {
                                                DateTime dt = DSPMessageJobInfo.ConvertAllstateTimestampToDateTime(key3.Value.ToString());

                                                key3.Value = Core.OffsetDateTime(WebGlobal.CurrentUser.Company, dt).ToShortTowbookTimeString();
                                            }
                                            catch (Exception e)
                                            {
                                                key3.Value = e.ToString();
                                            }
                                        }
                                        else if (key3name == "ratecard")
                                        {
                                            if (hasOfferPrice)
                                                continue;
                                            else
                                                key3.Value = key3.Value + " " + UrgentlyRestClient.JobOfferService.GetDollarsForRateCard(key3.Value.ToString());
                                        }
                                        else if (key.Name == "inspection" && key3.Value.ToString() == "False")
                                        {
                                            continue;
                                        }
                                        else if (key.Name == "inspection" && cr.MasterAccountId == MasterAccountTypes.OonUrgently)
                                        {
                                            continue;
                                        }

                                        if (key.Name == "Comments" && key.Value == "[]")
                                            continue;

                                        if (key3name == "vin")
                                        {
                                            sb.AppendLine(String.Format("  <li><span>{0}</span> {1}</li>",
                                                TranslateAlias(aliases, key3.Name),
                                                key3.Value.ToString().ToUpperInvariant()));
                                        }
                                        else if (key3name == "additional" && cr.MasterAccountId == MasterAccountTypes.Sykes)
                                        {
                                            var html = GetHtmlAdditionalSykes(key3.Value);
                                            if (!String.IsNullOrEmpty(html))
                                            {
                                                sb.AppendLine(String.Format("  <li><span>{0}</span> <ul>{1}</ul></li>",
                                                TranslateAlias(aliases, key3.Name), html));
                                            }
                                        }
                                        else
                                        {
                                            var n = Core.FormatName(key3.Value.ToString().Replace("False", "No").Replace("True", "Yes"));

                                            if (n == "Y")
                                                n = "Yes";
                                            else if (n == "N")
                                                n = "No";

                                            sb.AppendLine(String.Format("  <li><span>{0}</span> {1}</li>",
                                                TranslateAlias(aliases, key3.Name), n));
                                        }
                                    }
                                }
                            }
                        }
                        int afterLength = sb.Length;
                        if (afterLength == beforeLength)
                        {
                            sb.Remove(beforeAdd, sb.Length - beforeAdd);
                        }
                        else
                        {
                            sb.AppendLine("</ul></li>");
                        }
                    }
                    else
                    {
                        if (key.Name == "ClientID" || 
                            key.Name == "Event" || 
                            key.Name == "ResponseID" 
                            || key.Name == "DispatchId" || 
                            keysToIgnore.Where(o => o.ToLower() == key.Name.ToString().ToLower()).Any())
                            continue;

                        if (key.Name == "TimeStamp")
                        {
                            try
                            {
                                DateTime dt = DSPMessageJobInfo.ConvertAllstateTimestampToDateTime(key.Value.ToString());

                                key.Value = Core.OffsetDateTime(WebGlobal.CurrentUser.Company, dt).ToShortTowbookTimeString();
                            }
                            catch (Exception e)
                            {
                                key.Value = e.ToString();
                            }
                        }
                        else if (key.Name == "providers")
                        {
                            sb.AppendLine("<div class=\"jobOfferAmount\" style=\"background-color: #3E9B4F; margin-top: 10px; color: #ffffff; padding: 10px; margin: 0px -10px\">Job Offer Amount: " +
                                Convert.ToDecimal((key.Value as IEnumerable<dynamic>).FirstOrDefault().offerPrice).ToString("C") + "</div>");

                            continue;
                        }
                        else if (key.Name == "additional") // sykes case
                        {
                            if ((key.Value as IEnumerable<dynamic>).Count() == 1)
                            {
                                var html = GetHtmlAdditionalSykes(key.Value);
                                if (!String.IsNullOrEmpty(html))
                                {
                                    sb.AppendLine("<li style='list-style:none; margin-top: 10px'>");
                                    sb.AppendLine("<strong>" + TranslateAlias(aliases, key.Name) + "</strong>");
                                    sb.AppendLine("<ul>");
                                    sb.AppendLine(html);
                                    sb.AppendLine("</ul></li>");
                                }
                            }
                            continue;
                        }

                        DateTime date = DateTime.MinValue;
                        if (DateTime.TryParse(key.Value.ToString(), out date))
                        {
                            date = Core.OffsetDateTime(WebGlobal.CurrentUser.Company, date.ToLocalTime());
                            key.Value = date.ToShortDateString() + " " + date.ToShortTowbookTimeString();
                        }

                        if (key.Name == "EquipmentList" && oond != null)
                        {
                            sb.AppendFormat("<li><strong>Equipment Required:</strong> {0}</li>\n",
                                string.Join(", ", oond.EquipmentList.Select(o => o.Type)));
                            continue;
                        }

                        if (!keysToIgnore.Any(o => o.ToLowerInvariant() == (key.Name as string)?.ToLowerInvariant())  &&
                            key.Value.ToString() != "{}")
                            sb.AppendFormat("<li>{0}: {1}</li>\n", 
                                TranslateAlias(aliases, key.Name), 
                                key.Value.ToString().Replace("False", "No").Replace("True", "Yes"));
                    }
                }
            }
            sb.AppendLine("</ul>");

            return sb.ToString();
        }

        private static string GetHtmlCoverageSykes(SykesRestClient.Coverage coverage)
        {
            var stringBuilder = new StringBuilder();
            if (!string.IsNullOrEmpty(coverage.Text))
            {
                stringBuilder.AppendLine(String.Format("  <li><span>{0}</span> {1}</li>", "Description", coverage.Text));
            }
            if (coverage.HasDetail("AUTHAMT"))
            {
                stringBuilder.AppendLine(String.Format("  <li><span>{0}</span> {1}</li>", "Covered Amount", coverage.GetDetail("AUTHAMT").GetFormatedValue()));
            }
            if (coverage.HasDetail("AUTHKM"))
            {
                stringBuilder.AppendLine(String.Format("  <li><span>{0}</span> {1}</li>", "Covered Distance", coverage.GetDetail("AUTHKM").GetFormatedValue()));
            }
            return stringBuilder.ToString();
        }

        private static string GetHtmlAdditionalSykes(dynamic entities)
        {
            if (entities == null || (entities as IEnumerable<dynamic>).Count() <= 0)
            {
                return string.Empty;
            }
            var html = string.Empty;
            foreach (var item in entities)
            {
                html += string.Format("<li><span>{0}</span> {1}</li>", item.label, item.value);
            }
            return html;
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public string IsscToRawHtml(string detailedDispatch, CallRequest cr, bool darkMode)
        {
            var c = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(detailedDispatch,
                new Newtonsoft.Json.JsonSerializerSettings()
                {
                    Formatting = Newtonsoft.Json.Formatting.Indented,
                    NullValueHandling = NullValueHandling.Ignore
                });

            var sb = new StringBuilder();


            sb.AppendLine(getStyle(darkMode));

            sb.AppendLine("<ul>");

            if (c.Job?.RequestedFutureDateTime != null)
            {
                var ftdate = DateTime.MinValue;

                if (DateTime.TryParse(c.Job?.RequestedFutureDateTime.ToString(), out ftdate))
                {
                    sb.AppendFormat("<li><span class='x-call-request-scheduled'>Scheduled Date and Time:</span> {0}</li>", Core.OffsetDateTime(WebGlobal.CurrentUser.Company, ftdate.ToLocalTime()));
                }
            }

            string[] keysToIgnore = new string[]
            {
                "Latitude", "Longitude", "TimeZone", "RequestedFutureDateTime"
            };

            foreach (var key in c)
            {
                var values = key as IEnumerable<dynamic>;

                if (key.Value != null)
                {
                    if ((key.Value as IEnumerable<dynamic>).Count() > 1)
                    {
                        sb.AppendLine("<li>");

                        string zname = key.Name;

                        if (zname == "AccountInfo")
                            zname = "Customer Contact";

                        sb.AppendLine("<strong>" + zname + "</strong>");
                        sb.AppendLine("<ul>");

                        if (zname == "Vehicle")
                        {
                            sb.AppendFormat("<li>{0} {1} {2} {3}<br />{4}</li>", key.Value.Year, key.Value.Make, key.Value.Model, key.Value.Color, key.Value.AdditionalInfo);
                        }
                        else
                            foreach (var key3 in key.Value)
                            {

                                if (new string[] { "JobID", "RequiredAcknowledgeTime", "RequestedFutureDateTime" }.Contains((string)key3.Name.ToString()))
                                    continue;

                                DateTime date = DateTime.MinValue;
                                if (DateTime.TryParse(key3.Value.ToString(), out date))
                                {
                                    date = Core.OffsetDateTime(WebGlobal.CurrentUser.Company, date.ToLocalTime());
                                    key3.Value = date.ToShortDateString() + " " + date.ToShortTowbookTimeString();
                                }

                                if (key3.Name == "Customer")
                                {
                                    if (cr.Status == CallRequestStatus.Accepted)
                                    {
                                        var kv = key3.Value;
                                        sb.AppendLine(String.Format("<li><span>Customer:</span> {0} {1}</li>", kv.FirstName, kv.LastName));
                                    }
                                }
                                else if (key3.Name == "Member")
                                {
                                    if (cr.Status == CallRequestStatus.Accepted)
                                    {
                                        var kv = key3.Value;
                                        var customerName = (key.Value as IEnumerable<dynamic>)
                                            .Where(o => o.Name == "Customer")
                                            .FirstOrDefault()?
                                            .Value;

                                        if (customerName == null || !(customerName.FirstName == kv.FirstName &&
                                            customerName.LastName == kv.LastName))
                                        {
                                            sb.AppendLine(String.Format("<li><span>Member:</span> {0} {1}</li>", kv.FirstName, kv.LastName));
                                        }
                                    }
                                }
                                else if (key3.Name == "PrimaryTasks" && key3.Value.Count == 1)
                                {
                                    sb.AppendFormat("<li><span>Task:</span> {0}", key3.Value[0].Task);
                                }
                                else if (key3.Name == "ServiceQuestions" && key3.Value != null)
                                {
                                    foreach (var x in key3.Value)
                                    {
                                        sb.AppendFormat("<li><span>{0}</span> {1}</li>\r\n",
                                            (x.Question + ":").ToString().Replace("?:", "?"),
                                            String.Join(",", x.Responses));
                                    }
                                }
                                else if (key3.Value != null && !String.IsNullOrWhiteSpace(key3.Value.ToString()) && key3.Value.ToString() != "0")
                                {
                                    if (!keysToIgnore.Where(o => o?.ToLowerInvariant() == (key.Name as string)?.ToLowerInvariant()).Any())
                                    {
                                        string name = key3.Name;
                                        if (name == "CallBackPhone")
                                            name = "Phone #";

                                        sb.AppendLine(String.Format("  <li><span>{0}:</span> {1}</li>", name, key3.Value.ToString().Replace("False", "No").Replace("True", "Yes")));
                                    }
                                }
                            }
                        sb.AppendLine("</ul></li>");
                    }
                    else
                    {

                        if (key.Name == "ClientID" || key.Name == "Event" || key.Name == "ResponseID" || key.Name == "DispatchID" || keysToIgnore.Where(o => o == key.Name).Any())
                            continue;
                        sb.AppendFormat("<li><span>{0}:</span> {1}</li>\n", key.Name, key.Value.ToString().Replace("False", "No").Replace("True", "Yes"));
                    }
                }
            }
            sb.AppendLine("</ul>");

            return sb.ToString();
        }

        private static async Task<CallRequest> MapAsync(CallRequest o)
        {
            // TODO: Create a CalLRequestModel and move this logic into its map method.
            // Update (12/24) There is now a CallRequestModel with a map function that matches
            // this logic. It is only used in one place now but it would be good to move all usages to take
            // advantage of it


            if (o == null)
                return null;

            var acc = await Account.GetByIdAsync(o.AccountId);

            o.MasterAccountId = acc.MasterAccountId;
            o.AccountName = acc.Company;

            if (o.MasterAccountId == MasterAccountTypes.OonAgero ||
                o.MasterAccountId == MasterAccountTypes.OonAllstate)
            {
                if (o.MasterAccountId == MasterAccountTypes.OonAgero)
                {
                    o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 91).ToArray();
                    o.MaxEta = 90;
                }

                if (o.MasterAccountId == MasterAccountTypes.OonAllstate)
                {
                    o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 301).ToArray();
                    o.MaxEta = 300;
                }
                o.ProviderId = await Integrations.Email.EmailAddress.GetPrimaryTowbookEmailAddressAsync(o.CompanyId);
            }

            if (o.MasterAccountId == MasterAccountTypes.OonAllstate ||
                o.MasterAccountId == MasterAccountTypes.Allstate)
            {
                var json = await Core.GetRedisValueAsync(o.CallRequestId + ":extra");
                try
                {
                    if (json != null)
                    {
                        var extra = JsonConvert.DeserializeObject<CallRequestExtraModel>(json);
                        if (extra != null)
                        {
                            if (extra.Amount != 0 && extra.Type != null)
                            {
                                o.OfferAmount = extra.Amount;
                                o.OfferTypeText = extra.Type;
                            }
                        }
                    }
                }
                catch
                {

                }

                o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 1000).ToArray();

                if (o.MaxEta == 0)
                    o.MaxEta = 999;
            }

            if (o.MasterAccountId == MasterAccountTypes.Towbook &&
                o.AccountName != null &&
                o.AccountName.ToLowerInvariant().Contains("county towing & storage"))
            {
                // TODO: DTS, Dispatch to Subcontractor: make maxEta come from sender,
                // this is a bit of a hack to take care of county towing & storage,
                // but others will need this.

                o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 31).ToArray();
                o.MaxEta = 30;
            }

            if (o.MasterAccountId == MasterAccountTypes.Towbook &&
               o.AccountName != null &&
               o.AccountName.ToLowerInvariant().Contains("splendora"))
            {
                o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 16).ToArray();
                o.MaxEta = 15;
            }

            if (o.MasterAccountId == MasterAccountTypes.Towbook &&
               o.AccountName != null &&
               o.AccountName.ToLowerInvariant().Contains("carvana"))
            {
                o.SupportedEtas = new int[] {
                    15, 30, 45, 60, 75, 90,
                    120, 180, 240, 360, 420, 480, 
                    540, 600, 1000, 1200, 1440
                };
                o.MaxEta = 1440;
            }

            var defaultEta = await Towbook.Integration.AccountKeyValue.GetFirstValueOrNullAsync(o.CompanyId, o.AccountId, Towbook.Integration.Provider.Towbook.ProviderId, "DefaultEta");
            if (defaultEta != null && int.TryParse(defaultEta, out int deta))
            {
                o.DefaultEta = deta;
            }

            if (o.ProviderId != null && o.ProviderId.Contains(".") && o.MaxEta == 0) // hack to detect that the call is an agero call.
                o.MaxEta = 90;
            else if (o.MaxEta == 0)
            {
                // iOS Beta workaround
                o.MaxEta = 240;
            }
            else
            {
                // another fix for iOS
                if (acc.MasterAccountId == MasterAccountTypes.Nsd)
                    o.MaxEta = 240;
            }

            if (o.MasterAccountId == MasterAccountTypes.OonUrgently)
            {
                o.MaxEta = 90;
                o.SupportedEtas = o.SupportedEtas.Where(v => v >= 30 && v < 91).ToArray();
            }

            if (o.MasterAccountId == MasterAccountTypes.AlliedDispatch)
            {
                o.MaxEta = 90;
                o.SupportedEtas = o.SupportedEtas.Where(v => v >= 30 && v < 91).ToArray();
            }

            if (o.MasterAccountId == MasterAccountTypes.OonUrgently ||
                o.MasterAccountId == MasterAccountTypes.Honk)
            {
                o.Drivers = (await Driver.GetByExactCompanyIdAsync(o.CompanyId))
                    .Where(x => x.IsActive())
                    .Select(rx => rx.Id).ToCsv();
            }

            if (o.MasterAccountId == MasterAccountTypes.Gerber)
            {
                o.SupportedEtas = o.SupportedEtas.Where(eo =>
                   eo > 5 &&
                   eo < 61).ToArray();

                o.MaxEta = 120;
            }
            else if (o.MasterAccountId == MasterAccountTypes.Urgently ||
                o.MasterAccountId == MasterAccountTypes.OonUrgently)
            {
                o.SupportedEtas = o.SupportedEtas.Where(eo =>
                    eo >= 30 &&
                    eo < 181).ToArray();

                o.MaxEta = 180;
            }

            return o;
        }

        private static object Map2(CallRequestEvent o)
        {
            DigitalDispatchActionQueueItemType actionId = (DigitalDispatchActionQueueItemType) o.ActionId;

            return new
            {
                Id = o.DispatchEntryRequestEventId,
                UserId = o.UserId,
                ActionId = o.ActionId,
                ActionText = actionId.ToString().FromPascalCase(),
                Message = o.Message,
                JsonData = o.JsonData,
                Data = o.JsonData.FromJson(),
                Source = o.Source,
                SourceText = o.Source.ToString().FromPascalCase(),
                CreateDate = o.CreateDate,
            };
        }

       

        public class CallRequestRejectRequestBody
        {
            public int Id { get; set; }
            public string FullName { get; set; }
            public string PhoneNumber { get; set; }
            public int MasterAccountReasonId { get; set; }
            public int OwnerUserId { get; set; }
            public string Comments { get; set; }
        }

        public class CallRequestRequestPhoneCallBody
        {
            public int Id { get; set; }
            public int MasterAccountReasonId { get; set; }
            public string FullName { get; set; }
            public string PhoneNumber { get; set; }
            public int OwnerUserId { get; set; }
        }

        public class CallRequestAcceptRejectResponse
        {
            public string Message { get; set;  }
        }

        public class CallRequestAcceptMissedBody
        {
            public int Id { get; set; }
            public string PurchaseOrderNumber { get; set; }
            public int ETA { get; set; }
            public int OwnerUserId { get; set; }
        }

        /// <summary>
        /// Accepts the specified call with the ETA of the call.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="accept"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{id}/accept")]
        public async Task<HttpResponseMessage> Accept(int id, CallRequestAcceptRequestBody accept)
        {
            var cr = CallRequest.GetById(id);

            await ThrowIfNoCompanyAccessAsync(cr?.CompanyId, "dispatch offer");

            var alreadyResponded = ReturnIfAlreadyResponded(cr, "Accept Offer");
            if (alreadyResponded != null)
                return alreadyResponded;

            if (accept.Eta.GetValueOrDefault() == 0)
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "The ETA field must be passed." })
                };
            }

            if (!await cr.UpdateStatus(CallRequestStatus.Accepting, WebGlobal.CurrentUser.Id, eta: accept.Eta))
            {
                return new HttpResponseMessage(Forbidden())
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "This request has already been responded to - your request has been ignored." })
                };
            }

            accept.OwnerUserId = WebGlobal.CurrentUser.Id;
            accept.FullName = WebGlobal.CurrentUser.FullName;
            accept.PhoneNumber = WebGlobal.CurrentUser.Company.Phone;

            if (accept.Id == 0)
                accept.Id = id;

            await DigitalDispatchService.NotifyCallUpdateEventAsync(cr.CompanyId,
                cr.AccountId,
                accept.ToJson(null),
                DigitalDispatchService.CallUpdateType.Accept,
                ownerUserId: WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new JsonContent(new CallRequestAcceptRejectResponse()
                {
                    Message = "Your request to accept the call has been received."
                })
            };
        }

        /// <summary>
        /// Notify the provider that requested the call that you rejected the call.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="reject"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{id}/reject")]
        public async Task<HttpResponseMessage> Reject(int id, CallRequestRejectRequestBody reject)
        {
            var cr = CallRequest.GetById(id);
            await ThrowIfNoCompanyAccessAsync(cr?.CompanyId, "dispatch offer");

            var alreadyResponded = ReturnIfAlreadyResponded(cr, "Reject Offer");
            if (alreadyResponded != null)
                return alreadyResponded;

            if (WebGlobal.CurrentUser.CompanyId == 7565)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "Your company doesn't have access to Reject Dispatches. It's status will remain at:" + cr.Status.ToString() })
                };
            }

            cr.ResponseReasonId = reject.MasterAccountReasonId;
            await cr.UpdateStatus(CallRequestStatus.Rejecting, WebGlobal.CurrentUser.Id);

            reject.OwnerUserId = WebGlobal.CurrentUser.Id;
            reject.FullName = WebGlobal.CurrentUser.FullName;
            reject.PhoneNumber = WebGlobal.CurrentUser.Company.Phone;
            if (reject.Id == 0)
                reject.Id = id;

            await DigitalDispatchService.NotifyCallUpdateEventAsync(cr.CompanyId, 
                cr.AccountId, 
                reject.ToJson(null), 
                DigitalDispatchService.CallUpdateType.Reject,
                ownerUserId: WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "Your request has been successfully marked as rejected." })
            };
        }


        private static HttpResponseMessage ReturnIfAlreadyResponded(CallRequest cr, string action)
        {
            if (cr.Status == CallRequestStatus.PhoneCallRequested)
            {
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "This request has already been responded to with a Phone Call Request. No action has been taken." })
                };
            }
            else if (cr.Status == CallRequestStatus.RequestingPhoneCall)
            {
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "This dispatch offer has already been responded to with a Phone Call Request and is currently being processed." })
                };
            }
            else if (cr.Status == CallRequestStatus.Accepting || cr.Status == CallRequestStatus.AcceptSent)
            {
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = $"This dispatch offer has already been responded to with an Accept and is currently being processed." })
                };
            }
            else if (cr.Status == CallRequestStatus.Accepted)
            {
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "This dispatch offer has already been marked as accepted." })
                };
            }
            else if (cr.Status == CallRequestStatus.Rejected)
            {
                return new HttpResponseMessage(Forbidden())
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "This dispatch offer has already been rejected." })
                };
            }
            else if (cr.Status != CallRequestStatus.None)
            {
                return new HttpResponseMessage(Forbidden())
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "This request has already been responded to, it's status is set to:" + cr.Status.ToString() })
                };
            }

            return null;
        }

        /// <summary>
        /// Requests a phone call for the specified call.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="phoneCall"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{id}/requestphonecall")]
        public async Task<HttpResponseMessage> RequestPhoneCall(int id, CallRequestRequestPhoneCallBody phoneCall)
        {
            var cr = CallRequest.GetById(id);

            await ThrowIfNoCompanyAccessAsync(cr?.CompanyId, "dispatch offer");

            var alreadyResponded = ReturnIfAlreadyResponded(cr, "Request Phone Call");
            if (alreadyResponded != null)
                return alreadyResponded;

            await cr.UpdateStatus(CallRequestStatus.RequestingPhoneCall, WebGlobal.CurrentUser.Id);

            if (phoneCall == null)
            {
                return new HttpResponseMessage(Forbidden())
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "Missing JSON object. Did you forget include the JSON body in the request?" })
                };
            }

            phoneCall.OwnerUserId = WebGlobal.CurrentUser.Id;
            phoneCall.FullName = WebGlobal.CurrentUser.FullName;
            phoneCall.PhoneNumber = WebGlobal.CurrentUser.Company.Phone;

            if (phoneCall.Id == 0)
                phoneCall.Id = id;

            if (string.IsNullOrWhiteSpace(phoneCall.PhoneNumber))
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "The phone number for your company is missing, thus a phone call request cannot be sent." })
                };
            }

            await DigitalDispatchService.NotifyCallUpdateEventAsync(
                cr.CompanyId,
                cr.AccountId,
                phoneCall.ToJson(null),
                DigitalDispatchService.CallUpdateType.PhoneCall,
                ownerUserId: WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "Your phone call request has been received." })
            };
        }

        [HttpPost]
        [Route("{id}/acceptmissedrequest")]
        public async Task<HttpResponseMessage> AcceptMissedRequest(int id, CallRequestAcceptMissedBody accept)
        {
            var cr = CallRequest.GetById(id);

            await ThrowIfNoCompanyAccessAsync(cr?.CompanyId, "dispatch offer");

            var alreadyResponded = ReturnIfAlreadyResponded(cr, "Accept Missed Offer");
            if (alreadyResponded != null)
                return alreadyResponded;

            if (string.IsNullOrWhiteSpace(accept.PurchaseOrderNumber))
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "You must specify a PO # to manually accept a call." })
                };
            }

            accept.OwnerUserId = WebGlobal.CurrentUser.Id;

            if (accept.Id == 0)
                accept.Id = id;

            await DigitalDispatchService.NotifyCallUpdateEventAsync(
                cr.CompanyId, 
                cr.AccountId, 
                accept.ToJson(null), 
                DigitalDispatchService.CallUpdateType.PhoneCallManuallyAccepted,
                ownerUserId: WebGlobal.CurrentUser.Id);

            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new JsonContent(new CallRequestAcceptRejectResponse() { Message = "Your request to accept the missed request has been received." })
            };
        }

        /// <summary>
        /// Notify the server that you have received the call request. 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{id}/acknowledge")]
        public void Acknowledge(int id)
        {
            // Use this to acknowledge that the offer was received  by a device/client.
            return;
        }
    }
}
