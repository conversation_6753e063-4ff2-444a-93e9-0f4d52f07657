using System;
using System.Net;
using System.Net.Http;
using Extric.Towbook.Accounts;
using Extric.Towbook.WebShared;
using static Extric.Towbook.API.ApiUtility;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{
    [Route("accountpublicrequest")]
    public class AccountPublicRequestController : ControllerBase
    {
        [HttpGet]
        [Route("{id}")]
        public async Task<object> Get(int id)
        {
            return await AccountPublicRequest.GetByIdAsync(id);
        }
    }

    [Route("accountpublicrequestlink")]
    public class AccountPublicRequestLinkController : ControllerBase
    {
        [HttpGet]
        [Route("{id}")]
        public async Task<object> Get(int id)
        {
            if (id < 1)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Invalid accountId specified")
                });
            }

            var aprl = await AccountPublicRequestLink.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(aprl?.CompanyId);

            return aprl;
        }

        [HttpGet]
        [Route("")]
        public async Task<object> Get([FromQuery]int? accountId, [FromQuery]int? companyId=null)
        {
            if(accountId == null || accountId == -1)
                return null;
           
            var account = await Account.GetByIdAsync(accountId.Value);

            int cId = companyId != null ? companyId.Value : account != null ? account.CompanyId : WebGlobal.CurrentUser.CompanyId;

            await ThrowIfNoCompanyAccessAsync(account?.CompanyId);

            return await AccountPublicRequestLink.GetByAccountIdAsync(accountId.Value, cId);
        }

        /// <summary>
        /// Removes the ability to use an external link to request a call for a certain account.
        /// </summary>
        /// <param name="id">This is an accountId for the company.</param>
        /// <returns>AccountPublicRequestLink object</returns>
        [HttpDelete]
        [Route("{id}")]
        public async Task <object> Delete(int id)
        {
            if (id > 0)
            {
                var a = await AccountPublicRequestLink.GetByIdAsync(id);
                if (a != null)
                {
                    if (await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId))
                    {
                        a.Delete();
                    }
                    else
                    {
                        return new HttpResponseMessage(System.Net.HttpStatusCode.Forbidden);
                    }
                }
            }

            return new HttpResponseMessage(System.Net.HttpStatusCode.NoContent);
        }

        /// <summary>
        /// Inserts (and enables) an account to have an external web link for requesting calls.
        /// </summary>
        /// <param name="id">This is an accountId for the company.</param>
        /// <returns>AccountPublicRequestLink object</returns>
        [HttpPost]
        [Route("{id}")]
        public async Task<object> Post(int id)
        {
            if (id < 1)
                throw new TowbookException("Post requires the account Id");

            var account = await Account.GetByIdAsync(id);
            await ThrowIfNoCompanyAccessAsync(account?.CompanyId);

            var a = await AccountPublicRequestLink.GetByAccountIdAsync(id, account.CompanyId);
            if(a == null)
            {
                a = new AccountPublicRequestLink();
                a.AccountId = id;
                a.CompanyId = account.CompanyId;
                a.OwnerUserId = WebGlobal.CurrentUser.Id;
                a.CreateDate = DateTime.Now;
            }

            a.Deleted = false;
            a.Save();

            return a;
        }
    }
}
