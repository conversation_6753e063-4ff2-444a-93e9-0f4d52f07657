using Agero;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models.MotorClubs;
using Extric.Towbook.Integrations.MotorClubs.Allstate;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Extric.Towbook.Dispatch;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System;
using System.Threading.Tasks;
using System.Collections.ObjectModel;
using Extric.Towbook.Integrations.MotorClubs.Issc;
using Extric.Towbook.Integrations.MotorClubs.Trx;
using Extric.Towbook.Integrations.MotorClubs.StackThree;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Web;
using Extric.Towbook.Integrations.MotorClubs.Honk;
using NLog;

namespace Extric.Towbook.API.Controllers
{
    [Route("DigitalDispatch")]
    public class DigitalDispatchController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Get the status of all master accounts
        /// </summary>
        /// <returns></returns>
        public static async Task<IEnumerable<DigitalStatusModel>> StatusAsync()
        {
            var acc = (await Account.GetByCompanyAsync(await WebGlobal.GetCompaniesAsync(), false,
                AccountType.MotorClub)).Where(o => o.MasterAccountId > 0);

            var l = new List<DigitalStatusModel>();

            foreach (var a in acc)
            {
                l.AddRange(await MapAccountToDigitalStatusAsync(a));
            }

            return l;
        }

        /// <summary>
        /// Get the status of a single master account
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<IEnumerable<DigitalStatusModel>> StatusAsync(int id)
        {
            var ac = await Account.GetByIdAsync(id);
            if (ac == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(ac.CompanyId))
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified account either doesn't exist or you don't have access to it.")
                });
            }

            if (ac.MasterAccountId == 0)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified account does not have a MasterAccountID set.")
                });
            }

            return await MapAccountToDigitalStatusAsync(ac);
        }

        /// <summary>
        /// Log in to all master accounts
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("LoginAll")]
        public async Task<object> LoginAll()
        {
            if (WebGlobal.CurrentUser != null &&
                WebGlobal.CurrentUser.CompanyId == 20005 &&
                WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager)
                return null;

            var acc = (await Account.GetByCompanyAsync(await WebGlobal.GetCompaniesAsync(), false,
                AccountType.MotorClub))
                .Where(o => o.MasterAccountId > 0 && 
                o.MasterAccountId != MasterAccountTypes.Agero &&
                o.MasterAccountId != MasterAccountTypes.Swoop &&
                o.MasterAccountId != MasterAccountTypes.Urgently && 
                o.MasterAccountId != MasterAccountTypes.OonUrgently &&
                o.MasterAccountId != MasterAccountTypes.OonSwoop &&
                o.MasterAccountId != MasterAccountTypes.OonQuest &&
                o.MasterAccountId != MasterAccountTypes.OonAllstate &&
                o.MasterAccountId != MasterAccountTypes.OonAgero);

            var list = new List<object>();

            foreach (var a in acc)
            {
                list.Add(new
                {
                    accountId = a.Id,
                    jobId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.Login(a.CompanyId, a.Id, WebGlobal.CurrentUser.Id)
                });
            }

            return list;
        }

        /// <summary>
        /// Log out of all master accounts
        /// </summary>
        /// <returns></returns>
        [HttpPost("LogoutAll")]
        public async Task<object> LogoutAllAsync()
        {

            if (WebGlobal.CurrentUser != null &&
                WebGlobal.CurrentUser.CompanyId == 20005 &&
                WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager)
                return null;

            var acc = (await Account.GetByCompanyAsync(await WebGlobal.GetCompaniesAsync(), false,
                AccountType.MotorClub)).Where(o => o.MasterAccountId > 0);

            var list = new List<object>();

            foreach (var a in acc)
            {
                list.Add(new
                {
                    accountId = a.Id,
                    jobId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.Logoff(a.CompanyId, a.Id, WebGlobal.CurrentUser.Id)
                });
            }

            return list;
        }

        /// <summary>
        /// Logs the company in to the specified accountId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("Login")]
        public async Task<object> Login(int id)
        {
            var a = await Account.GetByIdAsync(id);

            if (a == null || (WebGlobal.CurrentUser != null &&
                !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId)))
                return null;

            var jobId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.Login(a.CompanyId, a.Id, WebGlobal.CurrentUser?.Id ?? 1);

            return new { JobId = jobId };
        }

        /// <summary>
        /// Logs the company out of the specified accountId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("Logout")]
        public async Task<object> Logout(int id)
        {
            var a =await Account.GetByIdAsync(id);

            if (a == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(a.CompanyId))
                return null;

            var jobId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.Logoff(a.CompanyId, a.Id, WebGlobal.CurrentUser.Id);

            return new { JobId = jobId };
        }

        /// <summary>
        /// Get a list of all digital dispatch call requests
        /// </summary>
        /// <returns></returns>
        [HttpGet("CallRequests")]
        public async Task<object> CallRequests(
            int? accountId = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? page = 1,
            int? pageSize = 100,
            bool extended = false)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                return Array.Empty<DigitalDispatchCallRequest>();

            List<int> companies = new List<int>() { WebGlobal.CurrentUser.CompanyId };

            if (WebGlobal.CurrentUser.CompanyId == 26130 ||
                WebGlobal.CurrentUser.CompanyId == 36649)
                companies = (await WebGlobal.GetCompaniesAsync()).Select(o => o.Id).ToList();

            if (extended)
            {
                var count = DigitalDispatchCallRequest.CountDigitalByCompanyId(companies.ToArray(), accountId, startDate, endDate);
                Response.Headers["X-Records-Count"] = count.ToString();
            }

            return DigitalDispatchCallRequest.GetDigitalByCompanyId(companies.ToArray(), page, pageSize, accountId, startDate, endDate).Select(o => Map(o));
        }



        public sealed class EmailModel
        {
            public int RecId { get; set; }
            public int FileId { get; set; }
            public int CompanyId { get; set; }
            public string Description { get; set; }
            public int CallId { get; set; }
            public int CallNumber { get; set; }
            public string Client { get; set; }
            public string MotorClub { get; set; }
            public string PoNumber { get; set; }
            public string Source { get; set; }
            public int StatusId { get; set; }
            public DateTime CreateDate { get; set; }
        }

        /// <summary>
        /// Get a list of all emails / faxes that became call requests
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Emails")]
        public async Task<IEnumerable<EmailModel>> Emails(
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? page = 1,
            int? pageSize = 100,
            bool extended = false)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser || WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                return null;

            var sql = @"SELECT RecId = ROW_NUMBER() OVER(ORDER BY FileId DESC), * FROM
    (
        select
            q.FileId,
            q.Description,
            c.name as Client,
            a.Company as MotorClub,
            d.CallNumber,
            d.DispatchEntryId,
            q.CompanyId,
            StatusId = coalesce(mq.StatusId, 5),
            q.CreateDate,
            d.PurchaseOrderNumber as PONumber,
            Source = case when mq.TypeId is null or mq.TypeId = 3 or q.StorageKey = 'ImportedJob_Email.eml' then 'Email' 
                          when mq.TypeId = 2 then 'AAA'
                          when mq.TypeId = 1 then 'Fax'
                          else 'Other' end
        from Files q WITH (nolock)
            inner join companies c  WITH (nolock) on c.companyid = q.companyid
            left join MotorClubProcessingQueue mq  WITH (nolock) on mq.FileId = q.FileId
            left join dispatchentryfiles df  WITH (nolock) on q.fileid = df.FileId
            left join dispatchentries d  WITH (nolock) on d.DispatchEntryId = df.DispatchEntryId
            left join accounts a  WITH (nolock) on a.accountid = d.accountid
        where q.StorageKey like 'ImportedJob_Email%' OR mq.FileId IS NOT NULL
    ) v1";

            if (extended)
            {
                var b = new SqlBuilder();
                var s = b.AddTemplate(@"SELECT COUNT(*) FROM (" + sql + ") rx /**where**/ /**orderby**/");
                b.Where("CompanyId = @CompanyId", new { CompanyId = WebGlobal.CurrentUser.CompanyId });

                if (startDate.HasValue)
                    b.Where("CreateDate >= CAST(@StartDate AS DATE)", new { StartDate = startDate.Value });

                if (endDate.HasValue)
                    b.Where("CreateDate < DATEADD(dd, 1, CAST(@EndDate AS DATE))", new { EndDate = endDate.Value });

                var res = SqlMapper.Query<int>(s.RawSql, s.Parameters).ToList();

                Response.Headers["X-Records-Count"] = res[0].ToString();
            }

            var builder = new SqlBuilder(true);
            var selector = builder.AddTemplate(sql + " /**where**/ /**orderby**/");
            builder.Where("CompanyId = @CompanyId", new { CompanyId = WebGlobal.CurrentUser.CompanyId });

            if (startDate.HasValue)
                builder.Where("CreateDate >= CAST(@StartDate AS DATE)", new { StartDate = startDate.Value });

            if (endDate.HasValue)
                builder.Where("CreateDate < DATEADD(dd, 1, CAST(@EndDate AS DATE))", new { EndDate = endDate.Value });

            builder.OrderBy("RecID ASC", null, pageSize.GetValueOrDefault(100) * (page.GetValueOrDefault(1) - 1), pageSize.GetValueOrDefault(100));
            //return selector.RawSql;

            using var conn = Core.GetConnection();
            return (await Dapper.SqlMapper.QueryAsync<EmailModel>(conn, selector.RawSql, selector.DapperParameters)).ToList();
        }

        public sealed class DigitalAccountModel
        {
            public int InternalId { get; set; }
            public int CompanyId { get; set; }
            public string Name { get; set; }
            public string ContractorId { get; set; }
            public int AccountId { get; set; }
            public string AccountName { get; set; }
            public int MasterAccountId { get; set; }
            public string MasterAccountName { get; set; }
            public string LocationId { get; set; }
            public int LoginStatus { get; set; }
            public string LoginStatusName { get; set; }
            public DateTime LastLoginDate { get; set; }
            public string Recid { get; set; }
            public string ErrorMessage { get; set; }
        }

        [HttpGet("DigitalConnections")]
        public async Task<IEnumerable<DigitalAccountModel>> DigitalConnectionsAsync()
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                return Array.Empty<DigitalAccountModel>();

            var companyIds = (await this.GetCompaniesForRequestAsync()).Select(a => a.Id).ToArray();

            var connections = await SqlMapper.QuerySpAsync<DigitalAccountModel>(@"dbo.internalDigitalAccountsGetByCompanyId", new
            {
                CompanyId = string.Join(",", companyIds)
            });

            return connections;
        }


        public sealed class MoveConnectionModel
        {
            public int MasterAccountId { get; set; }
            public int SourceCompanyId { get; set; }
            public int DestinationCompanyId { get; set; }
            public string ContractorId { get; set; }
            /// <summary>
            /// Optional
            /// </summary>
            public string LocationId { get; set; }
            
            // If not set, it will try to find the account automatically or create one.
            public int? ForceAccountId { get; set; }
        }

        [HttpPost("MoveConnections")]
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<object> MoveConnections(MoveConnectionModel model)
        {
            if (model == null)
                throw new TowbookException("You must pass a non-null model.");

            if (model.SourceCompanyId == 0 || model.DestinationCompanyId == 0)
                throw new TowbookException("Source and Destination companyId's need to be set.");

            if (string.IsNullOrWhiteSpace(model.ContractorId))
                throw new TowbookException("ContractorId must be specified");

            var output = await MoveConnections(model.SourceCompanyId, 
                model.DestinationCompanyId,
                model.ContractorId, 
                model.LocationId, 
                model.ForceAccountId);

            return output;
        }

        public static async Task<string[]> MoveConnections(
            int sourceCompanyId, int destinationCompanyId, string contractorId = null, string locationId = null, int? forceAccountId = null)
        {
            List<string> output = new List<string>();
            var xmlContractors = AllstateContractor.GetByCompanyId(sourceCompanyId);
            var isscContractors = IsscProvider.GetByCompanyId(sourceCompanyId);
            var ageroSessions = AgeroSession.GetByCompanyId(sourceCompanyId);
            var trxContractors = TrxContractor.GetByCompanyId(sourceCompanyId);
            var honkContractors = HonkProvider.GetByCompanyId(sourceCompanyId);
            var stsContractors = await StackThreeContractor.GetByCompanyId(sourceCompanyId);
            var fleetnetProviders = Integrations.MotorClubs.Fleetnet.FleetnetProvider.GetByCompanyId(sourceCompanyId);
            var sykesProviders = Integrations.MotorClubs.Sykes.SykesContractor.GetByCompanyId(sourceCompanyId);

            var toMove = new List<IDigitalContractor>();
            var toMoveAsync = new List<IDigitalContractorAsync>();

            if (contractorId != null)
            {
                xmlContractors = xmlContractors.Where(o => o.ContractorId == contractorId).ToCollection();
                isscContractors = isscContractors.Where(o => o.ContractorId == contractorId).ToCollection();
                ageroSessions = ageroSessions.Where(o => o.VendorId.ToString() == contractorId).ToCollection();
                fleetnetProviders = fleetnetProviders.Where(o => o.ProviderId == contractorId || o.ContractorId == contractorId).ToCollection();
                sykesProviders = sykesProviders.Where(o => o.ContractorId == contractorId).ToCollection();
                stsContractors = stsContractors.Where(o => o.ContractorId == contractorId).ToCollection();
                trxContractors = trxContractors.Where(o => o.ContractorId == contractorId).ToCollection();
                honkContractors = honkContractors.Where(o => o.ProviderId == contractorId).ToCollection();
            }

            if (locationId != null)
            {
                stsContractors = Array.Empty<StackThreeContractor>();
                trxContractors = Array.Empty<TrxContractor>();
                ageroSessions = Array.Empty<AgeroSession>();
                xmlContractors = new Collection<AllstateContractor>();
                isscContractors = isscContractors.Where(o => o.LocationId == locationId).ToCollection();

                // contractorId is like VAB1234, providerId is numeric like 1001234
                fleetnetProviders = fleetnetProviders.Where(o => o.ContractorId == locationId).ToCollection();
            }

            toMove.AddRange(xmlContractors);
            toMove.AddRange(isscContractors);
            toMove.AddRange(ageroSessions);
            toMove.AddRange(fleetnetProviders);
            toMove.AddRange(sykesProviders);
            toMove.AddRange(trxContractors);
            toMove.AddRange(stsContractors);

            toMoveAsync.AddRange(honkContractors);

            var destAccounts = await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(destinationCompanyId));

            async Task<int> CopyAccount(int accountId, int newCompanyId)
            {
                var original = await Account.GetByIdAsync(accountId);

                var existing = destAccounts.FirstOrDefault(o => o.Company == original.Company);
                if (existing != null)
                {
                    return existing.Id;
                }

                output.Add("need to create new account... names dont match");

                var dest = new Account();

                dest.CompanyId = newCompanyId;
                dest.Type = original.Type;
                dest.Company = original.Company;
                dest.MasterAccountId = original.MasterAccountId;
                dest.Billable = original.Billable;
                dest.Phone = original.Phone;
                dest.Email = original.Email;
                dest.Status = AccountStatus.Active;

                await dest.Save();

                return dest.Id;
            }

            foreach (var move in toMove)
            {
               
                output.Add("Moving " + move.ToJson());

                var newAccountId = forceAccountId.GetValueOrDefault() > 0 ? forceAccountId :
                    (await CopyAccount(move.AccountId, destinationCompanyId));
                move.CompanyId = destinationCompanyId;
                move.Save();

                output.Add("Moved to " + move.CompanyId);
            }
            foreach (var move in toMoveAsync)
            {
                output.Add("Moving " + move.ToJson());

                var newAccountId = forceAccountId.GetValueOrDefault() > 0 ? forceAccountId :
                    (await CopyAccount(move.AccountId, destinationCompanyId));
                move.CompanyId = destinationCompanyId;
                await move.Save();

                output.Add("Moved to " + move.CompanyId);
            }

            return output.ToArray();
        }

        private static DigitalDispatchCallRequest Map(DigitalDispatchCallRequest o)
        {
            if (o == null)
                return null;

            o.StatusName = o.Status.ToString().FromPascalCase();

            if (o.OwnerUserId == 1 && o.StatusName == "Accepted")
                o.StatusName = "Auto Accepted";
            else if (o.OwnerUserId == 1 && o.StatusName == "Rejected")
                o.StatusName = "Auto Rejected";
            if (o.Status == CallRequestStatus.AnotherProviderResponded)
                o.StatusName = "Another Provider Responded";

            return o;
        }

        private static async Task<IEnumerable<DigitalStatusModel>> MapAccountToDigitalStatusAsync(Account a)
        {
            var l = new List<DigitalStatusModel>();

            switch (a.MasterAccountId)
            {
                case MasterAccountTypes.Agero:
                    var ag = AgeroSession.GetByAccountId(a.Id);

                    if (ag == null)
                        break;

                    var agm = new DigitalStatusModel();

                    agm.Id = a.Id;
                    agm.Name = a.Company;
                    agm.MasterName = "Agero";
                    agm.MasterAccountId = a.MasterAccountId;
                    agm.ContractorId = ag.VendorId.ToString();
                    agm.LoggedIn = ag.SignedIn;

                    l.Add(agm);
                    break;

                case MasterAccountTypes.Allstate:
                case MasterAccountTypes.Quest:
                case MasterAccountTypes.Nsd:
                case MasterAccountTypes.Nac:
                    foreach (var ac in AllstateContractor.GetByAccountId(a.Id))
                    {
                        var ddm = new DigitalStatusModel();

                        ddm.Id = a.Id;
                        ddm.Name = a.Company;
                        ddm.MasterName = (await MasterAccount.GetByIdAsync(a.MasterAccountId)).Name;
                        ddm.MasterAccountId = a.MasterAccountId;
                        ddm.ContractorId = ac.ContractorId;
                        ddm.LoggedIn = ac.LoginStatus == AllstateContractorLoginStatus.LoggedIn;

                        if (a.MasterAccountId == MasterAccountTypes.Quest ||
                            a.MasterAccountId == MasterAccountTypes.Nac)
                        {
                            ddm.LoggedIn = true;
                            ddm.AllowLoginLogout = false;
                        }

                        l.Add(ddm);
                    }
                    break;

                case MasterAccountTypes.AlliedDispatch:

                    foreach (var ac in TrxContractor.GetByAccountId(a.Id))
                    {
                        var gm = new DigitalStatusModel();

                        gm.Id = a.Id;
                        gm.Name = a.Company;
                        gm.MasterName = (await MasterAccount.GetByIdAsync(a.MasterAccountId)).Name;
                        gm.ContractorId = ac.ContractorId;
                        gm.LoggedIn = ac.LoginStatus == TrxLoginStatus.LoggedIn;
                        gm.MasterAccountId = a.MasterAccountId;

                        l.Add(gm);
                    }
                    break;

                case MasterAccountTypes.RoadsideProtect:
                case MasterAccountTypes.Tesla:
                case MasterAccountTypes.Usac:
                case MasterAccountTypes.Geico:
                    foreach (var ac in Integrations.MotorClubs.Issc.IsscProvider.GetByAccountId(a.Id))
                    {
                        var gm = new DigitalStatusModel();

                        gm.Id = a.Id;
                        gm.Name = a.Company;
                        gm.MasterName = (await MasterAccount.GetByIdAsync(a.MasterAccountId)).Name;
                        gm.ContractorId = ac.ContractorId;
                        gm.LocationId = ac.LocationId;
                        gm.LoggedIn = ac.LoginStatus == Integrations.MotorClubs.Issc.IsscProviderLoginStatus.LoggedIn;
                        gm.MasterAccountId = a.MasterAccountId;

                        l.Add(gm);
                    }
                    break;

            }

            return l;
        }
    }
}
