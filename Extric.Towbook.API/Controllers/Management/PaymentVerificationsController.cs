using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.API.Management.Models;
using Extric.Towbook.Management.Payments;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Management.Controllers
{
    [Route("management")]
    public class PaymentVerificationsController : ControllerBase
    {
        //routes.MapHttpRoute(
        //    name: "PaymentVerifications",
        //    routeTemplate: "management/{userId}/paymentVerifications/{id}",
        //    defaults: new { controller = "PaymentVerifications", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Management.Controllers" } } };
        [HttpGet]
        [Route("{userId}/paymentVerifications")]
        public object Get(int userId)
        {
            return Get(userId, 0);
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public object Get(int userId, int id)
        {
            if (id != 0)
                return PaymentVerificationModel.Map(PaymentVerification.GetById(id));

            return PaymentVerification.GetByUserId(id).Select(o => PaymentVerificationModel.Map(o));
        }

        //routes.MapHttpRoute(
        //    name: "PaymentVerifications",
        //    routeTemplate: "management/{userId}/paymentVerifications/{id}",
        //    defaults: new { controller = "PaymentVerifications", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Management.Controllers" } } };
        [HttpPost]
        [Route("{userId}/paymentVerifications")]
        public async Task<object> Post(PaymentVerificationModel model, [FromQuery] bool unverify = false)
        {
            if (unverify)
            {
                var o = await Unverify(model);
                return o;
            }

            var p = PaymentVerificationModel.Map(model);

            var payments = await Extric.Towbook.Dispatch.InvoicePayment.GetByIdsAsync(p.PaymentIds);
            ThrowIfNotFound(payments, "Payments Not Found");

            var invoices = await Invoice.GetByIdsAsync(payments.Select(s => s.InvoiceId).ToArray());
            ThrowIfNotFound(invoices, "Payments Not Found");

            // Verify user has access to verify payment
            foreach (var i in invoices)
                await ThrowIfNoCompanyAccessAsync(i?.CompanyId);

            // save payment verification
            p.IPAddress = this.GetRequestingIp();
            p.Status = PaymentStatus.Verified;
            p.UserId = WebGlobal.CurrentUser.Id;
            p.CreateDate = DateTime.Now;
            p.CompanyId = WebGlobal.CurrentUser.CompanyId;
            p.Save();

            // update each entry payment with new payment verification id
            foreach (var payment in payments)
            {
                payment.PaymentVerificationId = p.Id;

                await payment.MarkPaymentVerified(
                    invoices.FirstOrDefault(f => f.Id == payment.InvoiceId),
                    WebGlobal.CurrentUser,
                    this.GetRequestingIp());
            }

            return new
            {
                PaymentIds = payments.Select(s => s.Id).ToArray(),
                VerifiedUserId = WebGlobal.CurrentUser.Id,
                VerifiedUser = WebGlobal.CurrentUser.FullName,
                VerifiedDate = Core.OffsetDateTime(WebGlobal.CurrentUser.Company, p.CreateDate)
            };
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        internal async Task<object> Unverify(PaymentVerificationModel model)
        {
            var payments = await Extric.Towbook.Dispatch.InvoicePayment.GetByIdsAsync(model.PaymentIds);
            ThrowIfNotFound(payments, "Payments Not Found");

            var invoices = await Invoice.GetByIdsAsync(payments.Select(s => s.InvoiceId).ToArray());
            ThrowIfNotFound(invoices, "Payments Not Found");

            // Verify user has access to verify payment
            foreach (var i in invoices)
                await ThrowIfNoCompanyAccessAsync(i?.CompanyId);

            // verify all payments are verified
            if (payments.Any(w => w.PaymentVerificationId == null))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("One (or more) of the payments has not been marked as verified.  The unverify action can not continue.") });

            foreach (var item in payments)
            {
                var p = PaymentVerification.GetById(item.PaymentVerificationId.Value);

                if (!p.Deleted)
                    p.Delete(WebGlobal.CurrentUser);

                await item.MarkPaymentUnVerified(invoices.FirstOrDefault(f => f.Id == item.InvoiceId), WebGlobal.CurrentUser, this.GetRequestingIp());
            }

            return new
            {
                PaymentIds = payments.Select(s => s.Id).ToArray()
            };
        }

        //routes.MapHttpRoute(
        //    name: "PaymentVerifications",
        //    routeTemplate: "management/{userId}/paymentVerifications/{id}",
        //    defaults: new { controller = "PaymentVerifications", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Management.Controllers" } } };
        [HttpDelete]
        [Route("{userId}/paymentVerifications/{id}")]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            //TODO Async
            var p = PaymentVerification.GetById(id);
            if (p != null)
            {
                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(p.CompanyId))
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to that companies data.") });

                p.Delete(WebGlobal.CurrentUser);
            }

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }
    }
}
