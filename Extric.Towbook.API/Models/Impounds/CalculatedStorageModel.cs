using Extric.Towbook.Accounts;
using Extric.Towbook.Impounds;
using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Utility;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Models
{
    public class CalculatedStorageBillableModel
    {
        public decimal DaysHeldBillable { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public CalculatedStorageBillableInvoiceModel Invoice { get; set; }
    }

    public class CalculatedStorageBillableInvoiceModel
    {
        public decimal Subtotal { get; set; }
        public decimal Tax { get; set; }
        public decimal Total { get; set; }
        public decimal BalanceDue { get; set; }
        public IEnumerable<CallInvoiceItemModel> InvoiceItems { get; set; }
    }

    public class CalculatedStorageModel
    {
        public int CompanyId { get; set; }
        public int? AccountId { get; set; }
        public int RateItemId { get; set; }
        public int BodyTypeId { get; set; }
        public Decimal InvoiceStorageTotal { get; set; }
        public Decimal DaysHeldBillable { get; set; }
        public DateTime? ImpoundDate { get; set; }
        public DateTime? ReleaseDate { get; set; }
        public DateTime? NextScheduledRecalculate { get; set; }
        public StorageRateModel StorageRate { get; set; }
        public List<dynamic> TieredItems { get; set; }
        public decimal AfterHoursReleaseFee { get; set; }
        public decimal CustomPrice { get; set; }

        public IEnumerable<CallInvoiceItemModel> StorageInvoiceItems { get; set; }

        public static async Task<CalculatedStorageModel> CalculateStorageAsync(RateItem rateItem,
            Vehicle.BodyType bodyType,
            DateTime impoundDate,
            DateTime releaseDate,
            decimal? daysHeld = null,
            int? accountId = null,
            int? companyId = null,
            decimal? customPrice = null)
        {
            if (rateItem == null)
                return null;

            if (rateItem.CategoryId != 5)
                return null;

            if (rateItem.Predefined != null && !new int[] { PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE, PredefinedRateItem.BUILTIN_STORAGE_DAILYRATE, PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE }.Contains(rateItem.Predefined.Id))
                return null;

            var impound = new Impound();
            var entry = new Dispatch.Entry();

            // Setup entry properties
            entry.Impound = true;
            entry.AccountId = accountId.HasValue ? accountId.Value : 0;

            // Setup impound properties
            var company = await Towbook.Company.Company.GetByIdAsync(companyId.HasValue ? companyId.Value : rateItem.CompanyId);
            impound.Company = company;
            impound.ImpoundDate = Core.OffsetDateTime(impound.Company, impoundDate);
            impound.ReleaseDate = Core.OffsetDateTime(impound.Company, releaseDate);
            impound.DispatchEntry = entry;

            // Setup invoice
            var invoice = new Invoice();
            invoice.DispatchEntry = entry;
            invoice.CompanyId = companyId.HasValue ? companyId.Value : rateItem.CompanyId;
            invoice.Impound = impound;
            invoice.NextScheduledRecalculate = releaseDate.AddHours(-1);
            impound.Invoice = invoice;


            IRateItem iri = rateItem;

            // get storage rate
            var sr = new Company.StorageRate();
            sr = null;
            
            // check Account first
            if (accountId.GetValueOrDefault() > 1)
            {
                // AC: 2/19/2020 - Changed to Entry.Account.CompanyId (not companyId.Value) to make sure that the storage rate is found 
                // with the possible child company scenario where accounts are shared but live under certain child companies.
                sr = await Company.StorageRate.GetByAccountIdAsync(entry?.Account?.CompanyId ?? (companyId.HasValue ? companyId.Value : rateItem.CompanyId), accountId.Value);
                
                // consider account rate item pricing
                var ari = await Towbook.Accounts.RateItem.GetByRateItemAsync(entry.Account, rateItem);
                if (ari != null)
                    iri = ari;
            }
            
            // get default company storage if null
            if (sr == null)
                sr = await Company.StorageRate.GetByCompanyIdAsync(companyId.HasValue ? companyId.Value : rateItem.CompanyId);

            // One more time, check if the default storage rate is null and default to the rateItem's companyId. 
            // Note: this fixes an issue when requesting storage information for a sister company in a multicompany setup and 
            //       the sister company doesn't have a default company storage rate.  In this case, we should rely on the rateItems companyId.
            if (sr == null)
                sr = await Company.StorageRate.GetByCompanyIdAsync(rateItem.CompanyId);

            // otherwise, use the company's default storage rate
            if (sr == null)
                sr = company.StorageRate;

            // Setup and add one storage invoice item
            var invoiceItem = new InvoiceItem();
            
            invoiceItem.RateItem = iri;
            invoiceItem.Quantity = daysHeld.HasValue ? daysHeld.Value : impound.GetDaysHeldBillable(sr, impoundDate, releaseDate, rateItem);
            invoiceItem.CustomPrice = customPrice.HasValue ? customPrice : Towbook.Accounts.RateItem.GetTransforemdCost(entry.Account, iri, bodyType);

            #region Hourly Storage Calculation
            if (company.HasFeature(Generated.Features.Impounds_HourlyStorage) && accountId.HasValue)
            {
                var ri = rateItem;
                if (rateItem?.Predefined?.Id != PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
                    ri = (RateItem)RateItem.GetPredefinedByCompany(company).FirstOrDefault(f => f.Predefined?.Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE);

                var tsi = Impound.CalculateHourlyStorageItem(entry.Account, ri, impoundDate, releaseDate, bodyType);
                if(tsi != null)
                {
                    // not null, we have an to swap to an hourly storage item
                    invoiceItem.Quantity = tsi.Quantity;
                    invoiceItem.CustomPrice = tsi.Price;
                    invoiceItem.RateItem = tsi.RateItem; // force to hourly rate item
                }
                else
                {
                    // Not within the hourly storage item...check if we need to change to default storage item
                    if (rateItem.Predefined?.Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE && entry.Account != null)
                    {
                        IRateItem r = await RateItem.GetByIdAsync(entry.Account.DefaultStorageRateItemId.GetValueOrDefault());

                        Towbook.Accounts.RateItem ari = await Towbook.Accounts.RateItem.GetByRateItemAsync(entry.Account, r);
                        if (ari != null)
                            r = ari;

                        var defaultRateItem = r ?? entry.Account.StorageRate;
                        if (defaultRateItem != null)
                        {
                            invoiceItem.RateItem = defaultRateItem;
                            invoiceItem.CustomPrice = Towbook.Accounts.RateItem.GetTransforemdCost(entry.Account, defaultRateItem, bodyType);
                            invoiceItem.Quantity = daysHeld.HasValue ? daysHeld.Value : impound.GetDaysHeldBillable(sr, impoundDate, releaseDate, defaultRateItem);
                        }
                    }
                }
            }
            #endregion

            impound.InvoiceItems.Add(invoiceItem);

            bool isTiered = rateItem.IsTieredStorageItem();
            List<dynamic> tieredItems = new List<dynamic>();
            decimal summedTiered = 0.0M;

            // get tier information ready
            if (isTiered)
            {
                Impound.GenerateTieredItems(invoice, impound.InvoiceItems.Where(w => w.RateItem?.RateItemId == rateItem.RateItemId).First(), bodyType);

                // get parent invoiceItem
                var relatedInvoiceId = impound.InvoiceItems.Where(w => w.RateItem?.RateItemId == rateItem.RateItemId).Select(s => s.Id).First();

                // sum tier totals
                summedTiered = impound.InvoiceItems.Where(w => w.RelatedInvoiceItemId == relatedInvoiceId).Sum(x => x.Total);

                // prepare an array of tiers with each tier having a name, quantity, price and total
                tieredItems.AddRange(impound.InvoiceItems.Where(w => w.RelatedInvoiceItemId == relatedInvoiceId).OrderBy(o => o.RateItem.RateItemId).Select(ii => new
                {
                    ii.Name,
                    ii.Quantity,
                    ii.Price,
                    ii.Total
                }));
            }
            else
            {
                impound.Invoice.UpdateStorageQuantities();
            }

            decimal ahrf = 0.0M;
            if (company.HasFeature(Generated.Features.Impounds_AfterHoursReleaseFee))
            {
                Account acc = null;
                
                if(accountId.GetValueOrDefault() > 0)
                    acc = await Account.GetByIdAsync(accountId.Value);
                
                ahrf = AfterHoursReleaseFeeModel.GetAfterHourReleaseFeeIfAny(company, impound.ReleaseDate.Value, acc, bodyType);
                if(ahrf > 0)
                {
                    var ri = RateItem.GetPredefinedByCompany(company, acc)
                        .Where(w => w.Predefined != null && w.Predefined.Id == PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE)
                        .FirstOrDefault();

                    var ahii = new InvoiceItem();

                    ahii.RateItem = ri;
                    ahii.Quantity = 1;
                    ahii.CustomPrice = ri.Cost;
                    ahii.CustomName = ri.Name;

                    impound.InvoiceItems.Add(ahii);
                }
            }

            // ready to return now...
            return new CalculatedStorageModel()
            {
                InvoiceStorageTotal = isTiered ? summedTiered : impound.InvoiceStorageTotal,
                DaysHeldBillable = daysHeld.HasValue ? daysHeld.Value : invoiceItem.Quantity,
                CustomPrice = invoiceItem.CustomPrice.GetValueOrDefault(),
                ImpoundDate = impound.ImpoundDate,
                ReleaseDate = impound.ReleaseDate,
                NextScheduledRecalculate = impound.Invoice.NextScheduledRecalculate,
                StorageRate = StorageRateModel.MapDomainObjectToModel(sr),
                AccountId = accountId,
                CompanyId = companyId.HasValue ? companyId.Value : rateItem.CompanyId,
                RateItemId = invoiceItem.RateItem?.RateItemId ?? rateItem.Id,
                BodyTypeId = bodyType.Id,
                TieredItems = isTiered ? tieredItems : null,
                AfterHoursReleaseFee = ahrf,
                StorageInvoiceItems = CallInvoiceItemModel.MapDomainObjectListToModel(impound.InvoiceItems)
            };
        }
    }
}
