using System;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.Azure.Cosmos;
using SkiaSharp;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Web;
using Microsoft.Extensions.Primitives;
using Extric.Towbook.WebShared.Multipart;
using HttpContext = Extric.Towbook.Web.HttpContext;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Net.Http.Headers;
using Microsoft.AspNetCore.Http;
using Extric.Towbook.WebShared.Net5;

namespace Extric.Towbook.API
{
    public static class ApiUtility
    {
        public static HttpStatusCode Forbidden()
        {
            // iOS thinks Forbidden is 401... so we have to return 500 instead.
            if (HttpContext.Current.IsAppleDevice())
                return HttpStatusCode.InternalServerError;
            else
                return HttpStatusCode.Forbidden;
        }

        public static void ThrowIfPropertyMissing(string name)
        {
            throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
            {
                Content = new StringContent($"Couldn't process request because the {name} property wasn't specified.")
            });
        }
        public static void ThrowIfRequestBodyMissing()
        {
            throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
            {
                Content = new StringContent($"Couldn't process request because you didn't send a JSON request body in your request.")
            });
        }

        /// <summary>
        /// Async variation. MORE PERFORMANT.
        /// Checks if the object is null, and if so, returns a 404.
        /// Secondly, it checks if the current user has access to the companyId specified.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="type">What the object should be referred to as, example: call</param>
        public static async Task ThrowIfNoCompanyAccessAsync(int? companyId, string type = "object")
        {
            type ??= "object";

            if (companyId == null)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The {type.ToLowerInvariant()} you requested cannot be found.")
                });
            }

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(companyId.Value))
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent($"Your user account doesn't have access to this {type}.")
                });
            }
        }
        public static async Task ThrowIfNoCompanyAccessAsync(int[] companyIds, string type = "object")
        {
            type ??= "object";

            if (companyIds == null)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The {type.ToLowerInvariant()} you requested doesn't exist.")
                });
            }

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(companyIds))
            {
                throw new HttpResponseException(new HttpResponseMessage(Forbidden())
                {
                    Content = new StringContent($"Your user account doesn't have access to this {type} due to a companyId restriction.")
                });
            }
        }

        public static void ThrowIfNotFound(object o, string type = "object")
        {
            if (o == null)
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The {type} you tried to access cannot be found.")
                });
        }

        public static void ThrowIfFeatureNotAssigned(Generated.Features f)
        {
            if (!WebGlobal.CurrentUser.Company.HasFeature(f))
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"Your company doesn't have the {f.ToString()} feature included in its current service plan.")
                });
        }

        public static async Task ThrowIfFeatureNotAssignedAsync(Generated.Features f)
        {
            if (!(await WebGlobal.CurrentUser.Company.HasFeatureAsync(f)))
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"Your company doesn't have the {f.ToString()} feature included in its current service plan.")
                });
        }

        public static void ThrowAccessDenied(string type = "object")
        {
            throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
            {
                Content = new StringContent($"The {type} you tried to access cannot be accessed.")
            });
        }

        public static HttpResponseMessage NoContent()
        {
            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        public static User CurrentUser
        {
            get
            {
                return WebGlobal.CurrentUser;
            }
        }

        /// <summary>
        /// Gets the latitude included in the body for this HTTP request. (android/iphone)
        /// </summary>
        /// <returns></returns>
        public static decimal RecentLatitude()
        {
            decimal recentLat = 0;
            if (HttpContext.Current == null)
                return 0;

            if (!StringValues.IsNullOrEmpty(HttpContext.Current.Request.Headers["X-Api-My-Latitude"]))
            {
                if (!decimal.TryParse(HttpContext.Current.Request.Headers["X-Api-My-Latitude"], out recentLat))
                    recentLat = 0;
            }
            return recentLat;
        }

        /// <summary>
        /// Gets the latitude included in the body for this HTTP request. (android/iphone)
        /// </summary>
        /// <returns></returns>
        public static decimal RecentLongitude()
        {
            decimal recentLong = 0;

            if (HttpContext.Current == null)
                return 0;

            if (!StringValues.IsNullOrEmpty(HttpContext.Current.Request.Headers["X-Api-My-Longitude"]))
            {
                if (!decimal.TryParse(HttpContext.Current.Request.Headers["X-Api-My-Longitude"], out recentLong))
                    recentLong = 0;
            }

            return recentLong;
        }


        public static async Task<HttpResponseMessage> HttpImageResponse(string location, string contentType)
        {
            var location2 = await Storage.FileUtility.GetFileAsync(location);

            if (location2 == null)
                throw new Exception($"Invalid file path: ${location}.");

            var result = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StreamContent(
                    new FileStream(location2, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
            };
            result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(contentType);

            return result;
        }

        public static async Task<List<FileInfo>> GetFilesFromHttpPostRequest(
            Microsoft.AspNetCore.Http.HttpRequest request)
        {
            var filesInfo = new List<FileInfo>();

            FormOptions _defaultFormOptions = new FormOptions();
            var boundary = MultipartRequestHelper.GetBoundary(
                MediaTypeHeaderValue.Parse(request.ContentType),
                _defaultFormOptions.MultipartBoundaryLengthLimit);
            var reader = new MultipartReader(boundary, HttpContext.Current.Request.Body);

            var section = await reader.ReadNextSectionAsync();

            while (section != null)
            {
                var hasContentDispositionHeader =
                    ContentDispositionHeaderValue.TryParse(
                        section.ContentDisposition, out var contentDisposition);

                if (hasContentDispositionHeader)
                {
                    if (MultipartRequestHelper.HasFileContentDisposition(contentDisposition))
                    {
                        var targetFilePath = Path.GetTempFileName();
                        using (var targetStream = System.IO.File.Create(targetFilePath))
                        {
                            await section.Body.CopyToAsync(targetStream);
                            filesInfo.Add(new FileInfo(targetFilePath));
                            Console.WriteLine($"Copied the uploaded file '{targetFilePath}'");
                        }
                    }
                }
                section = await reader.ReadNextSectionAsync();
            }

            return filesInfo;
        }

        public static async Task<List<FileInfo>> GetFilesFromHttpPostRequest(
            HttpRequestMessage request)
        {
            var provider = new MultipartFormDataStreamProvider(Path.GetTempPath());
            var task = await request.Content.ReadAsMultipartAsync(provider);
            var files = new List<FileInfo>();

            foreach (var file in provider.FileData)
            {
                var tempFile = file.LocalFileName;
                files.Add(new FileInfo(file.LocalFileName));
            }

            return files;
        }

        public static async Task CreateaPhotoFile(string sourceFilename, string newFile)
        {
            var xpath = Path.GetDirectoryName(newFile);

            if (!Directory.Exists(xpath))
                Directory.CreateDirectory(xpath);

            using (var img = SKBitmap.Decode(sourceFilename))
            using (var result = img.ResizeProportionately(1920))
                result.Save(newFile, SKEncodedImageFormat.Jpeg);

            await Storage.FileUtility.SendFileAsync(newFile);
        }

        public static async Task<MemoryStream> GeneratePdf(
            string html, HttpRequest request = null, bool noMarginBottom = false)
        {
            var footer = "<div style=\"color: #333333; font-family: verdana; font-size: 9px; font-weight: bold\"><br /><br />" +
                "Created with Towbook Management Software | www.towbook.com " +
                "<div style=\"display:inline-block; float:right; font-weight: normal;\">Printed " + DateTime.Now.ToShortDateString() + "</div></div>";

            if (request == null)
            {
                request = HttpContext.Current.Request;
            }

            var baseUrl = request.EnvironmentSpecificRequestUrl().Host + "/";

            return await PdfClient.GeneratePdf(
                html, baseUrl, FileType.PDF, footer, noMarginBottom: noMarginBottom);
        }

        public static async Task<MemoryStream> GeneratePdf(
            string html, string footer, FileType type = FileType.PDF)
        {
            return await PdfClient.GeneratePdf(html, null, type, footer);
        }
        public static void ThrowIfMessagingTokenIsMissing(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Messaging token is missing.")
                });
            }
        }

        public static Dictionary<string, string> GetTwilioCreadentials()
        {
            return new Dictionary<string, string> {
                { "apiKey", Core.GetAppSetting("Twilio:ApiKey") },
                { "apiSecret", Core.GetAppSetting("Twilio:ApiSecret") },
                { "accountSid", Core.GetAppSetting("Twilio:AccountSid") },
                { "authToken", Core.GetAppSetting("Twilio:AuthToken") },
                { "serviceSid", Core.GetAppSetting("Twilio:ServiceSid") },
                { "pushCredentialSid", Core.GetAppSetting("Twilio:PushCredentialSid") }
            };
        }

        public static async Task<Collection<T>> ExecuteQuery<T>(Container container, QueryDefinition sqlQuery, PartitionKey? pk)
        {
            Collection<T> result = new Collection<T>();
            string continuationToken = null;
            double charge = 0;

            const int limit = 200;

            int runs = 0;
            int innerLoop = 0;

            do
            {
                var feedIterator = container.GetItemQueryIterator<T>(sqlQuery,
                    continuationToken, new QueryRequestOptions() { PartitionKey = pk });

                while (feedIterator.HasMoreResults)
                {
                    innerLoop++;

                    if (innerLoop > limit)
                        throw new Exception($"CRITICAL: Exceeded Inner Limit of {limit}. " +
                            $"Total RU Cost: {charge}, Cancelling query. Result Length: {result.Count}, Query Text:{sqlQuery.QueryText}");

                    FeedResponse<T> feedResponse = await feedIterator.ReadNextAsync();

                    charge += feedResponse.RequestCharge;
                    continuationToken = feedResponse.ContinuationToken;

                    foreach (T item in feedResponse)
                    {
                        result.Add(item);
                    }
                }

                runs++;

                if (runs > limit)
                    throw new Exception($"CRITICAL: Exceeded Run Limit of {limit}. Cancelling query. Total RU Cost: {charge}, Result Length: " + result.Count + ", Query Text:" + sqlQuery);

            } while (continuationToken != null);

            //if (HttpContext.Current != null)
            //    HttpContext.Current.Response.Headers.Add("X-Towbook-RU", charge.ToString() + "RU");

            return result;
        }

        public static Dictionary<string, string> GetCookiesAsDictionary(HttpRequest request)
        {
            var cookies = request.Cookies;

            var cookiesDictionary = new Dictionary<string, string>();

            foreach (var (Key, Value) in cookies)
            {
                cookiesDictionary[Key] = Value;
            }

            return cookiesDictionary;
        }
    }
}
