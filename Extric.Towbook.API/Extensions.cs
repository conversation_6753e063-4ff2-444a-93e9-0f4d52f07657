using System;
using System.Collections.Specialized;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Reflection;
using System.Text;
using Extric.Towbook.Web;
using Extric.Towbook.Platform;
using Extric.Towbook.WebShared;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration;
using Extric.Towbook.Dispatch;
using System.Collections.ObjectModel;
using Extric.Towbook.Utility;
using System.Text.RegularExpressions;
using System.Net;
using System.Net.Http;
using Extric.Towbook.Generated;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Company;
using Extric.Towbook.Impounds;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Extric.Towbook.API
{
    public static class Extensions
    {
        private static User CurrentUser => (User)HttpContextFactory.Instance.CurrentUser;

        public static bool IsAppleDevice(this Microsoft.AspNetCore.Http.HttpContext obj)
        {
            if (obj?.Request?.UserAgent() == null)
                return false;

            var agent = obj.Request.UserAgent().ToLowerInvariant();

            return MobileUserAgentHelper.IsAppleUserAgent(agent);
        }

        public static bool IsAndroidDevice(this Microsoft.AspNetCore.Http.HttpContext obj)
        {
            if (obj?.Request?.UserAgent() != null)
            {
                var agent = obj.Request.UserAgent().ToLower();

                return MobileUserAgentHelper.IsAndroidUserAgent(agent);
            }
            else
            {
                return false;
            }
        }

        public static async Task<bool> ShouldSendDriverEventsAsync(this Account acc)
        {
            if (acc == null)
                return false;
            if (acc.MasterAccountId != 0)
            {
                var ma = await MasterAccount.GetByIdAsync(acc.MasterAccountId);
                return (ma != null && ma.SendDriverEvents);
            }

            return false;
        }

        public static async Task<bool> ShouldSendDispatchSignatureEventsAsync(this Account acc)
        {
            if (acc == null)
                return false;
            if (acc.MasterAccountId != 0)
            {
                var ma = await MasterAccount.GetByIdAsync(acc.MasterAccountId);
                return (ma != null && ma.SendDispatchSignatureEvents);
            }

            return false;
        }

        public static T? GetValueOrNull<T>(this NameValueCollection dr, string columnName) where T : struct
        {
            T retValue = default(T);
            string obj = null;

            try
            {
                obj = dr[columnName];
            }
            catch
            {
                obj = null;
            }

            if (obj == "null")
                obj = null;

            if (string.IsNullOrWhiteSpace(obj))
                return new Nullable<T>();

            retValue = (T)Convert.ChangeType(obj, typeof(T), CultureInfo.InvariantCulture);

            return retValue;
        }

        public static int[] GetIntValues(this NameValueCollection collection, string name, int? includeId = null, bool? allowZero = null)
        {
            var ret = new Collection<int>();

            // validation of inputs
            if (collection == null || string.IsNullOrEmpty(collection[name]))
            {
                if (includeId.GetValueOrDefault() > 0)
                    return new int[] { includeId.Value }.ToArray();
                else
                    return ret.ToArray();
            }

            string val = collection[name];

            if (includeId.GetValueOrDefault() > 0 && !ret.Contains(includeId.Value))
                ret.Add(includeId.Value);

            int temp;
            // consider comma seperated values
            if (val.Contains(","))
            {
                foreach (var id in val.Split(','))
                {
                    if (int.TryParse(id, out temp) && !ret.Contains(temp))
                        ret.Add(temp);
                }
            }
            else
            {
                // attempt one only
                if (int.TryParse(val, out temp) && !ret.Contains(temp))
                {
                    if (temp > 0)
                        ret.Add(temp);
                    else if (temp == 0 && allowZero.GetValueOrDefault())
                        ret.Add(temp);
                }
            }

            return ret.ToArray();
        }

        public static AuthenticationToken GetCurrentToken(this ControllerBase obj)
        {
            if (HttpContext.Current?.Items["ServiceGlobal.CurrentUserToken"] != null)
            {
                return AuthenticationToken.GetByToken(HttpContext.Current.Items["ServiceGlobal.CurrentUserToken"].ToString());
            }
            else
            {
                if (WebGlobal.CurrentUser != null)
                {
                    return new AuthenticationToken()
                    {
                        ClientVersionId = ClientVersion.GetByGitHash("web-app").Id,
                        UserId = WebGlobal.CurrentUser.Id
                    };
                }
            }

            return null;
        }


        public static async Task<AuthenticationToken> GetCurrentTokenAsync(this ControllerBase obj)
        {
            if (HttpContext.Current?.Items["ServiceGlobal.CurrentUserToken"] != null)
            {
                return await AuthenticationToken.GetByTokenAsync(HttpContext.Current.Items["ServiceGlobal.CurrentUserToken"].ToString());
            }
            else
            {
                if (WebGlobal.CurrentUser != null)
                {
                    return new AuthenticationToken()
                    {
                        ClientVersionId = ClientVersion.GetByGitHash("web-app").Id,
                        UserId = WebGlobal.CurrentUser.Id
                    };
                }
            }

            return null;
        }



        public static bool IsMobileAppRequest(this Microsoft.AspNetCore.Http.HttpContext obj) =>
            IsAppleDevice(obj) || IsAndroidDevice(obj);

        public static string GetRequestingIp(this ControllerBase obj)
        {
            return WebGlobal.GetRequestingIp();
        }

        public static async Task<AuthenticationToken> GetCurrentToken()
        {
            if (HttpContext.Current == null)
                return null;

            if (HttpContext.Current.Items["ServiceGlobal.CurrentUserToken"] != null)
            {
                return await AuthenticationToken.GetByTokenAsync(HttpContext.Current.Items["ServiceGlobal.CurrentUserToken"].ToString());
            }
            else
            {
                if (WebGlobal.CurrentUser != null)
                {
                    return new AuthenticationToken()
                    {
                        ClientVersionId = ClientVersion.GetByGitHash("web-app", ClientVersionType.WebApp).Id,
                        UserId = WebGlobal.CurrentUser.Id,
                    };
                }
            }

            return null;
        }

        public static async Task<Company.Company[]> GetCompaniesForRequestAsync(this ControllerBase obj) =>
            await GetCompaniesForRequestAsync(obj, false);

        public static string GetRequestHeaderValueOrNull(this HttpContext obj, string name)
        {
            if (string.IsNullOrEmpty(name))
                return null;

            var request = HttpContext.Current.Request;

            if (request == null ||
                request.Headers == null ||
                request.Headers.ContainsKey(name))
                return null;

            return request.Headers[name].FirstOrDefault();
        }

        public static string GetRequestHeaderValueOrNull(this Microsoft.AspNetCore.Http.HttpContext obj, string name)
        {
            if (string.IsNullOrEmpty(name))
                return null;

            var request = HttpContext.Current.Request;

            if (request == null ||
                request.Headers == null ||
                request.Headers.ContainsKey(name))
                return null;

            return request.Headers[name].FirstOrDefault();
        }

        public static async Task<Company.Company[]> GetCompaniesForRequestAsync(this ControllerBase obj, bool returnAllByDefault)
        {
            try
            {
                string company = "";

                var request = HttpContext.Current.Request;
                if (request != null &&
                    request.Headers != null &&
                    request.Headers.ContainsKey("X-Company"))
                {
                    company = request.Headers["X-Company"].FirstOrDefault();
                }
                else if (HttpContext.Current?.Items["TBRequestCompany"] != null)
                {
                    company = HttpContext.Current?.Items["TBRequestCompany"] as string;
                }

                if (!string.IsNullOrWhiteSpace(company))
                {
                    if (company == "all")
                    {
                        return await WebGlobal.GetCompaniesAsync();
                    }
                    else
                    {
                        if (WebGlobal.CurrentUser.Type == User.TypeEnum.SystemAdministrator)
                            return new[] { await Company.Company.GetByIdAsync(Convert.ToInt32(company)) };

                        return new Company.Company[] {
                    (await WebGlobal.GetCompaniesAsync()).FirstOrDefault(o => o.Id == Convert.ToInt32(company))
                };
                    }
                }
                else
                {
                    if (returnAllByDefault)
                    { 
                        return await WebGlobal.GetCompaniesAsync();
                    }
                    else
                    {
                        return new Company.Company[] { WebGlobal.CurrentUser.Company };
                    }
                }
            }
            finally
            {
                // Empty finally block retained from original code
            }
        }

        /// <summary>
        /// Converts the MailMessage to a string to be saved as an .EML file.
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        public static string ToEmlFile(this MailMessage message)
        {
            var assembly = typeof(SmtpClient).Assembly;
            var mailWriterType = assembly.GetType("System.Net.Mail.MailWriter");

            using (var memoryStream = new MemoryStream())
            {
                // Get reflection info for MailWriter contructor
                var mailWriterContructor = mailWriterType.GetConstructor(BindingFlags.Instance | BindingFlags.NonPublic, null, new[] { typeof(Stream), typeof(bool) }, null);

                // Construct MailWriter object with our FileStream
                var mailWriter = mailWriterContructor.Invoke(new object[] { memoryStream, true });

                // Get reflection info for Send() method on MailMessage
                var sendMethod = typeof(MailMessage).GetMethod("Send", BindingFlags.Instance | BindingFlags.NonPublic);



                // Call method passing in MailWriter
                //sendMethod.Invoke(message, BindingFlags.Instance | BindingFlags.NonPublic, null, new[] { mailWriter, true, true }, null);

                // Call method passing in MailWriter
                var smparams = sendMethod.GetParameters().Length;


                sendMethod.Invoke(
                    message,
                    BindingFlags.Instance | BindingFlags.NonPublic,
                    null,
                    (smparams == 3 ?
                        new object[] { mailWriter, true, true } : // 2 params: for Windows 7 / <=.net 4.0 
                        new object[] { mailWriter, true }),   // 3 parameters, for Windows 8 / .Net 4.5 (?)
                    null);




                // Finally get reflection info for Close() method on our MailWriter
                var closeMethod = mailWriter.GetType().GetMethod("Close", BindingFlags.Instance | BindingFlags.NonPublic);

                // Call close method
                closeMethod.Invoke(mailWriter, BindingFlags.Instance | BindingFlags.NonPublic, null, new object[] { }, null);

                return Encoding.ASCII.GetString(memoryStream.ToArray());
            }
        }

        #region Permissions
        [Obsolete("Please use User.HasPermissionToEmailCall which accomodates differing settings in multi companies.")]
        public static bool HasPermissionToEmailCalls(this User u)
        {
            if (u.Type == User.TypeEnum.Driver)
            {
                var kv = CompanyKeyValue.GetByCompanyId(u.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromEmailingCalls").FirstOrDefault();
                if (kv != null && kv.Value == "1") return false;
            }
            return true;
        }

        public static bool HasPermissionToModifyCompletedCalls(this User u)
        {
            if (u.Type == User.TypeEnum.AccountUser)
                return false;

            if (u.Type == User.TypeEnum.Driver)
            {
                var kv = CompanyKeyValue.GetByCompanyId(u.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromModifyingAfterCompletion").FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            else if (u.Type == User.TypeEnum.Dispatcher)
            {
                var kv = CompanyKeyValue.GetByCompanyId(u.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromModifyingAfterCompletion").FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            else
                return true;
        }

        public static bool HasPermissionToCreateAdHocPricingItems(this User u)
        {
            var kv = CompanyKeyValue.GetByCompanyId(u.CompanyId, Provider.Towbook.ProviderId, "PreventAdHocInvoiceItems").FirstOrDefault();
            if (kv != null && kv.Value == "1")
                return false;
            else
                return true;
        }

        public static Collection<Entry> HasPermissionToViewCompletedCalls(this User u, Collection<Entry> entries)
        {
            string kv = null;

            if ((u.Notes ?? "").Contains("PreventFromViewingCompletedCalls=all"))
            {
                kv = "1";
            }
            else if ((u.Notes ?? "").Contains("PreventFromViewingCompletedCalls=24"))
            {
                kv = "24";
            }
            else if ((u.Notes ?? "").Contains("PreventFromViewingCompletedCalls=48"))
            {
                kv = "48";
            }
            else
            {
                if (u.Type == Towbook.User.TypeEnum.Driver)
                {
                    kv = CompanyKeyValue.GetFirstValueOrNull(u.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingCompletedCalls");
                }
                else if (u.Type == User.TypeEnum.Dispatcher)
                {
                    kv = CompanyKeyValue.GetFirstValueOrNull(u.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromViewingCompletedCalls");

                }
                else if (u.Type == User.TypeEnum.AccountUser)
                {
                    kv = CompanyKeyValue.GetFirstValueOrNull(u.CompanyId, Provider.Towbook.ProviderId, "PreventAccountUsersFromViewingCompletedCalls");
                }
            }

            if (kv != null)
            {
                if (kv == "1")
                {
                    return entries.Where(o => (o.Status.Id != Status.Completed.Id)).ToCollection();
                }
                else if (kv == "24")
                {
                    return entries.Where(o => (o.Status.Id != Status.Completed.Id || (o.Status.Id == Status.Completed.Id && o.CreateDate > DateTime.Now.AddDays(-1)))).ToCollection();
                }
                else if (kv == "48")
                {
                    return entries.Where(o => (o.Status.Id != Status.Completed.Id || (o.Status.Id == Status.Completed.Id && o.CreateDate > DateTime.Now.AddDays(-2)))).ToCollection();
                }
                else
                {
                    int hours = 0;
                    if (int.TryParse(kv, out hours))
                    {
                        return entries.Where(o => (o.Status.Id != Status.Completed.Id ||
                            (o.Status.Id == Status.Completed.Id &&
                            o.CreateDate > DateTime.Now.AddHours(-hours)))).ToCollection();
                    }

                }
            }


            return entries;
        }

        /// <summary>
        /// Determines if the current user has access to view cancelled calls, and if not, 
        /// returns a list with all cancelled calls removed.
        /// </summary>
        /// <param name="u"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        public static System.Collections.Generic.IEnumerable<CallModel> HasAccessToViewCancelledCalls(this User u, System.Collections.Generic.IEnumerable<CallModel> input)
        {
            var kv = CompanyKeyValue.GetFirstValueOrNull(u.CompanyId, Provider.Towbook.ProviderId, "PreventUserTypesFromViewingCancelledCalls");

            if (!String.IsNullOrWhiteSpace(kv))
            {
                kv = kv + ",";

                foreach (var cu in kv.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries))
                {
                    int typeId = 0;

                    if (int.TryParse(cu, out typeId))
                    {
                        if (typeId == (int)WebGlobal.CurrentUser.Type)
                        {
                            return input.Where(o => o.Status.Id != Status.Cancelled.Id).ToCollection();
                        }
                    }
                }
            }

            return input;
        }

        public static bool HasAccessToAuditCalls(this User u)
        {
            if (u == null)
                return false;

            if (!WebGlobal.CurrentUser.Company.HasFeature(Features.Dispatching_AuditCalls))
                return false;

            return u.HasAccessToLockCalls();
        }


        public static bool HasAccessToLockCalls(this User u)
        {
            if (u == null)
                return false;

            if (!WebGlobal.CurrentUser.Company.HasFeature(Features.Dispatching_LockCalls))
                return false;

            return u.HasAccessToLockCalls();
        }

        /// <summary>
        /// Determines if the current user has access to view cancelled calls, and if not, 
        /// returns a list with all cancelled calls removed.
        /// </summary>
        /// <param name="u"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        
        public static async Task<Collection<CallModel>> HasAccessToViewCancelledCallsAsync(this User u, Collection<CallModel> input)
        {
            var kv = await CompanyKeyValue.GetFirstValueOrNullAsync(u.CompanyId, Provider.Towbook.ProviderId, "PreventUserTypesFromViewingCancelledCalls");

            if (!String.IsNullOrWhiteSpace(kv))
            {
                kv = kv + ",";

                foreach (var cu in kv.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries))
                {
                    int typeId = 0;

                    if (int.TryParse(cu, out typeId))
                    {
                        if (typeId == (int)WebGlobal.CurrentUser.Type)
                        {
                            return input.Where(o => o.Status.Id != Status.Cancelled.Id).ToCollection();
                        }
                    }
                }
            }

            return input;
        }

       public static async Task<Collection<CallModel>> HasPermissionToViewCompletedCallsAsync(this User u, Collection<CallModel> entries,
            int limitToDaysOverride = 0)
        {
            if (u.Type == User.TypeEnum.Driver || u.Type == User.TypeEnum.Dispatcher)
            {
                string blockCompletedValue = await CompanyKeyValue.GetFirstValueOrNullAsync(u.CompanyId, Provider.Towbook.ProviderId,
                    u.Type == User.TypeEnum.Driver
                        ? "PreventDriversFromViewingCompletedCalls"
                        : "PreventDispatchersFromViewingCompletedCalls");

                if (blockCompletedValue == null) return entries;

                if (blockCompletedValue == "1")
                {
                    return entries.Where(entry => entry.Status.Id != Status.Completed.Id).ToCollection();
                }
                if (int.TryParse(blockCompletedValue, out int hours))
                {
                    if (limitToDaysOverride != 0)
                        hours = limitToDaysOverride * 24;
                    return entries.Where(entry => entry.Status.Id != Status.Completed.Id ||
                                                  (entry.Status.Id == Status.Completed.Id &&
                                                   entry.CreateDate > DateTime.Now.AddHours(-hours))).ToCollection();
                }
            }
            else if (u.IsAccountTypeUser())
            {
                string blockCompletedValue = await CompanyKeyValue.GetFirstValueOrNullAsync(WebGlobal.CurrentUser.CompanyId,
                        Provider.Towbook.ProviderId,
                        "PreventAccountUsersFromViewingCompletedCalls");
                if (blockCompletedValue == "1")
                {
                    return entries.Where(entry => entry.Status.Id != Status.Completed.Id).ToCollection();
                }
            }
            return entries;
        }

        public static Collection<CallModel> HasPermissionToViewImpounds(this User u, Collection<CallModel> entries)
        {
            if (u.Type != User.TypeEnum.Driver && !u.IsAccountTypeUser())
                return entries;

            var companies = CompanyUser.GetByUserId(WebGlobal.CurrentUser.Id);
            var callCompanies = entries.Select(c => c.CompanyId).Union(companies.Select(o => o.CompanyId)).Distinct();

            if (callCompanies.Count() > 1)
            {
                if (u.IsAccountTypeUser())
                {
                    var preventAccountUserKeys = callCompanies.Select(companyId =>
                        CompanyKeyValue.GetByCompanyId(companyId, Provider.Towbook.ProviderId, "PreventAccountUsersFromViewingImpounds").FirstOrDefault()
                        ).ToArray();

                    return entries.Where(c =>
                    {
                        if (c.Impound == false || c.Status.Id != 5) return true;
                        int callCompany = c.CompanyId;
                        var key = preventAccountUserKeys.FirstOrDefault(k => k != null && k.CompanyId == callCompany);
                        if (key != null && key.Value == "1") return true; // Allow all
                        if (key != null && key.Value == "2") return false; // Block All
                        else if (key == null || key.Value == "0") // Default only applies to private property accounts
                        {
                            var account = Account.GetById(u.AccountId);
                            if (account != null && account.Type == AccountType.PrivateProperty)
                            {
                                // don't show impounds for private properties.
                                return false;
                            }
                            return true;
                        }
                        return true; // Should never reach here

                    }).ToCollection();
                }

                var preventDriverKeys = callCompanies.Select(companyId =>
                    CompanyKeyValue.GetByCompanyId(companyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingImpounds")
                        .FirstOrDefault()
                    ).ToArray();

                if (preventDriverKeys.Any(k => k != null && k.Value == "1"))
                {
                    int? driverId = Driver.GetByUserId(WebGlobal.CurrentUser.Id).FirstOrDefault()?.Id;
                    return entries.Where(c =>
                    {
                        // Include call if it's not an impound or it's not completed
                        if (c.Impound == false || c.Status.Id != 5)
                            return true;

                        int callCompany = c.CompanyId;
                        var key = preventDriverKeys.FirstOrDefault(k => k != null && k.CompanyId == callCompany);

                        if (key == null || key.Value != "1")
                            return true;

                        // if it's an active impound and the driver is assigned to any of its assets include it
                        if (c.Assets != null && c.Assets.Any(a => a?.Driver?.Id == driverId))
                            return true;

                        return false;

                    }).ToCollection();
                }
            }
            else
            {
                if (u.IsAccountTypeUser())
                {
                    var accountUserCanAccess = u.AccountUserHasPermissionToViewImpounds();
                    return u.AccountUserHasPermissionToViewImpounds()
                        ? entries
                        // Include call if it's not an impound or it's not completed
                        : entries.Where(c => c.Impound == false || c.Status.Id != 5).ToCollection();
                }

                if (!u.HasPermissionToViewImpounds())
                {
                    var driverId = Driver.GetByUserId(WebGlobal.CurrentUser.Id).FirstOrDefault()?.Id;
                    // Remove any completed impounds
                    return entries.Where(c =>
                    {
                        // Include call if it's not an impound or it's not completed
                        if (c.Impound == false || c.Status.Id != 5)
                            return true;

                        // if it's an active impound and the driver is assigned to any of its assets include it
                        if (c.Assets != null && c.Assets.Any(a => a?.Driver?.Id == driverId)) return true;
                        return false;
                    }).ToCollection();
                }
            }
            return entries;
        }

        public static bool HasPermissionToModifyCompletedCallsWithPayments(this User u)
        {
            if (u.Type == User.TypeEnum.Driver)
            {
                var kv = CompanyKeyValue.GetByCompanyId(u.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromModifyingCallAfterPayment").FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            else if (u.Type == User.TypeEnum.Dispatcher)
            {
                var kv = CompanyKeyValue.GetByCompanyId(u.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromModifyingCallAfterPayment").FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            else
                return true;
        }

        public static bool HasPermissionToCreateNewCalls(this User u)
        {
            if (u.Type == User.TypeEnum.Driver)
            {
                var kv = CompanyKeyValue.GetByCompanyId(u.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromCreatingCalls").FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            return true;
        }

        //PROPAGATE ASYNC METHOD
        public static async Task<bool> HasPermissionToCreateNewCallsAsync(this User u)
        {
            if (u.Type == User.TypeEnum.Driver)
            {
                var kv = (await CompanyKeyValue.GetByCompanyIdAsync(u.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromCreatingCalls")).FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            return true;
        }

        public static bool HasPermissionToViewQuotes(this User u)
        {
            if (u.Type == User.TypeEnum.Driver)
            {
                var kv = CompanyKeyValue.GetByCompanyId(u.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingQuotes").FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            return true;
        }

        #endregion

        public static void ThrowIfCannotViewImpounds(this User user, Entry entry)
        {
            if (user.Type == User.TypeEnum.Driver && entry.Impound == true && entry.Status == Status.Completed)
            {
                var prevent = CompanyKeyValue.GetFirstValueOrNull(entry.Company.Id, Provider.Towbook.ProviderId, "PreventDriversFromViewingImpounds");
                if (prevent == "1")
                {
                    var responseException = new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("You don't have access to view impounds."),
                    });

                    throw responseException;
                }
            }
        }

        public static void ThrowIfCannotViewCompleted(this User user, Entry entry)
        {
            if (entry.Status != Entry.EntryStatus.Completed) return;
            if (user.Type == Extric.Towbook.User.TypeEnum.Driver)
            {
                string preventDriversKv = CompanyKeyValue.GetFirstValueOrNull(user.CompanyId, Provider.Towbook.ProviderId,
                     "PreventDriversFromViewingCompletedCalls");
                if (preventDriversKv == "1" ||
                    (preventDriversKv == "24" && entry.CompletionTime.HasValue && entry.CompletionTime <= DateTime.Now.AddHours(-24)) ||
                    (preventDriversKv == "48" && entry.CompletionTime.HasValue && entry.CompletionTime <= DateTime.Now.AddHours(-48))
                    )
                {
                    
                    var responseException = new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified call either doesn't exist or you don't have access to it."),
                    });

                    responseException.Response.Headers.Add("x-cache-hit", "1");
                    throw responseException;
                }
            }
            else if (user.Type == Extric.Towbook.User.TypeEnum.Dispatcher)
            {
                string preventDispatchersKv = CompanyKeyValue.GetFirstValueOrNull(user.CompanyId, Provider.Towbook.ProviderId,
                     "PreventDispatchersFromViewingCompletedCalls");
                if (preventDispatchersKv == "1" ||
                    (preventDispatchersKv == "24" && entry.CompletionTime.HasValue && entry.CompletionTime <= DateTime.Now.AddHours(-24)) ||
                    (preventDispatchersKv == "48" && entry.CompletionTime.HasValue && entry.CompletionTime <= DateTime.Now.AddHours(-48))
                    )
                {

                    var responseException = new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified call either doesn't exist or you don't have access to it."),
                    });

                    responseException.Response.Headers.Add("x-cache-hit", "1");
                    throw responseException;
                }
            }
        }

        #region Preferences

        public static async Task<bool> ShouldReceiveCallRequestsAsync(this User u)
        {
            if (new Towbook.User.TypeEnum[] {
                    Towbook.User.TypeEnum.AccountUser,
                    Towbook.User.TypeEnum.Driver }.Contains(WebGlobal.CurrentUser.Type))
                return false;

            if (Core.GetAppSetting("Towbook:CallRequests:Disable") == "1")
                return false;

            if ((WebGlobal.CurrentUser.Notes ?? "").Contains("DisableLiveDispatch"))
                return false;

            // only respect the DisableAcceptReject if user is using Android or iOS
            if (HttpContext.Current != null &&
                !HttpContext.Current.IsAndroidDevice() &&
                !HttpContext.Current.IsAppleDevice())
                return true;

            if (u.Type != User.TypeEnum.Driver)
            {
                var kv = (await UserKeyValue.GetByUserAsync(u.CompanyId, u.Id, Provider.Towbook.ProviderId, "DisableAcceptReject")).FirstOrDefault();
                if (kv != null && kv.Value == "1")
                    return false;
                else
                    return true;
            }
            else
            {
                // drivers cannot receive call requests under any circumstance so dont bother checking.
                return false;
            }
        }

        #endregion

        // String
        public static string FromPascalCase(this string str)
        {
            return Regex.Replace(str, "[a-z][A-Z]", m => m.Value[0] + " " + m.Value[1]);
        }

    }
}
