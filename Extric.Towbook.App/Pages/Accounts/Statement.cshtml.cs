using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.JSInterop;
using Microsoft.AspNetCore.Http.Extensions;
using NLog;
//using OfficeOpenXml;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Company;
using Extric.Towbook.Utility;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.Integration;
using Extric.Towbook.Impounds;
using Extric.Towbook;
using System.Web;
using Microsoft.AspNetCore.Mvc.RazorPages;

public partial class Accounts_StatementModel : PageModel
{
    [Inject] private IJSRuntime JSRuntime { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }

    public Extric.Towbook.Accounts.Statement Statement;
    public List<object> _charges;
    public Dictionary<int, decimal> _agedBalances;

    public bool McBilling { get; set; }

    public  Extric.Towbook.Company.Company Company;
    public bool HasTaxes { get; set; }
    public bool HasPO { get; set; }
    public bool HasVin { get; set; }
    public bool HasVehicle { get; set; }

    public bool ShowInvoiceItems { get; set; }
    public bool ShowVinAsLastEight { get; set; }
    public bool ShowReason { get; set; }
    public bool ShowToFrom { get; set; }
    public bool ShowDriver { get; set; }
    public bool ShowOdometer { get; set; }
    public bool ShowVin { get; set; }
    public bool ShowNotes { get; set; }
    public bool ShowInvoiceNumber { get; set; }
    public bool ShowCallNumber { get; set; }
    public bool IsSafeClear { get; set; }
    public bool ShowInvoiceLinks { get; set; }
    public bool ShowAccountContact { get; set; }

    public bool ShowPaymentLink { get; set; }

    public bool HideMileage { get; set; }

    public bool ShowLatest { get; set; }

    public bool ShowCompletionDate { get; set; }
    public bool ShowPlateNumber { get; set; }
    public bool ShowUnitNumber { get; set; }
    public bool ShowTruck { get; set; }
    public bool ShowBillingNotes { get; set; }

    public string TaxName { get; set; }

    private static readonly Logger logger = LogManager.GetCurrentClassLogger();

    // Element references for UI elements
    public string lblTaxes;
    public string lblSubtotal;
    public string lblOriginalDue;
    public string lblOriginalDue2;
    public string lblGrandTotal;
    public string lblCompanyName;
    public string lblAddress;
    public string lblPhone;
    public string lblFax;
    public string lblAccountName;
    public string lblAccountInfo;
    public List<dynamic> rpInvoiceItems;

    public string _(string html)
    {
        return HttpUtility.HtmlEncode(html);
    }

    private string BuildQuery()
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("?");
        
        var query = Request.Query;
        foreach (string key in query.Keys)
        {
            if (key.ToLowerInvariant() == "pdf")
                continue;

            if (key.ToLowerInvariant() == "jpg")
                continue;

            sb.Append(key.ToLowerInvariant());
            sb.Append("=");
            sb.Append(query[key]);
            sb.Append("&");
        }

        return sb.ToString().Trim('&');
    }

    private string GetCustomAttributeValueByType(string value, Extric.Towbook.AttributeType type)
    {
        switch (type)
        {
            case Extric.Towbook.AttributeType.Boolean:
                return value == "1" ? "Yes" : value == "0" ? "No" : string.Empty;
        }

        return value;
    }

    protected async Task LoadPageAsync()
    {
        bool noAuth = true;
        // default these as true
        ShowUnitNumber = true;
        ShowTruck = true;
        
        var query = Request.Query;
        
        if (query.ContainsKey("id"))
        {
            Statement = await Extric.Towbook.Accounts.Statement.GetByIdAsync(Convert.ToInt32(query["id"]));

            if(Statement == null)
            {
                return;
            }

            if (query.ContainsKey("key"))
            {
                // square page.  NEVER reveal this salt in client side code. 
                if (query["key"] == Extric.Towbook.Core.MD5(Statement.Id.ToString() + ":27ed2fb84d816"))
                    noAuth = false;
            }
            LogRequest("Statement page load with id");
            if (noAuth && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(Statement.Company.Id) && WebGlobal.CurrentUser.Company.Id != 1)
            {
                return;
            }
        }

        if (WebGlobal.CurrentUser != null &&
           await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.PaymentIntegrations_Square))
        {
            ShowPaymentLink = false;

            try
            {
                int statusCode = 0;
                var resp = WebGlobal.GetResponseFromUrl("/api/integration/square/authorization", out statusCode);

                if (statusCode == 200)
                {
                    ShowPaymentLink = true;

                    var excludeLink = Extric.Towbook.Integration.AccountKeyValue.GetByAccount(Statement.Company.Id, Statement.AccountId, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements").FirstOrDefault();
                    var excludeLinkDefault = Extric.Towbook.Integration.CompanyKeyValue.GetByCompanyId(Statement.Company.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements").FirstOrDefault();

                    if (excludeLink != null && excludeLink.Value == "1")
                        ShowPaymentLink = false;
                    else if (excludeLinkDefault != null && excludeLinkDefault.Value == "1")
                        ShowPaymentLink = false;
                }
            }
            catch (Exception)
            {
                ShowPaymentLink = false;
            }
        }

        string buildQuery = BuildQuery();
        TaxName = "Taxes";

        // pdf and xls params removed. they need to be re-written.

        Extric.Towbook.Accounts.StatementOption io = null;

        if (Statement != null)
        {
            io = Extric.Towbook.Accounts.StatementOption.GetByCompanyId(Statement.Company.Id);

            ShowInvoiceItems = io.ShowInvoiceItems;
            ShowReason = io.ShowReason;
            ShowVinAsLastEight = io.ShowVinAsLastEight;
            ShowVin = io.ShowVin;
            ShowInvoiceNumber = io.ShowInvoiceNumber;
            ShowToFrom = io.ShowToFrom;
            ShowDriver = io.ShowDriver;
            ShowOdometer = io.ShowOdometer;
            ShowLatest = io.ShowLatest;
            ShowCallNumber = io.ShowCallNumber;
            ShowInvoiceLinks = io.ShowInvoiceLinks;
            ShowAccountContact = io.ShowAccountContact;
            ShowCompletionDate = io.ShowCompletionDate;
            ShowPlateNumber = io.ShowPlateNumber;
            ShowUnitNumber = io.ShowUnitNumber;
            ShowTruck = io.ShowTruck;
            ShowBillingNotes = io.ShowBillingNotes;

            HideMileage = WebGlobal.CurrentUser != null && new int[] { 35210 }.Contains(WebGlobal.CurrentUser.PrimaryCompanyId);

            if (Statement.Account.MasterAccountId == Extric.Towbook.Accounts.MasterAccountTypes.SafeClear ||
                Statement.Account.Company.ToLowerInvariant().Contains("city of houston") ||
                Statement.Account.Company.ToLowerInvariant().Contains("harris county"))
            {
                IsSafeClear = true;
                ShowVinAsLastEight = true;
                ShowInvoiceLinks = false;
                ShowInvoiceItems = false;
                ShowOdometer = false;
                ShowCallNumber = false;
                ShowDriver = false;
                ShowReason = false;
                ShowToFrom = false;
            }

            if (Statement.Company.Id == 2116)
                Statement.DispatchEntries = Statement.DispatchEntries.Where(o => o.BalanceDue > 0).ToCollection();

            _charges = new List<object>();

            if (IsSafeClear)
            {
                var calls = (Statement.DispatchEntries.OrderBy(x => (
                    x.Attributes.ContainsKey(8605) ? Convert.ToInt32(x.Attributes[8605].Value) : 0)).ThenBy(x => (
                    x.Attributes.ContainsKey(5793) ? Convert.ToInt32(x.Attributes[5793].Value) : 0)).ToArray());

                decimal subtotal = 0;
                decimal subbalance = 0;
                int subcount = 0;
                int totalcount = 0;
                for (int i = 0; i < calls.Length; i++)
                {
                    _charges.Add(calls[i]);
                    subtotal += calls[i].InvoiceTotal;
                    subbalance += calls[i].BalanceDue;
                    subcount++;
                    totalcount++;

                    var segment = calls[i].Attributes.ContainsKey(8605) ? Convert.ToInt32(calls[i].Attributes[8605].Value) : 0;

                    if (calls.Length > (i + 1))
                    {
                        var segmentNext = calls[i + 1].Attributes.ContainsKey(8605) ? Convert.ToInt32(calls[i + 1].Attributes[8605].Value) : 0;

                        if (segmentNext != segment)
                        {
                            _charges.Add(new SummaryRow() { Total = subtotal, Balance = subbalance, Count = subcount });
                            subtotal = 0;
                            subbalance = 0;
                            subcount = 0;
                        }
                    }
                    else
                    {
                        _charges.Add(new SummaryRow() { Total = subtotal, Balance = subbalance, Count = subcount });
                    }
                }

                _charges.Add(new SummaryRow() { Total = 0, Balance = 0, Count = totalcount });
            }
            else
            {
                _charges.AddRange(Statement.DispatchEntries.OrderBy(x =>
                    (ShowCompletionDate && x.CompletionTime != null)
                        ? x.CompletionTime
                        : x.CreateDate)
                    .ToArray());
                //_charges.AddRange(Statement.Impounds.OrderBy(x => x.CreateDate).ToArray());
                //_charges.AddRange(Statement.Items.ToArray());
                _charges.AddRange(Statement.Invoices.ToArray());
            }


            if (Statement.AccountId == 99648)
            {
                ShowNotes = true;
            }

            await Statement.RecalculateSafeAsync();

            lblTaxes = Statement.Taxes.ToMoney();
            lblSubtotal = Statement.Subtotal.ToMoney();
            lblOriginalDue = Statement.Total.ToMoney();


            if (WebGlobal.CurrentUser != null && 
                (WebGlobal.CurrentUser.PrimaryCompanyId == 1376 || 
                WebGlobal.CurrentUser.PrimaryCompanyId == 2116 || 
                WebGlobal.CurrentUser.PrimaryCompanyId == 89219 || 
                WebGlobal.CurrentUser.PrimaryCompanyId == 30563))
            {
                var balances = await Statement.Account.GetAgedBalancesAsync(Statement.DispatchEntries.Select(o => o.CompanyId).Distinct().ToArray(),
                                               Extric.Towbook.Accounts.Account.AgedInvoice.ImpoundInclusion.All,
                                               Statement.StatementDate != null ? WebGlobal.OffsetDateTime(Statement.StatementDate.Value.AddHours(23).AddMinutes(59).AddSeconds(59), true) : DateTime.Now,
                                                Statement.DispatchEntries.Select(s => s.Invoice));

                if (balances != null)
                    _agedBalances = balances.AgedBalances;
            }

            if (query["mcb"] == "1")
            {
                McBilling = true;
            }

            if (McBilling)
            {
                var des = new DispatchEntryService();
                string output = "";
                foreach (var call in Statement.DispatchEntries)
                {
                    var x = new DispatchEntryQueueItem() { CompanyId = Statement.Company.Id, DispatchEntryId = call.Id, OwnerUserId = WebGlobal.CurrentUser != null ? WebGlobal.CurrentUser.Id : 1 };
                    try
                    {
                        await des.RequestInvoiceSubmission(x, call);
                    }
                    catch (Exception ze)
                    {
                        output += ze.ToString();
                    }
                    output += x.ToJson() + "\n";
                }

                throw new Exception("SENT!" + output);
            }

            HasTaxes = Statement.Taxes > 0;
            HasPO = Statement.DispatchEntries.Where(o => !String.IsNullOrWhiteSpace(o.PurchaseOrderNumber)).Any();
            HasVehicle = Statement.DispatchEntries.Where(o => (!String.IsNullOrWhiteSpace(o.VehicleMake) && o.VehicleMake != "(none)") || (!String.IsNullOrWhiteSpace(o.VehicleModel))).Any() || ShowInvoiceItems;
            HasVin = Statement.DispatchEntries.Where(o => !String.IsNullOrWhiteSpace(o.VIN)).Any();
            
            try
            {
                if (Statement.Balance != Statement.Total)
                {
                    lblGrandTotal = Statement.Balance.ToMoney();
                }
                else
                {
                    lblGrandTotal = "";
                }
            }
            catch (Exception y)
            {
                lblGrandTotal = y.Message;
            }

            #region Header: Company Details

            this.Company = Statement.Company;

            if (Statement.DispatchEntries.Count > 0 &&
            Statement.DispatchEntries[0].Attributes.ContainsKey(12) && WebGlobal.CurrentUser != null && WebGlobal.CurrentUser.PrimaryCompanyId != 1376 && WebGlobal.CurrentUser.PrimaryCompanyId != 8438)
            {
                var tmp = Extric.Towbook.Company.Company.GetById(Convert.ToInt32(Statement.DispatchEntries[0].Attributes[12].Value));
                if (tmp != null)
                    Company = tmp;
            }

            if (Company.Id == 11309 && WebGlobal.CurrentUser != null)
                Company = WebGlobal.CurrentUser.Company;

            // Batch company info updates
            var companyInfoUpdates = new Dictionary<string, string>();
            
            string companyNameText = _(Company.Name);
            
            if (Company.Id == 2116)
                companyNameText = "A Division of K.T.L. Enterprises Inc.";
            else if (Company.Id == 3311 || Company.Id == 7223)
                companyNameText = "Keller Logistics";
                
            companyInfoUpdates.Add("lblCompanyName", companyNameText);
            companyInfoUpdates.Add("lblAddress", _(Company.Address + ", " + Company.City + " " + Company.State + " " + Company.Zip));
            companyInfoUpdates.Add("lblPhone", "Phone: " + _(Company.Phone));
            
            // Update company info batch
            await JSRuntime.InvokeVoidAsync("updateElementTexts", companyInfoUpdates);

            var companyBillingAddress = AddressBookEntry.GetByCompany(Company).Where(o => o.Name == "Billing Address").FirstOrDefault();
            var companyBillingAddress2 = AddressBookEntry.GetByCompany(Company).Where(o => o.Name == "Statement Address").FirstOrDefault();

            if (companyBillingAddress2 != null) companyBillingAddress = companyBillingAddress2;

            if (companyBillingAddress != null)
            {
                await JSRuntime.InvokeVoidAsync("setText", "lblAddress", _(companyBillingAddress.Address + ", " +
                    companyBillingAddress.City + " " +
                    companyBillingAddress.State + " " +
                    companyBillingAddress.Zip));
            }

            string faxText = "";
            if (Company.Fax != null)
                faxText = "| Fax: " + _(Company.Fax);

            if (Company.Email != null && Company.Id != 1827)
                faxText += " | " + _(Company.Email);

            if (Company.Website != null)
                faxText += " | " + _(Company.Website);

            lblFax= faxText;

            #endregion

            // Account information
            if (ShowAccountContact)
            {
                lblAccountName =
                    (!string.IsNullOrWhiteSpace(Statement.Account.FullName) ? _(Statement.Account.FullName) + "<br />" : "") +
                    _(Statement.Account.Company);
            }
            else
            {
                lblAccountName = _(Statement.Account.Company);
            }

            var billingAddress = (await AddressBookEntry.GetByAccountIdAsync(Statement.Account.Id)).Where(o => o.Name == "Billing Address").FirstOrDefault();

            if (billingAddress != null)
            {
                lblAccountInfo = _(billingAddress.Address + "\n" +
                    billingAddress.City + " " +
                    billingAddress.State + " " +
                    billingAddress.Zip).Replace("\n", "<br />");
            }
            else
            {
                lblAccountInfo = _(
                    Statement.Account.Address + "\n" +
                    Statement.Account.City + " " +
                    Statement.Account.State + " " +
                    Statement.Account.Zip).Replace("\n", "<br />");

            }
        }
        LogRequest("Statement page load closed at outermost scope");
    }

    public class SummaryRow { public decimal Total; public decimal Balance; public int Count; }
    public decimal TotalDue { get; set; }

    // In ASP.NET Core, we'll provide a method for binding repeater items
    // This would be called from the UI template for each item
    protected async Task BindItemData(object item, ElementReference job, ElementReference total, ElementReference date, 
                               ElementReference vin, ElementReference po, ElementReference invoiceNumber, 
                               ElementReference callNumber, ElementReference subtotal, ElementReference tax,
                               ElementReference incident, ElementReference auth, ElementReference segment)
    {
        if (item is SummaryRow row)
        {
            // Create a batch update for summary row
            var rowUpdates = new Dictionary<string, object>();
            
            rowUpdates["invoiceNumber"] = "<strong style='position:absolute'>" + row.Count + 
                (row.Total == 0 && row.Balance == 0 ? " jobs total" : " jobs") + "</strong><br />";
                
            if (row.Total > 0)
                rowUpdates["subtotal"] = "<strong>" + row.Total.ToMoney() + "</strong><br /><br />";
                
            if (row.Balance > 0)
                rowUpdates["total"] = "<strong>" + row.Balance.ToMoney() + "</strong><br /><br />";
                
            await JSRuntime.InvokeVoidAsync("updateElementsHtml", rowUpdates);
        }
        else if (item is Entry en)
        {
            var updates = new Dictionary<string, object>();
            
            // Start with job header as strong
            updates["job"] = "<strong>";
            
            // Handle invoice number
            if (!string.IsNullOrEmpty(en.InvoiceNumber))
                updates["invoiceNumber"] = _(en.InvoiceNumber);
            else
                updates["invoiceNumber"] = en.CreateDate.Year.ToString().Substring(2, 2) + "-" + en.CallNumber;
                
            // Call number
            updates["callNumber"] = en.CallNumber.ToString();
            
            // Purchase order if available
            if (!string.IsNullOrWhiteSpace(en.PurchaseOrderNumber))
                updates["po"] = _(en.PurchaseOrderNumber);
                
            // Vehicle info
            if (en.Assets.Count() > 0)
            {
                updates["job"] += String.Join("\n<br />", en.Assets.Select(o => 
                    _((o.Year > 1000 ? "'" + o.Year.ToString().Substring(2) : (o.Year > 0 ? o.Year.ToString() : "")) + 
                    " " + o.Make + " " + o.Model + " " + 
                    (ShowUnitNumber ? o.UnitNumber + " " : "") + (ShowPlateNumber ? o.LicenseNumber : "") +
                    " " + (o.ColorId > 0 ? Extric.Towbook.Vehicle.Color.GetById(o.ColorId).Name + " " : ""))
                ));
            }
            else if (!String.IsNullOrEmpty(en.MakeModelFormatted))
            {
                string jobText = _((en.Year > 1000 ? "'" + en.Year.ToString().Substring(2) + " " : (en.Year > 0 ? en.Year.ToString() : "")) + 
                    en.MakeModelFormatted);
                    
                if (en.Color != null && en.Color.Id != 9)
                    jobText += " " + _(en.Color.ToString());
                    
                updates["job"] += jobText;
            }
            
            // SafeClear specific data
            if (IsSafeClear)
            {
                if (en.Attributes.ContainsKey(5793))
                    updates["incident"] = _(en.Attributes[5793].Value);

                if (en.Attributes.ContainsKey(6126))
                    updates["auth"] = _(en.Attributes[6126].Value);

                if (en.Attributes.ContainsKey(8605))
                    updates["segment"] = _(en.Attributes[8605].Value);
            }
            
            // Close strong tag for job
            updates["job"] += "</strong>";
            
            // Build additional content for job
            string extra = "";
            
            // Add VIN if needed
            if (ShowVin)
            {
                if (en.Assets.Count() > 1)
                {
                    updates["vin"] = String.Join(",\n", en.Assets.Where(o => o.Vin != null)
                        .Select(o => _((ShowVinAsLastEight ? (o.Vin.Length > 8 ? 
                            o.Vin.Substring(o.Vin.Length < 8 ? 0 : o.Vin.Length - 8) : o.Vin) : o.Vin))));
                }
                else if (!String.IsNullOrEmpty(en.VIN))
                {
                    updates["vin"] = _((ShowVinAsLastEight ? (en.VIN.Length > 8 ? 
                        en.VIN.Substring(en.VIN.Length < 8 ? 0 : en.VIN.Length - 8) : en.VIN) : en.VIN));
                }
            }
            
            // Add odometer info
            if (en.Odometer > 0 && ShowOdometer)
                extra += (extra.Length > 0 ? ", " : "- ") + " Odometer: " + en.Odometer.ToString("0,0");
                
            // Add reason if configured
            if (en.Reason != null && ShowReason)
                extra += "<br />" + "- Reason: " + _(en.Reason.ToString());
                
            // Add driver info
            if (en.Driver != null && ShowDriver)
            {
                extra += ", Driver: " + _(en.Driver.Name);
                if (ShowTruck && en.Truck != null)
                    extra += " / " + _(en.Truck.Name);
            }
            
            // Add dispatch number if available
            if (en.Attributes.ContainsKey(AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER))
                extra += "- Dispatch Number: " + _(en.Attributes[AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER].Value) + "<br />";
                
            // Add membership number if available
            if (en.Attributes.ContainsKey(AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER))
                extra += "- Membership Number: " + _(en.Attributes[AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER].Value) + "<br />";
                
            // Add unit number if available
            if (en.Attributes.ContainsKey(1685))
                extra += "- Unit#: " + _(en.Attributes[1685].Value) + "<br />";
                
            // Add notes if configured
            if (ShowNotes && !String.IsNullOrWhiteSpace(en.Notes))
                extra += "- Notes:" + _(en.Notes) + "<br />";
                
            // Add printable attributes
            var printables = (await Extric.Towbook.Dispatch.Attribute.GetByCompanyAsync(en.CompanyId))
                .Where(o => o.PrintOnStatement == true && !IsSafeClear);
                
            foreach (var printable in en.Attributes.Values.Where(p => printables.Where(z => z.Id == p.DispatchEntryAttributeId).Any()))
            {
                var attributeInfo = printables.First(o => o.Id == printable.DispatchEntryAttributeId);
                extra += " " + _(attributeInfo.Name + ": " + 
                    GetCustomAttributeValueByType(printable.Value, attributeInfo.Type));
            }
            
            // Special case for company 984
            if (WebGlobal.CurrentUser != null && WebGlobal.CurrentUser.CompanyId == 984)
            {
                foreach (var x in en.Attributes.Values)
                {
                    var atk = await Extric.Towbook.Dispatch.Attribute.GetByIdAsync(x.DispatchEntryAttributeId);
                    extra += _(atk.Name + ":" + x.Value) + "\n<br />";
                }
            }
            
            // Add to/from if configured
            if (ShowToFrom)
            {
                extra += "<br /> - From: " + _(en.TowSource) + ", ";
                extra += "To: " + (en.TowDestination) + "<br />";
            }
            
            // Special impound notice for company 1376
            if (WebGlobal.CurrentUser != null && WebGlobal.CurrentUser.PrimaryCompanyId == 1376 && en.Impound)
            {
                var imp = Extric.Towbook.Impounds.Impound.GetByDispatchEntry(en);
                if (imp.ReleaseDate == null)
                    extra += "<strong style='color:red'>** Vehicle is still in impound **</strong>";
            }
            
            // Add billing notes if configured
            if (ShowBillingNotes && !String.IsNullOrWhiteSpace(en.BillingNotes()))
                extra += "- Billing Notes:" + _(en.BillingNotes()) + "<br />";
                
            // Add invoice items if configured
            if (ShowInvoiceItems)
            {
                extra += "<div style='padding-top: 0.25rem; padding-bottom: 0.25rem;'>";
                
                foreach (var i in en.InvoiceItems.Where(o => o.Name != null && !o.Name.Contains("FreeQuantity")))
                {
                    if (HideMileage && 
                        i.RateItem != null && 
                        i.RateItem.Predefined != null && 
                        new int[] { Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED, 
                                   Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED }.Contains(i.RateItem.Predefined.Id))
                        continue;
                        
                    extra += "<div class='row'>";
                    
                    if (i.RateItem != null)
                        extra += "<div class='column f2'>" + _(i.RateItem.Name) + "</div>";
                    else
                        extra += "<div class='column f2'>" + _(i.CustomName) + "</div>";
                        
                    extra += "<div class='column f2 right'>" + i.Quantity;
                    
                    decimal outputTotal = 0.0M;
                    
                    var free = en.Invoice.InvoiceItems.Where(o => o.RelatedInvoiceItemId == i.Id).FirstOrDefault();
                    if (free != null && free.Name.Contains("FreeQuan") && free.Quantity != 0)
                    {
                        // Start with the actual amount of free quantity units used
                        string strQuantity = free.Quantity.ToString();
                        decimal freeQuantity = free.Quantity;
                        
                        if (i.RateItem != null && en.Account != null)
                        {
                            // display the full amount for free miles for the account
                            var accFree = await RateItem.GetAccountFreeQuantityAsync(i.RateItem, en.Account.Id);
                            if (accFree != null)
                            {
                                strQuantity = accFree.Value.ToString();
                                freeQuantity = accFree.Value;
                            }
                        }
                        
                        if (((Extric.Towbook.RateItem)i.RateItem).Measurement == Extric.Towbook.RateItem.MeasurementEnum.Minutes)
                            extra += " - " + strQuantity + " minutes free = " + (i.Quantity - free.Quantity);
                        else
                            extra += " - " + strQuantity + " " + en.Company.LocaleMile + "s free = " + (i.Quantity - free.Quantity);
                            
                        var ftotal = i.Total + free.Total;
                        
                        if (i.Total != 0)
                        {
                            extra += " @ " + String.Format("{0:C}", (i.CustomPrice.HasValue ? i.CustomPrice : 0));
                            outputTotal = ftotal;
                        }
                    }
                    else
                    {
                        extra += " @ " + String.Format("{0:C}", (i.CustomPrice.HasValue ? i.CustomPrice : 0));
                        outputTotal = i.Total;
                    }
                    
                    extra += "</div>"; // column
                    extra += "<div class='column f1 right'>" + String.Format("{0:C}", outputTotal) + "</div>";
                    extra += "</div>"; // row
                }
                
                extra += "</div>";
            }
            
            // Payment information
            decimal balanceDue = en.InvoiceTotal;
            var totalPayments = 0.0m;
            
            try
            {
                var payments = Extric.Towbook.Dispatch.InvoicePayment.GetByDispatchEntryId(en.Id, false)
                    .Where(o => ShowLatest || o.PaymentDate < Statement.StatementDate);
                    
                foreach (var ip in payments)
                {
                    extra += "<span style=\"clear:both; display:block\">";
                    if (ip.ReferenceNumber == "*ADJUSTMENT")
                    {
                        extra += "-  <strong>Discount applied</strong> - " + ip.Amount.ToMoney();
                    }
                    else
                    {
                        extra += "-  <strong>Payment applied</strong> on " + ip.CreateDate.ToShortDate() + 
                            " via " + ip.PaymentType.ToString() + " " + _(ip.ReferenceNumber) + 
                            "<span style=\"float:right\">- " + ip.Amount.ToMoney() + "</span><br />";
                    }
                    extra += "</span>";
                    
                    balanceDue -= ip.Amount;
                }
                totalPayments = payments.Sum(o => o.Amount);
            }
            catch { }
            
            // Invoice link
            if (ShowInvoiceLinks)
            {
                string url = "https://app.towbook.com/PublicAccess/Invoice2.aspx?id=" + en.Id;
                string md5 = Extric.Towbook.Core.ProtectId(en.Id, en.OwnerUserId);
                
                url += "&sc=" + md5;
                
                extra += "<span style=\"clear:both; display:block\">Invoice: <a href=\"" + url + 
                    "\" target=\"_blank\" style=\"color: blue; text-decoration: underline; \">" + url + "</a></span>";
            }
            
            // Add all the extra content to the job
            updates["job"] += extra;
            
            // Set the date
            updates["date"] = (ShowCompletionDate && en.CompletionTime != null) 
                ? en.CompletionTime.Value.ToShortDate()
                : en.CreateDate.ToShortDateString();
                
            // Set totals
            updates["subtotal"] = " " + en.InvoiceSubtotal.ToMoney() + "<br />" + 
                (totalPayments != 0 && ShowInvoiceItems ? "- " + totalPayments.ToMoney() : "");
            updates["tax"] = en.InvoiceTax.ToMoney();
            updates["total"] = balanceDue.ToMoney();
            
            // Update all elements with the collected data
            await JSRuntime.InvokeVoidAsync("updateElementsHtml", updates);
            
            // Update TotalDue and original amount labels
            TotalDue += balanceDue;
            lblOriginalDue2 = TotalDue.ToMoney();
            lblOriginalDue = TotalDue.ToMoney();
        }
        else if (item is Extric.Towbook.Accounts.AccountInvoiceItem aie)
        {
            //job =_(aie.Memo));
            //date = aie.Date.ToShortDate());
        }
        else if (item is Extric.Towbook.Invoice inv && inv.InvoiceItems.Count == 1)
        {
            await JSRuntime.InvokeVoidAsync("setText", job, _(inv.InvoiceItems.First().CustomName));
            await JSRuntime.InvokeVoidAsync("setText", date, "Balance Forward");
            await JSRuntime.InvokeVoidAsync("setText", total, inv.BalanceDue.ToMoney());
        }
    }

    public void LogRequest(string message)
    {
        var logEvent = new LogEventInfo();
        logEvent.LoggerName = logger.Name;
        logEvent.Message = "Statement Create Log Event" + message;
        logEvent.Level = NLog.LogLevel.Info;
        logEvent.TimeStamp = DateTime.Now;
        if (Statement != null)
        {
            logEvent.Properties["data"] = new
            {
                invoices = Statement.Invoices != null ? Statement.Invoices.Select(i => i.Id).ToArray().ToJson() : null,
            }.ToString();
            logEvent.Properties["accountId"] = Statement.AccountId;
        }
        logEvent.Properties["commitId"] = Core.GetCommitId();
        var companyId = 0;
        var userId = 0;
        if (WebGlobal.CurrentUser != null)
        {
            companyId = WebGlobal.CurrentUser.CompanyId;
            userId = WebGlobal.CurrentUser.Id;
        }
        logEvent.Properties["companyId"] = companyId;
        logEvent.Properties["userId"] = userId;

        logger.Log(logEvent);
    }
}
