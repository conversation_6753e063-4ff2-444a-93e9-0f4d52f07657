using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Extric.Towbook;
using Extric.Towbook.Impounds;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models;
using Ajax.Areas.Settings.Models;
using static Ajax.AjaxUtility;
using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class ImpoundRemindersController : Controller
    {

        [HttpGet]
        public async Task<PartialViewResult> IndexAsync()
        {
            return await MainAsync();
        }

        [HttpGet]
        public async Task<PartialViewResult> MainAsync()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Impound Reminders");

            var reminderItems = (await ReminderItem.GetByCompanyAsync(WebGlobal.CurrentUser.Company))
                .OrderBy(o => o.Days)
                .ToCollection();


            // AC 11/12/2021: stop grouping by type.
            //var model = new ImpoundRemindersMainViewModel(WebGlobal.CurrentUser.Company, reminderItems);

            var model = new ImpoundRemindersMainViewModel();

            model.ReminderGroups = new List<ImpoundRemindersGroupViewModel>();

            model.ReminderGroups.Add(
                new ImpoundRemindersGroupViewModel()
                {
                    ImpoundType = ImpoundType.NotSpecified,
                    ReminderItems = reminderItems.Select(s => ReminderItemModel.MapDomainObjectToModel(s)).ToList(),
                    ImpoundTypeLabel = "Tasks and Reminders"
                });


            return PartialView("../impounds/reminders/List", model);
        }
        
        [HttpGet]
        public async Task<PartialViewResult> ListAsync()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            IList<ReminderItem> model = await ReminderItem.GetByCompanyAsync(WebGlobal.CurrentUser.Company);
            return PartialView(model);
        }

        /// <summary>
        /// Returns a read-only view of the ReminderItem with supplied ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Impound Reminders");

            var reminderItem = ReminderItem.GetById(id);

            await ThrowIfNoCompanyAccessAsync(reminderItem?.CompanyId);

            var model = ReminderItemModel.MapDomainObjectToModel(reminderItem);

            var letterTemplates = await LetterTemplate.GetByCompanyAsync(WebGlobal.CurrentUser.Company);
            
            
            List<SelectListItem> letterTemplateSelectList = new SelectList(letterTemplates, "Id", "Title").ToList();
            
            letterTemplateSelectList.Insert(0, new SelectListItem() { Text = "(none)", Value = "0" });
            
            ViewBag.letterTemplateSelectList = letterTemplateSelectList;

            ViewData["lblTitle"] = "Modify Reminder";

            return PartialView("../impounds/reminders/ReminderEditor", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Details(ReminderItemModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                ReminderItem reminderItem;

                if (model.ReminderItemId > 0)
                {
                    reminderItem = ReminderItem.GetById(model.ReminderItemId);
                    await ThrowIfNoCompanyAccessAsync(reminderItem?.CompanyId);
                }
                else { 
                    reminderItem = new ReminderItem();
                    reminderItem.CompanyId = WebGlobal.CurrentUser.CompanyId;
                }

                reminderItem = ReminderItemModel.MapModelToDomainObject(model, reminderItem);

                if (model.LetterTemplateId > 0)
                    reminderItem.LetterTemplateId = model.LetterTemplateId;
                else
                    reminderItem.LetterTemplateId = (int?)null;

                reminderItem.Save();               

                return RedirectToAction("Index"); // return Details(model.Id);
            }
            else
            {
                return await Details(model.ReminderItemId);
            }
        }

        [HttpGet]
        public async Task<PartialViewResult> New(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Impound Reminders");

            var model = new ReminderItemModel();
            ImpoundType impoundType = (ImpoundType)Convert.ToInt32(id);
            model.ImpoundTypeId = id;

            var letterTemplates = await LetterTemplate.GetByCompanyAsync(WebGlobal.CurrentUser.Company);
            
            List<SelectListItem> letterTemplateSelectList = new SelectList(letterTemplates, "Id", "Title").ToList();
            
            letterTemplateSelectList.Insert(0, new SelectListItem() { Text = "(none)", Value = "0" });
            
            ViewBag.letterTemplateSelectList = letterTemplateSelectList;

            ViewData["lblTitle"] = "Create a new Reminder for " + FormatImpoundType(impoundType);

            return PartialView("../impounds/reminders/ReminderEditor", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Delete(ReminderItemModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var ri = await ReminderItem.GetByIdAsync(model.ReminderItemId);

            if (ri == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(ri.CompanyId))
                throw new Exception("Invalid ID");

            ri.Delete(WebGlobal.CurrentUser);

            return RedirectToAction("Index");
        }

        /// <summary>
        /// Saves the posted object
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Save(ReminderItem model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                if (model.Id > 1)
                {
                    if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.CompanyId))
                        throw new Exception("Invalid ID");
                }

                model.Save();
                return await Details(model.Id);
            }
            else
            {
                return await Form(model);
            }
        }

        /// <summary>
        /// Returns a form for data entry.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PartialViewResult> Form(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            ReminderItem model;
            if (id == 0)
                model = new ReminderItem();
            else
            {
                model = await ReminderItem.GetByIdAsync(id);

                if (model == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.CompanyId))
                    throw new Exception("Invalid ID");
            }

            return PartialView();
        }

        /// <summary>
        /// Returns a form for data entry.
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PartialViewResult> Form(ReminderItem model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (model == null)
                model = new ReminderItem();
            else
                if(!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.CompanyId))
                    throw new Exception("Invalid ID");

            return PartialView();
        }

        protected string FormatImpoundType(object d)
        {
            switch ((ImpoundType)d)
            {
                case ImpoundType.NotSpecified:
                    return "All Impounds";
                case ImpoundType.Other:
                    return "Other Impounds";
                case ImpoundType.Police:
                    return "Police Impounds";
                case ImpoundType.Accident:
                    return "Accident Impounds";
                case ImpoundType.PrivatePropertyImpound:
                    return "Private Property Impounds";
                case ImpoundType.Repossession:
                    return "Repossessed Impounds";
            }

            return "";
        }

    }
}
