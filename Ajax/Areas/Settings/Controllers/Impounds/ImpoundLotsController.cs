using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.WebShared;
using Extric.Towbook.Impounds;
using Extric.Towbook.API.Models;
using Extric.Towbook;
using Extric.Towbook.API.Controllers;
using Extric.Towbook.API.Controllers.Mapping;
using System.Threading.Tasks;
using Extric.Towbook.Licenses;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class ImpoundLotsController : Controller
    {
        [HttpGet]
        public async Task<PartialViewResult> Index()
        {
            return await List();
        }

        [HttpGet]
        public async Task<PartialViewResult> List()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var model = LotModel.MapDomainObjectListToModelList(await Lot.GetByCompanyAsync(WebGlobal.CurrentUser.Company));
            Response.Headers["X-Twbk-Title"] = "Impound Lots";
            return PartialView("../impounds/lots/List", model);

        }
                        
        /// <summary>
        /// Returns a read-only view of the lot with supplied ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers["X-Twbk-Title"] = "Modify Impound Lot";
            var model = LotModel.MapDomainObjectToModel(await Lot.GetByIdAsync(WebGlobal.CurrentUser.CompanyId, id));

            this.ViewBag.TaxRates = await GetTaxRateSelectListAsync();


            #region ImpoundLotLicense

            var lotLicenseKeysFiltered = ImpoundLotLicenseKey.GetByCompany(WebGlobal.CurrentUser.Company);
            var allLotLicenseKeyValues = ImpoundLotLicenseKeyValue.GetByLotId(model.Id);
            var licenseData = LotModel.GetAllMatchedKeyValues(lotLicenseKeysFiltered, allLotLicenseKeyValues);

            model.Licenses = licenseData;
            #endregion

            
            return PartialView("../impounds/lots/Lot", model);
        }

        public async Task<SelectList> GetTaxRateSelectListAsync()
        {
            var tr = await TaxRate.GetByCompanyAsync(WebGlobal.CurrentUser.Company);
            if (tr.Count > 1)
            {
                var values = tr.Select(n => new { Id = n.Id, Name = n.Description }).ToList();

                values.Insert(0, new { Id = 0, Name = "No Default" });

                return new SelectList(values, "Id", "Name");
            }
            else
                return null;
        }

        [HttpGet]
        public async Task<PartialViewResult> New()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Add a New Impound Lot");
            var model = new LotModel();
            
            this.ViewBag.TaxRates = await GetTaxRateSelectListAsync();

            return PartialView("../impounds/lots/Lot", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Details(LotModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (model.Companies != null && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.Companies))
                 throw new TowbookException("Forbidden");

            if (ModelState.IsValid)
            {
                Lot _lot;
                if (model.Id > 0)
                {
                    _lot = await Lot.GetByIdAsync(WebGlobal.CurrentUser.CompanyId, model.Id);
                }
                else { 
                    _lot = new Lot();
                    _lot.CompanyId = WebGlobal.CurrentUser.CompanyId;
                }

                var address = _lot.GetComposedAddress();
                LocationModel[] location = null;

                var r2 = await GeocodeWithGoogle(address);
                if (r2 != null)
                    location = r2.ToArray();

                if (location.Length > 0)
                {
                    _lot.Latitude = location[0].Latitude;
                    _lot.Longitude = location[0].Longitude;
                }

                var l = LotModel.MapModelToDomainObject(model, _lot);
                await l.Save();


                #region Licenses Save to database
                if (model.Licenses != null && model.Licenses.Any())
                {
                    foreach (var x in model.Licenses)
                    {
                        ImpoundLotLicenseKeyValue clkv = new ImpoundLotLicenseKeyValue();

                        if (x.KeyId > 0)
                        {
                            var c = ImpoundLotLicenseKeyValue.GetByLotId(l.Id).FirstOrDefault(f => f.KeyId == x.KeyId);

                            if (c != null)
                                clkv = c;
                        }

                        clkv.ImpoundLotId = l.Id;
                        clkv.KeyId = x.KeyId;
                        clkv.Value = x.Value;

                        if (string.IsNullOrWhiteSpace(clkv.Value))
                            clkv.Delete();
                        else
                            await clkv.Save(WebGlobal.CurrentUser);
                    }
                }
                #endregion

                return RedirectToAction("Index"); // return Details(model.Id);
            }
            else
            {
                return await Details(model.Id);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Delete(LotModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var l = await Lot.GetByIdAsync(WebGlobal.CurrentUser.CompanyId, model.Id);

            if (l != null && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(l.Companies))
                 throw new TowbookException("Forbidden");

            if (l != null)
            {
                await l.Delete();
            }
            return RedirectToAction("Index");

        }

        /// <summary>
        /// Saves the posted object
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Save(Lot model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                await model.Save();
                return await Details(model.Id);
            }
            else
            {
                return Form(model);
            }
        }


        /// <summary>
        /// Returns a form for data entry.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PartialViewResult> Form(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Lot model;
            if (id == 0)
                model = new Lot();
            else
                model = await Lot.GetByIdAsync(WebGlobal.CurrentUser.CompanyId, id);
            return PartialView();
        }

        /// <summary>
        /// Returns a form for data entry.
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [NonAction]
        public PartialViewResult Form(Lot model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (model == null)
                model = new Lot();

            return PartialView();
        }

        private async Task<IEnumerable<LocationModel>> GeocodeWithGoogle(string address)
        {
            var je = await GMapUtil.Geocode(address);

            return LocationModel.MapFromGoogle(je);
        }
    }
}
