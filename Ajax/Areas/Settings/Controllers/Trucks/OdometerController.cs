using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook;
using Extric.Towbook.API.Models;
using Extric.Towbook.WebShared;
using Extric.Towbook.Utility;
using static Ajax.AjaxUtility;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers.Trucks
{
    [Area("Settings")]
    public class OdometerController : Controller
    {
        //
        // GET: /Settings/Odometer/

        public ActionResult Index()
        {
            return PartialView("../trucks/odometer");
        }

        [HttpGet]
        public async Task<PartialViewResult> EnterOdometer(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
                WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "New Odometer");

            var truck = await Truck.GetByIdAsync(id);
            await ThrowIfNoCompanyAccessAsync(truck?.Companies, "truck");

            var model = new TruckOdometerReadingModel();
            
            model.Id = 0;
            model.TruckId = id;
            model.UserId = WebGlobal.CurrentUser.Id;
            model.Date = WebGlobal.OffsetDateTime(DateTime.Now);

            return PartialView("../trucks/odometer", model);
        }

        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
                WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var tor = await TruckOdometerReading.GetByIdAsync(id);
            var t = await Truck.GetByIdAsync(WebGlobal.CurrentUser, tor.TruckId);
            await ThrowIfNoCompanyAccessAsync(t?.Companies, "truck");
            
            Response.Headers.Add("X-Twbk-Title", "Modify Odometer Reading for: " + t.Name);

            var model = TruckOdometerReadingModel.Map(tor);

            model.Date = WebGlobal.OffsetDateTime(model.Date);

            return PartialView("../trucks/odometer", model);
        }

        [HttpPost]
        public async Task<ActionResult> EnterOdometer(TruckOdometerReadingModel model)
        {
            model.Id = 0;
            return await Details(model);
        }

        [HttpPost]
        public async Task<ActionResult> Details(TruckOdometerReadingModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
                WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                TruckOdometerReading truckOdometerReading;

                var truck = await Truck.GetByIdAsync(model.TruckId);
                await ThrowIfNoCompanyAccessAsync(truck?.Companies, "truck");

                if (model.Id > 0)
                {
                    truckOdometerReading = await TruckOdometerReading.GetByIdAsync(model.Id);
                }
                else
                {
                    truckOdometerReading = new TruckOdometerReading();
                }

                if (truckOdometerReading == null)
                    throw new TowbookException("truckOdometerReading is null");

                if (model == null)
                    throw new TowbookException("TruckOdometerReadingModel is null");

                model.Date = WebGlobal.OffsetDateTime(model.Date, true, WebGlobal.CurrentUser.Company);

                var x = TruckOdometerReadingModel.Map(model, truckOdometerReading);
                x.UserId = WebGlobal.CurrentUser.Id;

                try
                {
                    x.Save();
                }
                catch
                {
                    ModelState.AddModelError("Odometer", "The odometer value you entered is invalid.");
                    return PartialView("../trucks/odometer", model);
                }

                if (Request.Form["ajax"] == "1")
                {
                    return Content("<script language='javascript' type='text/javascript'>" +
                        "parent.finishOdometerRequest(" + TruckOdometerReadingModel.Map(x).ToJson() + ");</script>", "text/html");
                }
                else
                {
                    return RedirectToAction("../Trucks/TrucksMain/" + model.TruckId + "/details"); // return Details(model.Id);
                }
            }
            else
            {
                return await Details(model.Id);
            }
        }

        [HttpPost]
        public async Task<IActionResult> Delete(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
                WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            (await TruckOdometerReading.GetByIdAsync(id)).Delete();
            
            Dictionary<string, dynamic> data = new Dictionary<string, dynamic>();

            data.Add("id", id);
            data.Add("deleted", true);

            return Content("<script language='javascript' type='text/javascript'>" +
                    "parent.finishOdometerRequest(" + data.ToJson() + ");</script>", "text/html");
        }
    }
}
