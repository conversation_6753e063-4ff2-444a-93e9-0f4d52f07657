using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Company;
using Extric.Towbook.API.Models;
using Extric.Towbook.WebShared;
using Extric.Towbook;
using static Ajax.AjaxUtility;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers.Trucks
{
    [Area("Settings")]
    public class TruckServiceGroupController : Controller
    {
        //
        // GET: /Settings/TruckServiceGroup/

        public ActionResult Index()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            return View();
        }

        [HttpGet]
        public PartialViewResult New(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "New Maintenance Item");
            var model = new TruckServiceGroupItemModel();
            model.ServiceGroupId = id;

            return PartialView("../trucks/TruckServiceGroupItem", model);
        }

        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Modify Expense");
            var model = TruckServiceGroupItemModel.MapDomainObjectToModel(await TruckServiceGroupItem.GetByIdAsync(id));

            var serviceGroupItem = await TruckServiceGroupItem.GetByIdAsync(id);
            var serviceGroup = await TruckServiceGroup.GetByIdAsync(serviceGroupItem.ServiceGroupId);
            await ThrowIfNoCompanyAccessAsync(serviceGroup?.Company?.Id);

            return PartialView("../trucks/TruckServiceGroupItem", model);
        }

        [HttpPost]
        public async Task<ActionResult> Details(TruckServiceGroupItemModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                TruckServiceGroupItem truckServiceGroupItem;

                if (model.TruckServiceGroupItemId > 0)
                {
                    truckServiceGroupItem = await TruckServiceGroupItem.GetByIdAsync(model.TruckServiceGroupItemId);
                    var serviceGroup = await TruckServiceGroup.GetByIdAsync(truckServiceGroupItem.ServiceGroupId);
                    await ThrowIfNoCompanyAccessAsync(serviceGroup?.Company?.Id);
                }
                else
                {
                    truckServiceGroupItem = new TruckServiceGroupItem();
                }

                TruckServiceGroupItemModel.MapModelToDomainObject(model, truckServiceGroupItem).Save();
                return RedirectToAction("../Trucks/MaintenanceServiceSchedule/Index"); // return Details(model.Id);
            }
            else
            {
                return await Details(model.TruckServiceGroupItemId);
            }
        }

        [HttpPost]
        public async Task<ActionResult> Delete(TruckServiceGroupItemModel model)
        {
            var serviceGroupItem = await TruckServiceGroupItem.GetByIdAsync(model.TruckServiceGroupItemId);
            var serviceGroup = await TruckServiceGroup.GetByIdAsync(serviceGroupItem.ServiceGroupId);
            await ThrowIfNoCompanyAccessAsync(serviceGroup?.Company?.Id);
            await serviceGroupItem.DeleteAsync();
            return RedirectToAction("../Trucks/MaintenanceServiceSchedule/Index");

        }

        [HttpGet]
        public ActionResult NewScheduleGroup()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var model = new ScheduleGroupModel();
            return PartialView("../Trucks/ScheduleGroup", model);
        }

        [HttpPost]
        public ActionResult SaveScheduleGroup(ScheduleGroupModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                TruckServiceGroup truckServiceGroup;

                truckServiceGroup = new TruckServiceGroup();

                truckServiceGroup.Company = Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company;
                truckServiceGroup.Name = model.Name;

                truckServiceGroup.Save();

                return RedirectToAction("../Trucks/MaintenanceServiceSchedule/Index"); // return Details(model.Id);
            }
            else
            {
                return NewScheduleGroup();
            }
        }
    }
}
