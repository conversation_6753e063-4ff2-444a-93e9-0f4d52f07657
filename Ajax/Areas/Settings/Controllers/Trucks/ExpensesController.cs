using System;
using System.Linq;
using Extric.Towbook;
using Extric.Towbook.API;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models;
using System.IO;
using System.Collections.ObjectModel;
using Extric.Towbook.Utility;
using Extric.Towbook.Storage;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using HttpContextHelper = Extric.Towbook.Web.HttpContext;
using Extric.Towbook.WebShared.Multipart;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers.Trucks
{
    [Area("Settings")]
    public class ExpensesController : Controller
    {
        [HttpGet]
        public async Task<PartialViewResult> AddNewExpense(int id)
        {
            if (!new[] { Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Accountant }.Contains(WebGlobal.CurrentUser.Type))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            ModelState.Clear();

            var truck = await Truck.GetByIdAsync(id);
            if (truck == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(truck.Companies))
                throw new TowbookException("Truck does not exist or you don't have access to it.");

            Response.Headers.Add("X-Twbk-Title", "New Expense for " + truck.Name);

            var model = new TruckExpenseItemModel();
            model.TruckId = id;
            model.UserId = WebGlobal.CurrentUser.Id;
            model.Date = DateTime.Now.Date;

            var trucks = (await Truck.GetByCompanyAsync(WebGlobal.CurrentUser.Company)).Where(o => o.IsActive == true || o.Id == truck.Id);

            this.ViewBag.Categories = new SelectList(TruckExpenseCategory.GetByCompanyId(WebGlobal.CurrentUser.CompanyId), "Id", "Name", model.CategoryId);
            this.ViewBag.Trucks = new SelectList(trucks, "Id", "Name", model.TruckId);

            return PartialView("../trucks/expenses/expense", model);
        }

        [HttpPost]
        public async Task<ActionResult> AddNewExpense(TruckExpenseItemModel model)
        {
            if (!new[] { Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Accountant }.Contains(WebGlobal.CurrentUser.Type))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            return await Details(model);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id">TruckId</param>
        /// <returns></returns>
        private async Task<CompanyFile> HandleUpload(int id, int expenseId)
        {
            if (Request.Form.Files == null || Request.Form.Files.Count == 0)
                return null;

            var file = Request.Form.Files[0];

            var Truck = await Extric.Towbook.Truck.GetByIdAsync(id);

            if (Truck == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(Truck.CompanyId))
                throw new TowbookException("Invalid TruckId or you don't have access to the item requested.");

            var tei = await TruckExpenseItem.GetByIdAsync(expenseId);

            if (tei == null || tei.TruckId != id)
            {
                throw new TowbookException("Invalid TruckId or you don't have access to the item requested.");
            }

            if (file.Length > 0)
            {
                var cn = new Extric.Towbook.CompanyFile();

                cn.CompanyId = WebGlobal.CurrentUser.CompanyId;
                cn.OwnerUserId = WebGlobal.CurrentUser.Id;
                cn.Filename = file.FileName;
                cn.Size = (int)Request.Form.Files[0].Length;
                cn.RawUrl = cn.Filename;
                cn.Description = "expenseItemId=" + expenseId;
                cn.Trucks = new Collection<int>() { id };
                cn.TruckExpenseFiles = new Collection<int>() { expenseId };
                cn.Save();

                var fn = HttpContextHelper.MapPath(cn.LocalLocation);

                if (!Directory.Exists(Path.GetDirectoryName(fn)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(fn));
                }

                FileUploadUtils.saveAs(file, fn);

                if (!DefenderApi.IsFileUnsafe(fn))
                {
                    var result = await FileUtility.SendFileAsync(fn);
                    if (result.IsHttpSuccess())
                        System.IO.File.Delete(fn);
                }
                else
                {
                    cn.Delete(WebGlobal.CurrentUser);
                    System.IO.File.Delete(fn);
                }

                return await System.Threading.Tasks.Task.FromResult(cn);
            }

            return null;
        }

        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (!new[] { Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Accountant }.Contains(WebGlobal.CurrentUser.Type))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Modify Expense");
            var model = TruckExpenseItemModel.MapDomainObjectToModel(await TruckExpenseItem.GetByIdAsync(id));
            var truck = await Truck.GetByIdAsync(model.TruckId);


            if (truck == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(truck.CompanyId))
                throw new TowbookException("Invalid truck reference or you don't have access to the item requested.");

            this.ViewBag.Categories = new SelectList(TruckExpenseCategory.GetByCompanyId(WebGlobal.CurrentUser.CompanyId), "Id", "Name", model.CategoryId);
            this.ViewBag.Trucks = new SelectList(await Truck.GetByCompanyAsync(WebGlobal.CurrentUser.Company), "Id", "Name", model.TruckId);

            return PartialView("../trucks/expenses/expense", model);
        }

        [HttpPost]
        public async Task<ActionResult> Details(TruckExpenseItemModel model)
        {
            if (!new[] { Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Accountant }.Contains(WebGlobal.CurrentUser.Type))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                var truck = await Truck.GetByIdAsync(model.TruckId);

                if (truck == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(truck.CompanyId))
                    throw new TowbookException("Invalid truck reference or you don't have access to the item requested. Truck:" + model.TruckId);

                var truckExpenseItem = new TruckExpenseItem();
                if (model.Id > 0)
                {
                    truckExpenseItem = await TruckExpenseItem.GetByIdAsync(model.Id);

                    if (truckExpenseItem != null && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync((await Truck.GetByIdAsync(truckExpenseItem.TruckId)).CompanyId))
                    {
                        throw new TowbookException("Invalid ID.");
                    }
                }

                var tr = TruckExpenseItemModel.MapModelToDomainObject(model, truckExpenseItem);

                tr.UserId = WebGlobal.CurrentUser.Id;
                tr.CategoryId = model.CategoryId;

                tr.Save();

                var cf = await HandleUpload(model.TruckId, tr.Id);

                if (cf != null)
                {
                    tr.Files = new int[] { cf.Id };
                }

                if (Request.Form["ajax"] == "1")
                {
                    return Content("<script language='javascript' type='text/javascript'>" +
                        "parent.finishExpenseRequest(" + TruckExpenseItemModel.MapDomainObjectToModel(tr).ToJson() + ");</script>", "text/html");
                }
                else
                {
                    return RedirectToAction("../Trucks/" + model.TruckId);
                }
            }
            else
            {
                return await Details(model.Id);
            }
        }

        [HttpPost]
        public async Task<ActionResult> Delete(TruckExpenseItemModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var tei = await TruckExpenseItem.GetByIdAsync(model.Id);

            if (tei != null)
            {
                var truck = await Truck.GetByIdAsync(tei.TruckId);

                if (truck == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(truck.CompanyId))
                    throw new TowbookException("Invalid truck reference or you don't have access to the item requested.");

                tei.Delete();
            }

            return RedirectToAction("../Trucks/" + model.TruckId);
        }
    }
}
