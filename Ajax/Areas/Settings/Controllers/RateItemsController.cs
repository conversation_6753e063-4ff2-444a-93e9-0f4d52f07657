using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook;
using System.Collections.ObjectModel;
using Extric.Towbook.Surcharges;
using Extric.Towbook.Company;
using Extric.Towbook.Commissions;
using Extric.Towbook.Vehicle;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models;
using Extric.Towbook.API.Controllers;
using Extric.Towbook.Integration;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Net;
using static Ajax.AjaxUtility;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class RateItemsController : Controller
    {
        private static bool IsAccessAllowed()
        {
            return (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager ||
                    WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant);
        }

        public async Task<IActionResult> IndexAsync()
        {
            return await ListAsync();
        }

        [HttpGet]
        public async Task<PartialViewResult> ListAsync()
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers["X-Twbk-Title"] = "Service & Storage Rates";

            bool includeDeleted = await (await Company.GetByIdAsync(WebGlobal.CurrentUser.PrimaryCompanyId))
                .HasFeatureAsync(Extric.Towbook.Generated.Features.Undelete);

            var list = await RateItemListAsync(includeDeleted);
            return PartialView("List", list);
        }

        private async Task<List<IRateItem>> RateItemListAsync(bool? includeDeleted = false)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var list1 = new List<IRateItem>();
            var list2 = new List<IRateItem>();

            foreach (IRateItem x in RateItem.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, true, includeDeleted))
            {
                if (x.ParentRateItemId > 0)
                    continue;

                if (x.Predefined != null)
                {
                    list1.Add(x);
                }
                else
                    list2.Add(x);
            }

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.LedgerAccounts))
            {
                ViewBag.LedgerAccounts = LedgerAccount.GetByCompany(WebGlobal.CurrentUser.CompanyId);
            }

            // Now let's find out what PredefinedRateItem's arent in the list...

            var predefinedList = PredefinedRateItem.GetAll();

            foreach (int i in predefinedList.Keys)
            {
                bool found = false;

                foreach (IRateItem ir in list1)
                {
                    if (ir.Predefined.Id == i)
                        found = true;
                }

                if (found == false)
                {
                    if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.Impounds_AfterHoursReleaseFee) &&
                       predefinedList[i].Id == PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE)
                        continue;

                    if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.Impounds_HourlyStorage) &&
                       predefinedList[i].Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
                        continue;

                    if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DeadheadMileage) &&
                       predefinedList[i].Id == PredefinedRateItem.BUILTIN_MILEAGE_DEADHEAD)
                        continue;

                    if (predefinedList[i].Hidden == false || predefinedList[i].Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE)
                    {
                        RateItem ir = new RateItem();
                        ir.Predefined = predefinedList[i];
                        list1.Add(ir);
                    }
                }
            }

            list1 = list1.OrderBy(o => o.Predefined.Id).ToList();

            list1.AddRange(list2);

            return list1;
        }

        /// <summary>
        /// Returns a read-only view of the Rate Service with supplied ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");
            Response.Headers["X-Twbk-Title"] = "Modify Service Rate Item";


            RateItem _ri = await RateItem.GetByIdAsync(id);
            var model = await RateItemModel.MapAsync(_ri);

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(_ri.CompanyId))
                throw new TowbookException("Access Denied");

            if (_ri.CompanyId != WebGlobal.CurrentUser.CompanyId)
                ViewBag.SharedObject = true;

            var categories = (await RateItemCategory.GetByCompanyAsync(WebGlobal.CurrentUser.CompanyId))
                .OrderByDescending(a => a.Name == "Towing")
                .OrderByDescending(a => a.ParentCategoryId > 0)
                .ThenBy(b => b.Name != "Towing")
                .ToCollection();

            model.Categories = new SelectList(categories, "Id", "Name");
            model.Statuses = new SelectList(Extric.Towbook.Dispatch.Status.GetByCompany(WebGlobal.CurrentUser.CompanyId, Extric.Towbook.Dispatch.StatusCategory.Dispatching)
                .Where(o => o != Extric.Towbook.Dispatch.Status.Cancelled), "Id", "Name");
            
            ViewBag.Config = new
            {
                AccountTypes = new AccountTypesController().Get(),
                AccountTypesPlural = new AccountTypesController().PluralCombined(),
                Vehicle = new
                {
                    Types = await new BodyTypesController().Get()
                }
            }.ToJson();

            ViewBag.Accounts = (await AccountsController.InternalGetAccountsMinimalAsync()).ToJson();

            var childItems = RateItem.GetByParentId(id);

            ViewBag.AccountRateItems = (await new AccountRateItemsController().GetAsync(id))
                .Where(o => 
                    o.AccountCompanyId == WebGlobal.CurrentUser.CompanyId || 
                    o.AccountCompanyId == _ri.CompanyId)
                .OrderBy(o => o.AccountName).ToJson();

            ViewBag.ChildRateItems = (await Task.WhenAll(childItems.Select(async o => await RateItemModel.MapAsync(o as RateItem)))).ToJson() ?? "[]";

            if (id > 0)
            {
                RateItemExclusion rie = await RateItemExclusion.GetByRateItemAsync(id, Surcharge.SURCHARGE_FUEL);
                if (rie != null)
                    model.ExcludeFuelSurcharge = true;
            }

            if (WebGlobal.CurrentUser.Company.State == "MA")
            {
                var rik = RateItemKey.GetByProviderId(Provider.Towbook.ProviderId, "RegulatedCharge");

                model.RegulatedCharge = RateItemKeyValue.GetByRateItem(_ri.CompanyId, _ri.RateItemId)
                    .Where(r => r.KeyId == rik.Id).Any(o => o.Value == "1");
            }

            // Account, AccountRateItem 
            model.TieredStorageRates = (childItems.Count > 0);
            
            var sc = await SurchargeRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, _ri.CompanyId);

            if (sc != null && sc.TreatExclusionsAsInclusions)
                ViewBag.ExcludeFuelSurcharge = "Charge Fuel Surcharge";
            else
                ViewBag.ExcludeFuelSurcharge = "Prevent Fuel Surcharges";

            ViewBag.HasFuelSurcharges = (sc != null);

           
            var mapPricingServerSide = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId,
                    "MapPricingServerSide").FirstOrDefault();

            ViewBag.MapUsingStandardPricing = !(WebGlobal.CurrentUser.CompanyId > 13000 || mapPricingServerSide?.Value == "1" );

            var ckvalue = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "LimitPricingOptionsInEditorToAccountPricingOnly");
            ViewBag.LimitToAccountPricingOption = ckvalue == "1";

            ViewBag.InclusionType = (bool)(_ri.InclusionType == RateItem.InclusionTypeEnum.Corporate);

            ViewBag.EnableClassTracking = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.ClassTracking);
            
            ViewBag.ChargeClasses = ChargeClass.GetByCompanyId(WebGlobal.CurrentUser.CompanyId)
                .Select(a => new SelectListItem() { Text = a.Name, Value = a.Id.ToString() }).ToCollection();

            ViewBag.LedgerAccounts = LedgerAccount.GetByCompany(WebGlobal.CurrentUser.CompanyId)
                .Select(a => new SelectListItem() { Text = (a.AccountNumber + " - " + a.Name), Value = a.Id.ToString() })
                .Prepend(new SelectListItem() {  Text = "(Uncategorized)", Value = "0" }).ToCollection();

            return PartialView("RateItem", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Details(RateItemModel model)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");
            if (!ModelState.IsValid) return await Details(model.Id);

            var ri = model.Id > 0 
                ? await RateItemModel.MapAsync(model, await RateItem.GetByIdWithoutCacheAsync(model.Id))
                : await RateItemModel.MapAsync(model, new RateItem
                {
                    CompanyId = WebGlobal.CurrentUser.CompanyId
                });

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(ri.CompanyId))
                throw new TowbookException("Access Denied");


            if (ri.Predefined != null && ri.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE)
                ri.CategoryId = 5;

            if (ri.Predefined != null && ri.Predefined.Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
            {
                ri.Measurement = RateItem.MeasurementEnum.Hours;
                ri.CategoryId = 5;
                ri.RateType = RateItem.RateTypeEnum.FixedCalculatedRate;
                ri.MaximumQuantity = 4;
                ri.TimeStartAtStatusId = null;
                ri.TimeStopAtStatusId = null;
            }

            await ri.Save(() => { RateItemModel.SaveExtendedRateItems(model, ri, WebGlobal.CurrentUser); });

            if (WebGlobal.CurrentUser.Company.State == "MA")
            {
                RateItemKeyValue.InsertOrUpdate(ri.CompanyId, ri.Id, Provider.Towbook.ProviderId, "RegulatedCharge",
                    model.RegulatedCharge.GetValueOrDefault() ? "1" : "");
            }


            #region save exclude fuel surchase option

            if (model.ExcludeFuelSurcharge)
            {
                RateItemExclusion rie = await RateItemExclusion.GetByRateItemAsync(ri.Id, Surcharge.SURCHARGE_FUEL);

                if (rie == null)
                {
                    rie = new RateItemExclusion();
                    rie.RateItemId = ri.Id;
                    rie.SurchargeId = Surcharge.SURCHARGE_FUEL;
                    await rie.Save();
                }
            }
            else
            {
                RateItemExclusion rie = await RateItemExclusion.GetByRateItemAsync(ri.Id, Surcharge.SURCHARGE_FUEL);

                if (rie != null)
                    await rie.Delete();
            }
            #endregion

            if (!model.TieredStorageRates.GetValueOrDefault())
            {
                var ratesToRemove = RateItem.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).Where(o => o.ParentRateItemId == ri.RateItemId);

                foreach (var r in ratesToRemove)
                {
                    RateItem rr = (r as RateItem);
                    if (rr != null)
                        await rr.Delete(WebGlobal.CurrentUser);
                }
            }

            Response.Headers.Add("X-Twbk-ResourceId", ri.Id.ToString());
            Response.Headers.Add("X-Twbk-Redirect", Url.Action("List"));
            return Content("");
        }

        [HttpGet]
        public async Task<PartialViewResult> New(int? predefinedId = 0)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");
            Response.Headers.Add("X-Twbk-Title", "New Service Rate");
            RateItemModel model = await RateItemModel.MapAsync(new RateItem());

            if (predefinedId != null && predefinedId != 0)
            {
                var r = new RateItem()
                {
                    CompanyId = WebGlobal.CurrentUser.CompanyId,
                    Predefined = await PredefinedRateItem.GetByIdAsync(predefinedId.Value)
                };

                if (r.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE)
                {
                    r.CategoryId = 5;
                }

                if (r.Predefined.Id == PredefinedRateItem.BUILTIN_STORAGE_HOURLYRATE)
                {
                    r.CategoryId = 5;
                    r.TimeRound = RateItem.TimeRoundEnum.Actual;
                    r.RateType = RateItem.RateTypeEnum.FixedCalculatedRate;
                    r.MaximumQuantity = 4;
                    r.Measurement = RateItem.MeasurementEnum.Hours;
                }

                model = await RateItemModel.MapAsync(r);
            }

            model.Categories = new SelectList(await RateItemCategory.GetByCompanyAsync(WebGlobal.CurrentUser.CompanyId), "Id", "Name");

            model.Statuses = new SelectList(Extric.Towbook.Dispatch.Status.GetByCompany(WebGlobal.CurrentUser.CompanyId, Extric.Towbook.Dispatch.StatusCategory.Dispatching)
                .Where(o => o != Extric.Towbook.Dispatch.Status.Cancelled), "Id", "Name");

            ViewBag.Config = new
            {
                AccountTypes = new AccountTypesController().Get(),
                AccountTypesPlural = new AccountTypesController().PluralCombined(),
                Vehicle = new
                {
                    Types = await new BodyTypesController().Get()
                }

            }.ToJson();

            ViewBag.Accounts = (await AccountsController.InternalGetAccountsMinimalAsync()).ToJson();

            model.TieredStorageRates = false;
            ViewBag.AccountRateItems = new Collection<int>().ToJson();
            ViewBag.ChildRateItems = new Collection<int>().ToJson();

            var sc = await SurchargeRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, WebGlobal.CurrentUser.CompanyId);

            if (sc != null && sc.TreatExclusionsAsInclusions)
                ViewBag.ExcludeFuelSurcharge = "Charge Fuel Surcharge";
            else
                ViewBag.ExcludeFuelSurcharge = "Prevent Fuel Surcharges";

            ViewBag.HasFuelSurcharges = (sc != null);

            var ckvalue = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "LimitPricingOptionsInEditorToAccountPricingOnly");
            ViewBag.LimitToAccountPricingOption = ckvalue == "1";

            ViewBag.InclusionType = false;

            ViewBag.ChargeClasses = ChargeClass.GetByCompanyId(WebGlobal.CurrentUser.CompanyId)
                .Select(a => new SelectListItem() { Text = a.Name, Value = a.Id.ToString() }).ToCollection();

            ViewBag.LedgerAccounts = LedgerAccount.GetByCompany(WebGlobal.CurrentUser.CompanyId)
                .Select(a => new SelectListItem() { Text = (a.AccountNumber + " - " + a.Name), Value = a.Id.ToString() })
                .Prepend(new SelectListItem() { Text = "(Uncategorized)", Value = "0" }).ToCollection();

            return PartialView("RateItem", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Delete(RateItemModel model)
        {
            if (!IsAccessAllowed())
            {
                throw new TowbookException("Your user account doesn't have access to perform this action.");
            }

            var ric = new Extric.Towbook.API.Controllers.RateItemsController();
            await ric.Delete(model.Id);

            return RedirectToAction("Index");
        }

        [HttpGet]
        public async Task<PartialViewResult> Storage()
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Storage Calculation");

            var model = new StorageRateModel();


            StorageRate storageRate = await StorageRate.GetByCompanyIdAsync(WebGlobal.CurrentUser.Company.Id);

            if (storageRate != null)
            {
                storageRate.AccountId = model.AccountId;
                model = StorageRateModel.MapDomainObjectToModel(storageRate);
            }

            model.Accounts = new List<Extric.Towbook.Accounts.Account>();
            var emptyAccount = new Extric.Towbook.Accounts.Account()
            {
                Company = WebGlobal.CurrentUser.Company.Name + " - Default"
            };
            model.Accounts.Add(emptyAccount);
            model.AccountId = emptyAccount.Id;
            model.Accounts.AddRange(await Extric.Towbook.Accounts.Account.GetByCompanyAsync(WebGlobal.CurrentUser.Company));
            model.AccountName = Core.HtmlEncode(model.AccountName);
                
            model.Accounts = model.Accounts.Select(a => {
                a.FullName = Core.HtmlEncode(a.FullName);
                a.Company = Core.HtmlEncode(a.Company);
                return a;
            }).ToList();
            return PartialView("StorageItem", model);
        }

        /// Updates the posted object
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> UpdateStorage([FromBody] StorageRateModel model)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                StorageRate storageRate = new StorageRate();

                if (model.CompanyId > 1)
                {
                    await ThrowIfNoCompanyAccessAsync(model.CompanyId);
                }

                if (model.AccountId == -1) // Company level configuration
                {
                    storageRate = await StorageRate.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);
                }
                else
                {
                    storageRate = await StorageRate.GetByAccountIdAsync(WebGlobal.CurrentUser.CompanyId, model.AccountId);
                }
                
                if (storageRate == null)
                {
                    storageRate = new StorageRate();
                    storageRate.CompanyId = WebGlobal.CurrentUser.CompanyId;
                    storageRate.AccountId = model.AccountId;
                }

                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(storageRate.CompanyId))
                    throw new TowbookException("Access Denied");

                StorageRateModel.MapModelToDomainObject(model, storageRate).Save();

                return Content("");
            }
         
            return View(model);
        }

        [HttpGet]
        public PartialViewResult GetAccountStorageRateConfiguration(int accountId)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var model = new StorageRateModel();

            StorageRate storageRate = new StorageRate();

            if (accountId == -1)
            {
                storageRate = StorageRate.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);
                if (storageRate == null)
                    storageRate = new StorageRate();
            }
            else
            {
                //TODO can be async
                storageRate = StorageRate.GetByAccountId(WebGlobal.CurrentUser.CompanyId, accountId);
            }

            if (storageRate == null)
            {
                storageRate = StorageRate.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);

                if (storageRate == null)
                    storageRate = new StorageRate();

                model = StorageRateModel.MapDomainObjectToModel(storageRate);
                model.Id = 0;
            }
            else
            {
                model = StorageRateModel.MapDomainObjectToModel(storageRate);
            }

            model.AccountId = accountId;

            return PartialView("StorageRateGracePeriodDetails", model);
        }

        [HttpGet]
        public async Task<IActionResult> CommissionsAsync(int? id, int? driverId = null)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (!id.HasValue)
            {
                return await CommissionsListAsync(driverId.GetValueOrDefault());
            }
            else
            {
                if (driverId == -1 || driverId == null) 
                    driverId = 0;

                var r = await RateItem.GetByIdAsync(id.Value);

                if (r == null || (r.RateItemId != -1 && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(r.CompanyId)))
                    throw new TowbookException("Access denied");

                var c = await CommissionRateItem.GetByRateItemAsync(id.Value, driverId.GetValueOrDefault(), null, WebGlobal.CurrentUser.CompanyId);
                var ce = await CommissionRateItemBodyType.GetByRateItemAsync(id.Value, driverId.GetValueOrDefault(), null, WebGlobal.CurrentUser.CompanyId);

                // default commission rate (for all drivers)
                decimal dcr = WebGlobal.CurrentUser.Company.StandardDriverCommission;

                // default driver commission rate for THIS driver
                decimal ddcr = -1; 

                var driver = (driverId != 0 ? await Driver.GetByIdAsync(driverId.Value) : null);

                if (driverId > 0)
                {
                    if (driver != null && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(driver.Companies))
                        throw new TowbookException("Access denied");

                    decimal? cr = DriverModel.Map(driver).CommissionRate;
                    if(cr != null)
                        ddcr = cr.Value;
                }



                // These will be the placeholder values for the rate item (and possible driverId overrides)
                var extendedCommissionPlaceholders = new List<CommissionRateExtendedModel>();

                // get specific default rates for the company and drivers
                var all = (await CommissionRateItemBodyType.GetAllDefaultByCompanies(new int[] { WebGlobal.CurrentUser.CompanyId }.ToArray())).ToList();

                // create placeholders for each body type providing the inherited value for the rateItem and possible driverId
                var bodyTypes = await BodyType.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);

                foreach (var bt in bodyTypes)
                {
                    var btm = new CommissionRateExtendedModel() {
                        BodyTypeId = bt.Id,
                        Name = bt.Name
                    };

                    if (ce.ContainsKey(bt.Id))
                    {
                        var ced = ce[bt.Id];
                        btm.Value = ced.Type == CommissionType.FlatRate ? ced.FlatRate.ToString("$0.00") : string.Format("{0:0.00}%", ced.Percentage);
                    }
                    else
                    {
                        string placeholder = string.Format("{0:0.00}%", 0.0M);

                        if (c != null) // specific base driver commission rate always is the default
                            placeholder = c.Type == CommissionType.FlatRate ? c.FlatRate.ToString("$0.00") : string.Format("{0:0.00}%", c.Percentage);
                        else
                        {
                            // allow inheritance to generate placholder value
                            var allBodyTypeDefaults = all.Where(w => w.BodyType != null && w.BodyType.Id == bt.Id);
                            if (allBodyTypeDefaults.Any())
                            {
                                var driverDefault = allBodyTypeDefaults.Where(w => w.DriverId > 0 && w.DriverId == driverId.GetValueOrDefault() && w.RateItem == null).FirstOrDefault();
                                var companyDefault = allBodyTypeDefaults.FirstOrDefault(w => w.DriverId == 0 && w.RateItem == null);

                                if (driverDefault != null)
                                    placeholder = driverDefault.Type == CommissionType.FlatRate ? driverDefault.FlatRate.ToString("$0.00") : string.Format("{0:0.00}%", driverDefault.Percentage);
                                else if (companyDefault != null)
                                    placeholder = companyDefault.Type == CommissionType.FlatRate ? companyDefault.FlatRate.ToString("$0.00") : string.Format("{0:0.00}%", companyDefault.Percentage);
                                else
                                    placeholder = string.Format("{0:0.00}%", ddcr != -1 ? ddcr : dcr);
                            }
                            else
                                placeholder = string.Format("{0:0.00}%", ddcr != -1 ? ddcr : dcr);
                        }

                        // use inherited value
                        btm.Value = placeholder;
                    }

                    extendedCommissionPlaceholders.Add(btm);
                }

                ViewBag.driverId = driverId.GetValueOrDefault();
                ViewBag.defaultDriverCommissionRate = ddcr == -1 ? (decimal?) null : ddcr;
                ViewBag.defaultCompanyRate = dcr;
                ViewBag.defaultCommissionRateExtended = extendedCommissionPlaceholders;

                var crm = new CommissionRateModel();

                crm.Name = r.Name;
                crm.RateItemId = r.RateItemId;

                if (c != null)
                {
                    crm.CommissionValue = c.ToString();
                    crm.DriverId = c.Driver != null ? c.Driver.Id : 0;
                    crm.Id = c.Id;
                }

                var csGroups = new List<CommissionScheduleGroupModel>();
                var companyCommissionGroups = CommissionScheduleGroup.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);

                IEnumerable<CommissionRateItem> companyCommissionGroupValues = null;
                if (driverId == null || driverId == 0)
                    companyCommissionGroupValues = await CommissionRateItem.GetGroupValuesByCompanyId(WebGlobal.CurrentUser.CompanyId, id.Value);
                else
                    companyCommissionGroupValues = await CommissionRateItem.GetGroupValuesByDriverId(WebGlobal.CurrentUser.CompanyId, driverId.Value, id.Value);

                foreach (var ccg in companyCommissionGroups)
                {
                    var stdGroupValue = companyCommissionGroupValues.Where(o => o.CommissionScheduleGroupId == ccg.CommissionScheduleGroupId).FirstOrDefault();
                    decimal? stdValue = null;
                    int? stdGroupValueId = null;

                    if (stdGroupValue != null)
                    {
                        stdValue = stdGroupValue.Percentage != 0 ? stdGroupValue.Percentage : stdGroupValue.FlatRate;
                        stdGroupValueId = stdGroupValue.Id;
                    }

                    csGroups.Add(new CommissionScheduleGroupModel()
                    {
                        Description = ccg.Description,
                        CommissionScheduleGroupId = ccg.CommissionScheduleGroupId,
                        CommissionScheduleGroupValueId = stdGroupValueId,
                        Value = ((stdValue != null) ? stdGroupValue.Type == CommissionType.FlatRate ? stdValue.Value.ToString("$0.00") : string.Format("{0:0.00}%", stdValue.Value) : null)
                    });
                }

                crm.CommissionScheduleGroups = csGroups;

                var extendedRates = new List<CommissionRateExtendedModel>();
                var erTemp = await CommissionRateExtendedModel.MapFromDomainObjectList(ce, WebGlobal.CurrentUser);
                var erWithGroups = await CommissionRateItemBodyType.GetByRateItemWithGroups(id.Value, driverId.GetValueOrDefault());

                foreach (var erItem in erTemp)
                {
                    extendedRates.Add(erItem);
                    extendedRates[extendedRates.Count - 1].CommissionScheduleGroups = new List<CommissionScheduleGroupModel>();

                    foreach (var ccg in companyCommissionGroups)
                    {
                        var stdGroupValue = erWithGroups.Where(o => o.CommissionScheduleGroupId == ccg.CommissionScheduleGroupId && o.BodyType.Id == erItem.BodyTypeId).FirstOrDefault();
                        decimal? stdValue = null;
                        int? stdGroupValueId = null;

                        if (stdGroupValue != null)
                        {
                            stdValue = stdGroupValue.Percentage != 0 ? stdGroupValue.Percentage : stdGroupValue.FlatRate;
                            stdGroupValueId = stdGroupValue.Id;
                        }

                        extendedRates[extendedRates.Count - 1].CommissionScheduleGroups.Add(new CommissionScheduleGroupModel()
                        {
                            Description = ccg.Description,
                            CommissionScheduleGroupId = ccg.CommissionScheduleGroupId,
                            CommissionScheduleGroupValueId = stdGroupValueId,
                            Value = ((stdValue != null) ? stdGroupValue.Type == CommissionType.FlatRate ? stdValue.Value.ToString("$0.00") : string.Format("{0:0.00}%", stdValue.Value) : null)
                        });
                    }
                }

                crm.ExtendedRates = extendedRates;

                if (driver != null)
                {
                    Response.Headers["X-Twbk-Title"] = "Modify " + WebUtility.HtmlEncode(driver.Name) + "'s Commission for " +
                       WebUtility.HtmlEncode(crm.Name);
                }
                else
                {
                    Response.Headers["X-Twbk-Title"] = "Modify Commission for " + WebUtility.HtmlEncode(crm.Name);
                }

                return PartialView("CommissionEditor", crm);
            }
        }

        [HttpPost]
        [ResponseCache(NoStore = true, Duration = 0, VaryByQueryKeys = new[] { "*" })]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Commissions(CommissionRateModel model)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var chkD = await Driver.GetByIdAsync(model.DriverId);
            if (chkD != null && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(chkD.Companies))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var chkRi = await RateItem.GetByIdAsync(model.RateItemId);

            if (chkRi != null && chkRi.CompanyId > 0 && !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(chkRi.CompanyId))
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (!ModelState.IsValid)
            {
                return await CommissionsAsync(model.Id);
            }

            var c = await CommissionRateItem.GetByRateItemAsync(model.RateItemId, model.DriverId, null,
                WebGlobal.CurrentUser.CompanyId);

            var e = await CommissionRateItemBodyType.GetByRateItemAsync(model.RateItemId, model.DriverId, null,
                WebGlobal.CurrentUser.CompanyId);

            if (model.DriverId > 0)
            {
                var toRemove = new List<int>();
                foreach (var x in e.Keys)
                {
                    if (e[x].DriverId != model.DriverId)
                    {
                        toRemove.Add(x);
                    }
                }

                foreach (var y in toRemove)
                {
                    if (e.ContainsKey(y))
                        e.Remove(y);
                }
            }

            if (model.DriverId > 0 && c != null)
            {
                if (c.Driver == null || c.Driver.Id != model.DriverId)
                    c = null;
            }

            if (c == null)
            {
                c = new CommissionRateItem() { RateItem = await RateItem.GetByIdAsync(model.RateItemId) };

                if (model.DriverId > 0)
                    c.Driver = await Driver.GetByIdAsync(model.DriverId);

                if (c.Driver != null)
                {
                    if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(c.Driver.Companies))
                        throw new TowbookException("Access denied to Driver");
                }

                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(c.RateItem.CompanyId) &&
                    c.RateItem.CompanyId > 0)
                    throw new TowbookException("Access denied to RateItem");
            }

            if (e == null)
            {
                e = new Dictionary<int, CommissionRateItemBodyType>();
            }

            if (c.RateItem == null || c.RateItem.Id < 1)
            {
                // make sure companyId is set if rateItem and driver are null.
                c.CompanyId = WebGlobal.CurrentUser.CompanyId;
            }

            if (!string.IsNullOrWhiteSpace(model.CommissionValue))
            {
                decimal anyValue = Math.Round(
                    Convert.ToDecimal(model.CommissionValue.Replace("%", "").Replace("$", "").Trim()), 2,
                    MidpointRounding.AwayFromZero);

                if (model.CommissionValue.StartsWith("$"))
                {
                    c.Percentage = 0;
                    c.FlatRate = anyValue;
                    c.Type = CommissionType.FlatRate;
                }
                else // if it ends with %, or no definition, treat it as %
                {
                    c.Percentage = anyValue;
                    c.FlatRate = 0;
                    c.Type = CommissionType.Percentage;
                }

                c.Save();
            }
            else
            {
                c.Delete(WebGlobal.CurrentUser);
            }

            int? driverId = null;
            if (model.DriverId > 0)
                driverId = model.DriverId;

            if (model.CommissionScheduleGroups != null)
            {
                foreach (var cg in model.CommissionScheduleGroups)
                {
                    if (string.IsNullOrEmpty(cg.Value) && cg.CommissionScheduleGroupValueId != null)
                    {
                        (await CommissionRateItem.GetById(cg.CommissionScheduleGroupValueId.Value))
                            ?.Delete(WebGlobal.CurrentUser);
                        continue;
                    }

                    if (!string.IsNullOrEmpty(cg.Value))
                    {
                        decimal tempValue = Math.Round(Convert.ToDecimal(cg.Value.Replace("%", "").Replace("$", "").Trim()),
                            2, MidpointRounding.AwayFromZero);

                        CommissionRateItem item = null;
                        if (cg.CommissionScheduleGroupValueId != null)
                            item = await CommissionRateItem.GetById(cg.CommissionScheduleGroupValueId.Value);
                        else
                            item = new CommissionRateItem();

                        Driver d = null;
                        if (driverId != null)
                            d = await Driver.GetByIdAsync(driverId.Value);

                        item.CompanyId = c.CompanyId;
                        item.CommissionScheduleGroupId = cg.CommissionScheduleGroupId;
                        item.Driver = d;
                        item.FlatRate = !cg.Value.EndsWith("%") ? tempValue : 0;
                        item.Percentage = cg.Value.EndsWith("%") ? tempValue : 0;
                        item.RateItem = await RateItem.GetByIdAsync(model.RateItemId);

                        item.Type = cg.Value.EndsWith("%") ? CommissionType.Percentage : CommissionType.FlatRate;
                        item.Save();
                    }
                }
            }

            #region save extended rate item prices

            foreach (CommissionRateExtendedModel crm in model.ExtendedRates)
            {
                decimal rAmount = 0;
                string sAmount = crm != null && crm.Value != null
                    ? crm.Value.Replace("%", "").Replace("$", "").Trim()
                    : "";

                decimal output = 0;

                if (!decimal.TryParse(sAmount, out output))
                {
                    output = 0;
                }

                if (!String.IsNullOrEmpty(crm.Value))
                    rAmount = Math.Round(output, 2, MidpointRounding.AwayFromZero);


                if (rAmount == 0 && sAmount != "0.00")
                {
                    if (e.ContainsKey(crm.BodyTypeId))
                    {
                        e[crm.BodyTypeId].Delete(WebGlobal.CurrentUser);
                    }
                }
                else
                {
                    if (e.ContainsKey(crm.BodyTypeId) &&
                        e[crm.BodyTypeId].Driver != null && model.DriverId > 0 &&
                        e[crm.BodyTypeId].Driver.Id != model.DriverId)
                    {
                        e.Remove(crm.BodyTypeId);
                    }

                    if (!e.ContainsKey(crm.BodyTypeId))
                    {
                        e.Add(crm.BodyTypeId, new CommissionRateItemBodyType());
                        e[crm.BodyTypeId].RateItem = c.RateItem;
                        e[crm.BodyTypeId].BodyType = await BodyType.GetByIdAsync(crm.BodyTypeId);

                        if (model.DriverId > 0)
                            e[crm.BodyTypeId].Driver = await Driver.GetByIdAsync(model.DriverId);
                    }


                    if (crm.Value.StartsWith("$"))
                    {
                        e[crm.BodyTypeId].Type = CommissionType.FlatRate;
                        e[crm.BodyTypeId].FlatRate = rAmount;
                        e[crm.BodyTypeId].Percentage = 0;
                    }
                    else
                    {
                        e[crm.BodyTypeId].Type = CommissionType.Percentage;
                        e[crm.BodyTypeId].FlatRate = 0;
                        e[crm.BodyTypeId].Percentage = rAmount;
                    }

                    e[crm.BodyTypeId].CompanyId = c.CompanyId;
                    e[crm.BodyTypeId].Save();

                    if (crm.CommissionScheduleGroups != null)
                    {
                        decimal tempValue = 0;
                        foreach (var cg in crm.CommissionScheduleGroups)
                        {
                            if (string.IsNullOrEmpty(cg.Value) && cg.CommissionScheduleGroupValueId != null)
                            {
                                new CommissionRateItemBodyType(cg.CommissionScheduleGroupValueId.Value).Delete(
                                    WebGlobal.CurrentUser);
                                continue;
                            }

                            if (!string.IsNullOrEmpty(cg.Value))
                            {
                                tempValue = Math.Round(
                                    Convert.ToDecimal(cg.Value.Replace("%", "").Replace("$", "").Trim()), 2,
                                    MidpointRounding.AwayFromZero);

                                CommissionRateItemBodyType item = null;
                                if (cg.CommissionScheduleGroupValueId != null)
                                    item = new CommissionRateItemBodyType(cg.CommissionScheduleGroupValueId.Value);
                                else
                                    item = new CommissionRateItemBodyType();

                                Driver d = null;
                                if (driverId != null)
                                    d = await Driver.GetByIdAsync(driverId.Value);

                                item.CommissionScheduleGroupId = cg.CommissionScheduleGroupId;
                                item.Driver = d;
                                item.FlatRate = !cg.Value.EndsWith("%") ? tempValue : 0;
                                item.Percentage = cg.Value.EndsWith("%") ? tempValue : 0;
                                item.RateItem = await RateItem.GetByIdAsync(model.RateItemId);
                                item.Type = cg.Value.EndsWith("%")
                                    ? CommissionType.Percentage
                                    : CommissionType.FlatRate;
                                item.BodyType = await BodyType.GetByIdAsync(crm.BodyTypeId);
                                item.Save();
                            }
                        }
                    }
                }
            }

            #endregion


            await Extric.Towbook.Caching.CacheWorkerUtility.UpdateCommissionAll(WebGlobal.CurrentUser.Id,
                WebGlobal.CurrentUser.CompanyId);

            if (model.DriverId != 0)
                return RedirectToAction("CommissionsList", new { driverId = model.DriverId });

            return RedirectToAction("CommissionsList");
        }

        [HttpGet]
        public async Task<IActionResult> CommissionsListAsync(int? driverId)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers["X-Twbk-Title"] = "Commissions";

            var crlm = new CommissionRateListModel();

            if (driverId != null && driverId > 0)
            {
                var driver = await Driver.GetByIdAsync(driverId.Value);
                if (driver == null || 
                    !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(driver.Companies))
                {
                    return RedirectToAction("CommissionsList");
                }

                decimal? standardCommissionRate = DriverModel.Map(driver).CommissionRate;
                
                if (standardCommissionRate == null)
                    crlm.StandardCommissionRate = WebGlobal.CurrentUser.Company.StandardDriverCommission;
                else
                {
                    crlm.StandardCommissionRate = standardCommissionRate.Value;
                    ViewBag.defaultDriverCommissionRate = standardCommissionRate.Value;
                }
                
                crlm.DriverId = driverId.Value;
            }
            else
            {
                crlm.StandardCommissionRate = WebGlobal.CurrentUser.Company.StandardDriverCommission;
                crlm.DriverId = 0;
            }

            var csGroups = new List<CommissionScheduleGroupModel>();

            var companyCommissionGroups = CommissionScheduleGroup.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);

            IEnumerable<CommissionScheduleGroupStandardValue> companyCommissionGroupValues = null;
            if (driverId == null || driverId <= 0)
                companyCommissionGroupValues = CommissionScheduleGroupStandardValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);
            else
                companyCommissionGroupValues = CommissionScheduleGroupStandardValue.GetByDriverId(WebGlobal.CurrentUser.CompanyId, driverId.Value);

            foreach (var ccg in companyCommissionGroups)
            {
                var stdGroupValue = companyCommissionGroupValues.Where(o => o.CommissionScheduleGroupId == ccg.CommissionScheduleGroupId).FirstOrDefault();
                decimal? stdValue = null;
                int? stdGroupValueId = null;

                if (stdGroupValue != null)
                { 
                    stdValue = stdGroupValue.StandardDriverCommission;
                    stdGroupValueId = stdGroupValue.CommissionScheduleGroupStandardValueId;
                }

                csGroups.Add(new CommissionScheduleGroupModel()
                {
                    Description = ccg.Description,
                    CommissionScheduleGroupId = ccg.CommissionScheduleGroupId,
                    CommissionScheduleGroupValueId = stdGroupValueId,
                    Value = stdValue.ToString()
                });
            }

            crlm.CommissionScheduleGroups = csGroups;

            ViewBag.defaultCompanyRate = WebGlobal.CurrentUser.Company.StandardDriverCommission;

            crlm.Items = new List<CommissionRateModel>();

            if (driverId == -1 || driverId == null) driverId = 0;

            var dcri = await CommissionRateItem.GetByCompany(WebGlobal.CurrentUser.CompanyId, driverId.Value);
            var dcrie = await CommissionRateItemBodyType.GetByCompany(WebGlobal.CurrentUser.CompanyId, driverId.Value);

            var rateItemList = (await RateItemListAsync())
                .Where(w => w.Predefined == null || (w.Predefined != null && w.RateItemId > 0))
                .Where(w => w.DefaultClassId != 4).ToList();

            rateItemList.Insert(0, await RateItem.GetByIdAsync(-1));

            foreach (var r in rateItemList)
            {
                CommissionRateItem c = null;
                Dictionary<int, CommissionRateItemBodyType> ce = null;
                
                dcri.TryGetValue(r.RateItemId > 0 ? r.RateItemId : 0, out c);
                dcrie.TryGetValue(r.RateItemId > 0 ? r.RateItemId : 0, out ce);

                var crm = new CommissionRateModel();
                if (c != null)
                {
                    crm.CommissionValue = c.ToString();
                    crm.DriverId = c.Driver?.Id ?? 0;
                    crm.Id = c.Id;
                }
                crm.Name = r.Name;
                crm.RateItemId = r.RateItemId;
                crm.DriverId = driverId.GetValueOrDefault();

                crm.ExtendedRates = await CommissionRateExtendedModel.MapFromDomainObjectList(ce, WebGlobal.CurrentUser, "");
                crm.CommissionOutputExtendedPricesHtml = await Commission_OutputExtendedPrices(crm);
                crlm.Items.Add(crm);
            }

            var allDrivers = await new Extric.Towbook.API.Controllers.DriversController().FullAsync();
            ViewBag.AllDrivers = allDrivers
                .Prepend(DriverModel.Map(new Driver() { Id = 0, Name = Core.HtmlEncode(WebGlobal.CurrentUser.Company.Name) + " - All Drivers", Active = true }))
                .Where(w => w.IsActive())
                .Select(d => {
                    d.Name = Core.HtmlEncode(d.Name);
                    return d;
                }).ToList();

            return PartialView("CommissionList", crlm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> CommissionsList(CommissionRateListModel model)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (model.DriverId < 1)
            {
                WebGlobal.CurrentUser.Company.StandardDriverCommission = model.StandardCommissionRate.GetValueOrDefault();
                await WebGlobal.CurrentUser.Company.Save();
                await Extric.Towbook.Caching.CacheWorkerUtility.UpdateCompanyBaseCommission(WebGlobal.CurrentUser.Company);
            }
            else
            {
                var driver = await Driver.GetByIdAsync(model.DriverId);
                driver.CommissionRate = model.StandardCommissionRate;
                await driver.Save(WebGlobal.CurrentUser);
                await Extric.Towbook.Caching.CacheWorkerUtility.UpdateDriverBaseCommission(driver);
            }

            int? driverId = null;
            if (model.DriverId > -1)
                driverId = model.DriverId;

            if (model.CommissionScheduleGroups != null)
            {
                foreach (var cg in model.CommissionScheduleGroups)
                {
                    if (cg.Value == null && cg.CommissionScheduleGroupValueId != null)
                    {
                        CommissionScheduleGroupStandardValue.GetById(cg.CommissionScheduleGroupValueId.Value).Delete();
                        continue;
                    }

                    if (cg.Value != null)
                        new CommissionScheduleGroupStandardValue()
                        {
                            CommissionScheduleGroupId = cg.CommissionScheduleGroupId,
                            DriverId = driverId,
                            CommissionScheduleGroupStandardValueId = cg.CommissionScheduleGroupValueId != null ? cg.CommissionScheduleGroupValueId.Value : 0,
                            StandardDriverCommission = Convert.ToDecimal(cg.Value)
                        }.Save();
                }
            }

            if (model.DriverId < 1)
                return RedirectToAction("CommissionsList", new { @driverId = model.DriverId });

            return RedirectToAction("CommissionsList");
        }


        public static async Task<string> OutputExtendedPrices(object data)
        {
            RateItem X = (Extric.Towbook.RateItem)data;
            Dictionary<int, IExtendedRateItem> xi = X.ExtendedRateItems;

            string retval = "";
            string colTemp = "<td class=\"price\">{0}</td>";

            retval += String.Format("<td class=\"price\">{0}</td>", (X.Cost != 0 ? X.Cost.ToString("C") : "-"));
            decimal price = X.Cost;

            var bodyTypes = await BodyType.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);

            foreach (var t in bodyTypes)
            {
                if (xi.ContainsKey(t.Id))
                {
                    retval += String.Format(colTemp, xi[t.Id].Amount.ToString("C"));
                }
                else
                {
                    if (price != 0)
                        retval += String.Format(colTemp, "<span style=\"color: #afafaf\">" + price.ToString("C") + "</span>");
                    else
                        retval += String.Format(colTemp, "-");
                }
            }
            return retval;
        }

        private Dictionary<int, CommissionRateItemBodyType> standardCommission;
        public async Task<string> Commission_OutputExtendedPrices(CommissionRateModel data)
        {
            if (standardCommission == null)
            {
                standardCommission = await CommissionRateItemBodyType.GetByRateItemAsync(0,
                    data.DriverId, null, WebGlobal.CurrentUser.CompanyId);


                foreach (var pair in await CommissionRateItemBodyType.GetByRateItemAsync(0,
                   0, null, WebGlobal.CurrentUser.CompanyId))
                {
                    if (!standardCommission.ContainsKey(pair.Key))
                        standardCommission.Add(pair.Key, pair.Value);
                }
            }

            string retval = "";
            string colTemp = "<td data-bodyType=\"{0}\">{1}</td>\r\n";
            string colTempLight = "<td data-bodyType=\"{0}\" class=\"commission-inherit\">{1}</td>\r\n";
            string inheritBase = "<td class=\"commission-inherit-base\">-</td>\r\n";

            if (!string.IsNullOrEmpty(data.CommissionValue))
                retval += String.Format("<td>{0}</td>", data.CommissionValue);
            else
                retval += inheritBase;

            foreach (var t in data.ExtendedRates)
            {
                if (!string.IsNullOrEmpty(t.Value))
                {
                    retval += string.Format(colTemp, t.BodyTypeId, t.Value);
                }
                else
                {
                    if (!string.IsNullOrEmpty(data.CommissionValue))
                    {
                        retval += string.Format(colTempLight, t.BodyTypeId, data.CommissionValue);
                    }
                    else
                    {
                        if (standardCommission.ContainsKey(t.BodyTypeId))
                            retval += string.Format(colTempLight, t.BodyTypeId,
                                standardCommission[t.BodyTypeId].GetValue());
                        else
                        {
                            retval += inheritBase;
                        }
                    }
                }
            }
            return retval;
        }


        #region Fuel Surcharges

        [HttpGet]
        public async Task<ActionResult> FuelSurcharges()
        {
            Response.Headers.Add("X-Twbk-Title", "Fuel Surcharge");

            FuelSurchargeModel model = new FuelSurchargeModel();

            SurchargeRate sr = await SurchargeRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL,
                Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.Id);

            if (sr != null)
            {
                model.FuelSurcharge = true;
                model.FuelSurchargeRate = Math.Round((sr.Rate * 100), 2);
                model.TreatExclusionsAsInclusions = sr.TreatExclusionsAsInclusions;
                model.Taxable = sr.Taxable;
                model.ApplyToCustomInvoiceItems = sr.ApplyToCustomInvoiceItems;
                
            }
            else
            {
                model.FuelSurcharge = false;
                model.TreatExclusionsAsInclusions = false;
                model.Taxable = false;
                
            }

            return PartialView("FuelSurcharges", model);
        }

        [HttpPost]
        public async Task FuelSurcharges(FuelSurchargeModel model)
        {
            SurchargeRate sr = await SurchargeRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, WebGlobal.CurrentUser.Company.Id);

            if (ModelState.IsValid)
            {
                if (model.FuelSurcharge)
                {
                    if (sr == null)
                    {
                        sr = new SurchargeRate()
                        {
                            SurchargeId = Surcharge.SURCHARGE_FUEL,
                            CompanyId = WebGlobal.CurrentUser.Company.Id
                        };
                    }

                    sr.Rate = (model.FuelSurchargeRate / 100);

                    if (model.TreatExclusionsAsInclusions != null)
                        sr.TreatExclusionsAsInclusions = model.TreatExclusionsAsInclusions.Value;

                    if (model.Taxable != null)
                        sr.Taxable = model.Taxable.Value;

                    await sr.Save();
                }
                else
                {
                    if (sr != null)
                        await sr.Delete();
                }
            }
        }

        #endregion

        [HttpGet]
        public PartialViewResult CommissionScheduleGroups()
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            CommissionScheduleGroupListModel result = new CommissionScheduleGroupListModel();
            result.CompanyId = WebGlobal.CurrentUser.CompanyId;
            result.Groups = new List<CommissionScheduleGroupListItem>();

            var groups = CommissionScheduleGroup.GetByCompanyId(result.CompanyId);
            foreach (var g in groups)
            {
                var cg = new CommissionScheduleGroupListItem()
                {
                    CommissionScheduleGroupId = g.CommissionScheduleGroupId,
                    CreateDate = g.CreateDate.Value,
                    Description = g.Description,
                    OwnerUserId = g.OwnerUserId
                };

                var times = CommissionScheduleGroupTime.GetByGroupId(g.CommissionScheduleGroupId);
                foreach (var t in times)
                {
                    cg.Times.Add(new CommissionScheduleGroupTimeModel()
                        {
                            EndTime = t.EndTime,
                            StartTime = t.StartTime,
                            WeekDay = Convert.ToInt32(t.WeekDay)
                        });
                }

                result.Groups.Add(cg);
            }

            return PartialView("CommissionScheduleGroups", result);
        }

        [HttpGet]
        public PartialViewResult EditSchedule(int? id)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var model = new CommissionScheduleGroupEditorModel();

            if (id != null && id != 0)
            {
                var cg = CommissionScheduleGroup.GetById(id.Value);
                if (cg == null)
                    throw new TowbookException("No commission schedule group found!");

                var cgt = CommissionScheduleGroupTime.GetByGroupId(cg.CommissionScheduleGroupId);

                model.CommissionScheduleGroupId = cg.CommissionScheduleGroupId;
                model.CompanyId = cg.CompanyId;
                model.Description = cg.Description;
                
                model.SundayStartTime = cgt.Where(o => o.WeekDay == DayOfWeek.Sunday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Sunday).FirstOrDefault().StartTime.ToString(@"hh\:mm") : "0";
                model.SundayEndTime = cgt.Where(o => o.WeekDay == DayOfWeek.Sunday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Sunday).FirstOrDefault().EndTime.ToString(@"hh\:mm") : "0";
                model.MondayStartTime = cgt.Where(o => o.WeekDay == DayOfWeek.Monday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Monday).FirstOrDefault().StartTime.ToString(@"hh\:mm") : "0";
                model.MondayEndTime = cgt.Where(o => o.WeekDay == DayOfWeek.Monday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Monday).FirstOrDefault().EndTime.ToString(@"hh\:mm") : "0";
                model.TuesdayStartTime = cgt.Where(o => o.WeekDay == DayOfWeek.Tuesday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Tuesday).FirstOrDefault().StartTime.ToString(@"hh\:mm") : "0";
                model.TuesdayEndTime = cgt.Where(o => o.WeekDay == DayOfWeek.Tuesday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Tuesday).FirstOrDefault().EndTime.ToString(@"hh\:mm") : "0";
                model.WednesdayStartTime = cgt.Where(o => o.WeekDay == DayOfWeek.Wednesday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Wednesday).FirstOrDefault().StartTime.ToString(@"hh\:mm") : "0";
                model.WednesdayEndTime = cgt.Where(o => o.WeekDay == DayOfWeek.Wednesday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Wednesday).FirstOrDefault().EndTime.ToString(@"hh\:mm") : "0";
                model.ThursdayStartTime = cgt.Where(o => o.WeekDay == DayOfWeek.Thursday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Thursday).FirstOrDefault().StartTime.ToString(@"hh\:mm") : "0";
                model.ThursdayEndTime = cgt.Where(o => o.WeekDay == DayOfWeek.Thursday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Thursday).FirstOrDefault().EndTime.ToString(@"hh\:mm") : "0";
                model.FridayStartTime = cgt.Where(o => o.WeekDay == DayOfWeek.Friday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Friday).FirstOrDefault().StartTime.ToString(@"hh\:mm") : "0";
                model.FridayEndTime = cgt.Where(o => o.WeekDay == DayOfWeek.Friday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Friday).FirstOrDefault().EndTime.ToString(@"hh\:mm") : "0";
                model.SaturdayStartTime = cgt.Where(o => o.WeekDay == DayOfWeek.Saturday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Saturday).FirstOrDefault().StartTime.ToString(@"hh\:mm") : "0";
                model.SaturdayEndTime = cgt.Where(o => o.WeekDay == DayOfWeek.Saturday).Any() ? cgt.Where(o => o.WeekDay == DayOfWeek.Saturday).FirstOrDefault().EndTime.ToString(@"hh\:mm") : "0";

                return PartialView("CommissionScheduleGroupEditor", model);
            }

            return PartialView("CommissionScheduleGroupEditor", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditSchedule(CommissionScheduleGroupEditorModel model)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");
            
            var g = new CommissionScheduleGroup();

            if (model.CommissionScheduleGroupId == 0)
            {
                g.CompanyId = WebGlobal.CurrentUser.CompanyId;
                g.OwnerUserId = WebGlobal.CurrentUser.Id;
            }
            else
            { 
                g = CommissionScheduleGroup.GetById(model.CommissionScheduleGroupId);

                foreach (var t in CommissionScheduleGroupTime.GetByGroupId(g.CommissionScheduleGroupId))
                    t.Delete();
            }

            g.Description = model.Description;
            g.Save();

            CreateCommissionGroupTime(g.CommissionScheduleGroupId, model.SundayStartTime, model.SundayEndTime, DayOfWeek.Sunday);
            CreateCommissionGroupTime(g.CommissionScheduleGroupId, model.MondayStartTime, model.MondayEndTime, DayOfWeek.Monday);
            CreateCommissionGroupTime(g.CommissionScheduleGroupId, model.TuesdayStartTime, model.TuesdayEndTime, DayOfWeek.Tuesday);
            CreateCommissionGroupTime(g.CommissionScheduleGroupId, model.WednesdayStartTime, model.WednesdayEndTime, DayOfWeek.Wednesday);
            CreateCommissionGroupTime(g.CommissionScheduleGroupId, model.ThursdayStartTime, model.ThursdayEndTime, DayOfWeek.Thursday);
            CreateCommissionGroupTime(g.CommissionScheduleGroupId, model.FridayStartTime, model.FridayEndTime, DayOfWeek.Friday);
            CreateCommissionGroupTime(g.CommissionScheduleGroupId, model.SaturdayStartTime, model.SaturdayEndTime, DayOfWeek.Saturday);

            return RedirectToAction("CommissionScheduleGroups");
        }

        private void CreateCommissionGroupTime(int groupId, string startTime, string endTime, DayOfWeek weekDay)
        {
            if (startTime != "0" && endTime != "0")
            {
                CommissionScheduleGroupTime nt = new CommissionScheduleGroupTime();
                nt.CommissionScheduleGroupId = groupId;
                nt.StartTime = TimeSpan.ParseExact(startTime, "g", null);
                nt.EndTime = TimeSpan.ParseExact(endTime, "g", null);
                nt.WeekDay = weekDay;
                nt.Save();
            }
        }

        public ActionResult DeleteSchedule(CommissionScheduleGroupTime model)
        {
            if (model.CommissionScheduleGroupId != 0)
            {
                CommissionScheduleGroup g = CommissionScheduleGroup.GetById(model.CommissionScheduleGroupId);
                if (g != null)
                    g.Delete();
            }

            return RedirectToAction("CommissionScheduleGroups");
        }

        public static async Task<string> GetSurchargeLabelAsync(int id)
        {
            // Get what 'checked' means - whether it means preventing or charging fuel surcharges
            var preventingSurcharges = true;
            var sc = await SurchargeRate.GetBySurchargeAsync(Surcharge.SURCHARGE_FUEL, WebGlobal.CurrentUser.CompanyId);

            if (sc == null || sc.Rate == 0)
                return "";

            if (sc != null && sc.TreatExclusionsAsInclusions)
                preventingSurcharges = false;

            // If this is not a new rate item
            if (id > -1)
            {
                var ri = await RateItem.GetByIdAsync(id);
                if (ri != null)
                {
                    // Get if this rate item's fuel surcharge is 'checked'
                    var isChecked = false;
                    if (ri.Id > 0 && await RateItemExclusion.GetByRateItemAsync(ri.Id, Surcharge.SURCHARGE_FUEL) != null)
                        isChecked = true;

                    var hasFuelSurcharge = isChecked;
                    if (preventingSurcharges)
                        hasFuelSurcharge = !isChecked;

                    return hasFuelSurcharge ? "FUEL SURCHARGE" : "";
                }
            }

            // If all else fails, return the default value for 'unchecked'
            return preventingSurcharges ? "FUEL SURCHARGE" : "";
        }
    }
}
