using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using tb = Extric.Towbook;
using fmt = Extric.Towbook.Integrations.Fleetmatics;
using Extric.Towbook.Integration;
using Extric.Towbook;
using Ajax.Areas.Settings.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using static Ajax.AjaxUtility;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class FleetmaticsController : Controller
    {
        //
        // GET: /Settings/Fleetmatics/

        /// <summary>
        /// Gets the view for Fleetmatics settings page.
        /// Is there's no configuration, or no data on Truck/Vehicle matches, it doesn't display 
        /// </summary>
        /// <returns>The Fleetmatics view</returns>
        [HttpGet]
        public ActionResult Index()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var companyId = WebGlobal.CurrentUser.CompanyId;

            var model = new FleetmaticsSettingsModel();

            var userLogin = GetUserLogin(companyId);
            if (userLogin != null)
                model.UserLogin = userLogin.Value;
            else
                model.UserLogin = string.Empty;

            var xmlCode = GetUserPassword(companyId);
            if (xmlCode != null)
                model.XmlCode = xmlCode.Value;
            else
                model.XmlCode = string.Empty;

            ViewBag.IsConnected = false;
            if ((userLogin != null && !String.IsNullOrEmpty(userLogin.Value)) && (xmlCode != null && !String.IsNullOrEmpty(xmlCode.Value)))
            {
                ViewBag.IsConnected = true;
                model.Matches = GenerateMatchesModel();
            }
            else
            {
                model.Matches = new FleetmaticTruckVehicleMatchesModel()
                {
                    Trucks = new List<tb.Truck>(),
                    Vehicles = new List<string>(),
                    Matches = new List<Extric.Towbook.Integration.TruckKeyValue>()
                };
            }

            return View("Fleetmatics", model);
        }

        /// <summary>
        /// Return the Key Value for the User's password (xml code) used to make requests to the FleetMatics API
        /// </summary>
        /// <param name="companyId">The Id of current company</param>
        /// <returns>A CompanyKeyValue instance if found, null otherwise</returns>
        private static CompanyKeyValue GetUserPassword(int companyId)
        {
            var xmlCode = CompanyKeyValue.GetByCompanyId(companyId, Provider.Fleetmatics.ProviderId, "FletmaticsPassword").FirstOrDefault();
            return xmlCode;
        }

        /// <summary>
        /// Return the Key Value for the User's login (xml login) used to make requests to the FleetMatics API
        /// </summary>
        /// <param name="companyId">The Id of current company</param>
        /// <returns>A CompanyKeyValue instance if found, null otherwise</returns>
        private static CompanyKeyValue GetUserLogin(int companyId)
        {
            var userLogin = CompanyKeyValue.GetByCompanyId(companyId, Provider.Fleetmatics.ProviderId, "FletmaticsLogin").FirstOrDefault();
            return userLogin;
        }

        /// <summary>
        /// Saves the Login/Password configuration
        /// </summary>
        /// <param name="collection">The two values, under the UserLogin and XmlCode of the collection</param>
        /// <returns>The Fleetmatics view</returns>
        [HttpPost]
        public ActionResult Index(FormCollection collection)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var companyId = WebGlobal.CurrentUser.CompanyId;

            var model = new FleetmaticsSettingsModel();

            var userLogin = GetUserLogin(companyId);
            if (userLogin == null)
                userLogin = new CompanyKeyValue()
                {
                    CompanyId = companyId,
                    KeyId = Provider.Fleetmatics.GetKey(KeyType.Company, "FletmaticsLogin").Id
                };

            userLogin.Value = collection["UserLogin"];
            userLogin.Save();
            model.UserLogin = userLogin.Value;

            var xmlCode = GetUserPassword(companyId);
            if (xmlCode == null)
                xmlCode = new CompanyKeyValue()
                {
                    CompanyId = companyId,
                    KeyId = Provider.Fleetmatics.GetKey(KeyType.Company, "FletmaticsPassword").Id
                };

            xmlCode.Value = collection["XmlCode"];
            xmlCode.Save();
            model.XmlCode = xmlCode.Value;

            ViewBag.IsConnected = false;
            if (!String.IsNullOrEmpty(model.UserLogin) && !String.IsNullOrEmpty(model.XmlCode))
            {
                ViewBag.IsConnected = true;
                model.Matches = GenerateMatchesModel();
            }
            else
            {
                model.Matches = new FleetmaticTruckVehicleMatchesModel()
                {
                    Trucks = new List<tb.Truck>(),
                    Vehicles = new List<string>(),
                    Matches = new List<Extric.Towbook.Integration.TruckKeyValue>()
                };
            }

            return View("Fleetmatics", model);
        }

        /// <summary>
        /// Performs an Ajax insert to add a new Truck / Vehicle relation to the TruckKeyValue store
        /// </summary>
        /// <param name="truckId">The Id of the related Truck</param>
        /// <param name="vehicleId">The Fleetmatics Id of the related Vehicle. TODO: display the Label instead of the vehicle Id</param>
        /// <returns>The FleetmaticsTruckVehicleAssociation partial view</returns>
        [HttpPost]
        public async Task<PartialViewResult> SaveTruckVehicleRelation(int truckId, string vehicleId)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var truck = await Truck.GetByIdAsync(truckId);
            await ThrowIfNoCompanyAccessAsync(truck?.Companies, "truck");

            var newTruckVehicleMatch = new TruckKeyValue()
            {
                KeyId = Provider.Fleetmatics.GetKey(KeyType.Truck, "FletmaticsVehicleId").Id,
                TruckId = truckId,
                Value = vehicleId
            };

            await newTruckVehicleMatch.SaveAsync();

            var matchedModel = GenerateMatchesModel();

            return PartialView("FleetmaticsTruckVehicleAssociation", matchedModel);
        }

        /// <summary>
        /// Performs an Ajax delete to remove a Truck / Vehicle relation from the TruckKeyValue store
        /// </summary>
        /// <param name="truckId">The Id of the related Truck</param>
        /// <returns>The FleetmaticsTruckVehicleAssociation partial view</returns>
        [HttpDelete]
        public async Task<PartialViewResult> DeleteTruckVehicleRelation(int truckId)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var truck = await Truck.GetByIdAsync(truckId);
            await ThrowIfNoCompanyAccessAsync(truck?.Companies, "truck");

            var truckKV = TruckKeyValue.GetByTruck(WebGlobal.CurrentUser.CompanyId,
                truckId).Where(w =>
                    w.KeyId == Provider.Fleetmatics.GetKey(KeyType.Truck, "FletmaticsVehicleId").Id).FirstOrDefault();

            if (truckKV != null)
            {
                await truckKV.DeleteAsync();
            }

            var matchedModel = GenerateMatchesModel();

            return PartialView("FleetmaticsTruckVehicleAssociation", matchedModel);
        }

        /// <summary>
        /// Lists all the company's trucks
        /// </summary>
        /// <returns>The list of the company's trucks</returns>
        private List<tb.Truck> GetCompanyTrucks()
        {
            return tb.Truck.GetByCompany(WebGlobal.CurrentUser.Company);
        }

        /// <summary>
        /// Lists all the vehicles for this company, determine by the Login and Password configured for Fleetmatics, that are returned by the FleetMatics API
        /// </summary>
        /// <returns>The list of the company's FleetMatics vehicles</returns>
        private List<string> GetCompanyVehiclesFromFleetmatics()
        {
            return fmt.FleetmaticsHelper.GetVehicleIds();
        }

        /// <summary>
        /// Returns the list of the company's truck / vechicle matches
        /// </summary>
        /// <param name="companyId">The Id of current company</param>
        /// <returns>A list of TruckKeyValue</returns>
        private List<TruckKeyValue> GetTruckVehicleMatches(int companyId)
        {
            return TruckKeyValue.GetByCompanyId(companyId, Provider.Fleetmatics.ProviderId, "FletmaticsVehicleId").ToList();
            //return TruckKeyValue.GetByCompany(companyId).Where(w => w.KeyId == Provider.Fleetmatics.GetKey(KeyType.Truck, "FletmaticsVehicleId").Id).ToList();
        }

        /// <summary>
        /// Generates the model to build the partial view FleetmaticsTruckVehicleAssociation
        /// </summary>
        /// <returns></returns>
        private FleetmaticTruckVehicleMatchesModel GenerateMatchesModel()
        {
            var matchedModel = new FleetmaticTruckVehicleMatchesModel();

            var truckVehicleMatches = GetTruckVehicleMatches(WebGlobal.CurrentUser.CompanyId);

            try
            {
                matchedModel.Trucks = (from t in GetCompanyTrucks()
                                       where !(truckVehicleMatches.Any(a => a.TruckId == t.Id))
                                       select t).ToList();
            }
            catch (Exception)
            {
                matchedModel.ErrorMessage = "Error retrieving the list of trucks. Please try again later";
                return matchedModel;
            }

            try
            {
                matchedModel.Vehicles = (from v in GetCompanyVehiclesFromFleetmatics()
                                         where !(truckVehicleMatches.Any(a => a.Value == v))
                                         select v).ToList();

                matchedModel.Matches = truckVehicleMatches;
            }
            catch
            {
                matchedModel.ErrorMessage = "Error retrieving the list of vehicles from FleetMatics. Please review the credentials.";
                return matchedModel;
            }

            return matchedModel;
        }
    }
}
