using Extric.Towbook.API.Models;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using Extric.Towbook;
using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class NotificationsController : Controller
    {
        //
        // GET: /Settings/payments/
        public async Task<ActionResult> Index()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            return await List(null);
        }

        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            return await List(id);
        }

        [HttpGet]
        public async Task<PartialViewResult> List(int? userId)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Notifications");

            // empty models
            ViewBag.userTypeItemJson = new EventUserTypeNotificationModel().ToJson();
            ViewBag.userItemJson = new EventUserNotificationModel().ToJson();

            // group types
            ViewBag.groupsJson = Enum.GetValues(typeof(EventNotificationGroup))
                .Cast<EventNotificationGroup>()
                .Where(w => w != EventNotificationGroup.System)
                .Select(s =>
                    new
                    {
                        Id = (int)s,
                        Name = s.ToString()
                    })
                .ToJson();

            // user types
            ViewBag.userTypesJson = Enum.GetValues(typeof(Extric.Towbook.User.TypeEnum))
                .Cast<Extric.Towbook.User.TypeEnum>()
                .Where(w => w == Extric.Towbook.User.TypeEnum.Manager ||
                        w == Extric.Towbook.User.TypeEnum.Dispatcher ||
                        w == Extric.Towbook.User.TypeEnum.Driver)
                .Select(s => new
                {
                    Id = (int)s,
                    Name = s.ToString()
                }).ToJson();


            Collection<EventUserTypeNotification> userTypeItems = (await EventUserTypeNotification.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId)).ToCollection();
            Collection<EventUserNotification> userItems = new Collection<EventUserNotification>();
            Extric.Towbook.User user = null;

            if (userId.HasValue)
            {
                user = await Extric.Towbook.User.GetByIdAsync(userId.Value);
                if (user == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(user.CompanyId))
                {
                    throw new Exception("Forbidden", new Exception("Forbidden: the user id is invalid or you don't have access to that user."));
                }

                userItems = (await EventUserNotification.GetByUserIdAsync(user.CompanyId, userId.Value)).ToCollection();
            }
            else
                userItems = await EventUserNotification.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);

            var savedSounds = await EventNotificationAlertSound.GetByCompanyIdsAsync(new[] { WebGlobal.CurrentUser.CompanyId });

            var items = EventNotification.GetAll()
                       .Select(s => EventNotificationModel.Map(s, userTypeItems, userItems, savedSounds)).ToCollection();

            var hasDestinationArrivalStatus = await CompanyKeyValue.GetFirstValueOrNullAsync(WebGlobal.CurrentUser.CompanyId,
                Provider.Towbook.ProviderId, "EnableDestinationArrivalStatus") == "1";
            
            // don't show dest arrival if its not enabled
            if (!hasDestinationArrivalStatus)
                items = items.Where(o => o.Id != (int)EventNotificationType.CallStatusDestinationArrival).ToCollection();

            ViewBag.itemsJson = items.ToJson();
            ViewBag.userJson = await WebGlobal.GetResponseFromUrlAsync("/api/users");
            ViewBag.currentUserId = user == null ? "0" : user.Id.ToString();
            ViewBag.currentUserTypeId = user == null ? "0" : ((int)user.Type).ToString();

            ViewBag.soundsJson = await WebGlobal.GetResponseFromUrlAsync("/api/eventnotificationsounds");

            return PartialView("List", user);
        }

        [HttpPost]
        public ActionResult ChangeUser(int Id)
        {
            return Redirect("/settings/Notifications/" + Id);
        }
    }
}
