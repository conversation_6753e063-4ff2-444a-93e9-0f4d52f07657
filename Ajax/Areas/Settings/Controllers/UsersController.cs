using System;
using System.Linq;

using Extric.Towbook;
using Extric.Towbook.API.Models;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System.Collections.Generic;
using Extric.Towbook.Integration;
using System.Threading.Tasks;
using System.Reflection;
using System.Collections.ObjectModel;
using Extric.Towbook.Company;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Extric.Towbook.WebShared.Multipart;
using System.IO;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class UsersController : Controller
    {
        public PartialViewResult Index()
        {
            return List();
        }

        public PartialViewResult List()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers["X-Twbk-Title"] = "Manage Users";
            ViewBag.UserTypes = JsonExtensions.ToJson(Enum.GetValues(typeof(Extric.Towbook.User.TypeEnum)).Cast<Extric.Towbook.User.TypeEnum>().Select(o => new
            {
                Id = (int)o,
                Name = o.GetDescription() ?? o.ToString(),
            }));

            // Redis Block - 12doc_new_user_driver_fields
            ViewBag.ShowNewFields = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            return PartialView("List");
        }

        public async Task Export()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var usersApi = new Extric.Towbook.API.Controllers.UsersController();
            var response = await usersApi.Export();

            // TODO fix merge
            var fileBytes = response.Content.ReadAsByteArrayAsync().Result;
            var fileName = response.Content.Headers.ContentDisposition.FileName;

            // Write the file content to the MVC response
            Response.Clear();
            Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            Response.Headers.Add("content-disposition", $"attachment; filename={fileName}");
            await Response.Body.WriteAsync(fileBytes, 0, fileBytes.Length);
            await Response.CompleteAsync();
        }

        /// <summary>
        /// Returns a read-only view of the Extric.Towbook.User with supplied ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            Response.Headers["X-Twbk-Title"] = "Modify User";
            //var neededForAccess = new List<int>();

            //Internal reusable method
            var model = await InternalGetUserModelAsync(id);

            if (model == null)
                throw new TowbookException("Invalid User");

            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user accoucnt doesn't have access to perform this action.");

            if (!model.Companies.Contains(WebGlobal.CurrentUser.CompanyId))
            {
                throw new TowbookException("Invalid userId or you don't have access to it.");
            }

            // Check if this user has access to modify a user
            var partialView = await HasCompanyAccessAsync(model);
            if (partialView != null)
                return partialView;

            //Get user history
            ViewData["history"] = await GetHistoryModelAsync(id);

            model.PasswordValidation = false;
            
            ViewBag.SharedCompanies = (await GetSharedCompaniesAsync(model.Id)).ToArray();
            
            var sharedCompanies = new Extric.Towbook.API.Controllers.SharedCompaniesController().Get();
            var mainCompanyid = model.Companies.FirstOrDefault(cId => !sharedCompanies.Any(sc => sc.SharedCompanyId == cId));

            ViewBag.MainCompany = null;
            if (mainCompanyid > 0)
            {
                ViewBag.MainCompany = await new Extric.Towbook.API.Controllers.CompaniesController().GetAsync(mainCompanyid);
            }

            ViewBag.HasPermissionToSetPermissions = false;

            //User Key Value
            var apiUserKeys = new Extric.Towbook.API.Integration.Controllers.IntegrationUsersController();
            var userKey = apiUserKeys.KeyValue("Role_Company_Owner");

            if (userKey != null && userKey.Value == "1")
                ViewBag.HasPermissionToSetPermissions = true;

            ViewBag.CustomCallRequestCompanyIds = false;

            //Company key value
            var apiCompKeys = new Extric.Towbook.API.Integration.Controllers.CompanyController();

            var coKey = apiCompKeys.FirstKeyValue(Provider.Towbook.ProviderId, "CallRequests_CustomCompanyIdsDelivery") == "1";
            if (coKey)
            {
                ViewBag.CustomCallRequestCompanyIds = true;
            }

            //Get upload files for this user
            var apiFiles = new Extric.Towbook.API.Controllers.FilesController();
            model.Files = (await apiFiles.Get(userId: id)).ToList();

            ViewBag.PrimaryCompanyName = await InternalGetPrimaryCompanyNameAsync();

            // Redis Block - 12doc_new_user_driver_fields
            ViewBag.ShowNewFields = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            return PartialView("User", model);
        }

        /// <summary>
        /// New User
        /// </summary>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        public async Task<PartialViewResult> New()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers["X-Twbk-Title"] = "New User";

            ViewBag.ShowNewFields = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            var model = new UserModel
            {
                PasswordValidation = true,
                Disabled = false,
                RecordLogins = true,
                RecordInvalidPasswordAttempts = true,
                Companies = Array.Empty<int>(),
                PrimaryCompanyId = WebGlobal.CurrentUser.CompanyId,
            };
            
            if (WebGlobal.CurrentUser.PrimaryCompanyId == 119800)
                model.Type = Extric.Towbook.User.TypeEnum.Dispatcher;

            ViewBag.SharedCompanies = (await GetSharedCompaniesAsync()).ToArray();

            ViewBag.HasPermissionToSetPermissions = false;

            //User Key Value
            var apiUserKeys = new Extric.Towbook.API.Integration.Controllers.IntegrationUsersController();
            var userKey = apiUserKeys.KeyValue("Role_Company_Owner");

            if (userKey != null &&  userKey.Value == "1")
                ViewBag.HasPermissionToSetPermissions = true;

            ViewBag.CustomCallRequestCompanyIds = false;
            model.CallRequestCompanies = Array.Empty<int>();

            //Company key value
            var apiCompKeys = new Extric.Towbook.API.Integration.Controllers.CompanyController();
            var coKey = apiCompKeys.FirstKeyValue(Provider.Towbook.ProviderId, "CallRequests_CustomCompanyIdsDelivery") != null;

            if (coKey)
            {
                ViewBag.CustomCallRequestCompanyIds = true;
            }

            ViewBag.PrimaryCompanyName = await InternalGetPrimaryCompanyNameAsync();

            // Redis Block - 12doc_new_user_driver_fields
            ViewBag.ShowNewFields = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            return PartialView("User", model);
        }

        /// <summary>
        /// Updates the posted object
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Save(UserModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (!ModelState.IsValid)
            {
                return await New();
            }
    
            if (model.Id == WebGlobal.CurrentUser.Id && model.Type != WebGlobal.CurrentUser.Type)
                model.Type = WebGlobal.CurrentUser.Type;

            if (WebGlobal.CurrentUser.AccountId > 0)
                model.AccountId = WebGlobal.CurrentUser.AccountId;

            var usersApi = new Extric.Towbook.API.Controllers.UsersController();

            // Pass true/false for create driver. This check is also handled in the User POST & PUT.
            var createDriver = (model.Type == Extric.Towbook.User.TypeEnum.Driver || model.Type == Extric.Towbook.User.TypeEnum.Spotter);

            //Update / Create User
            if (model.Id > 0)
            {
               await usersApi.Put(model.Id, model, createDriver);
               
            }
            else
            {
                model.CompanyId = WebGlobal.CurrentUser.Company.Id;
                await usersApi.Post(model, createDriver);
            }

            return RedirectToAction("List");
        }

        /// <summary>
        /// Make sure editing user can access all companies that the user being edited has access to
        /// </summary>
        /// <param name="model"></param>
        /// <param name="partialView"></param>
        /// <returns></returns>
        public async Task<PartialViewResult> HasCompanyAccessAsync(UserModel model)
        {
            // A manager editing their own account and with access to the primary company id, they have full rights to all child companies.
            if (WebGlobal.CurrentUser.Id == model.Id && HasAccessToPrimaryCompany())
                return null;

            var neededForAccess = new List<int>();

            foreach (var companyId in model.Companies)
            {
                // we collect all companies that are needed for access before redirecting
                // so that the error message lists everything that needs to be done to fix this situation.
                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(companyId))
                    neededForAccess.Add(companyId);
            }

            if (neededForAccess.Count > 0)
                return await AccessDeniedAsync(neededForAccess);

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.PrimaryCompanyId))
                return await AccessDeniedAsync(new List<int>() { model.PrimaryCompanyId });

            return null;
        }

        /// <summary>
        /// Show error message AccessDenied.cshtml on the UI when user doesn't have access to a company.
        /// </summary>
        /// <param name="companyIds"></param>
        /// <returns></returns>
        public async Task<PartialViewResult> AccessDeniedAsync(List<int> companyIds)
        {
            var missingCompanies = new List<string>();

            var api = new Extric.Towbook.API.Controllers.CompaniesController();

            foreach (var companyId in companyIds)
            {
                var company = await api.InternalGetCompanyAsync(companyId);
                missingCompanies.Add(company.Name);
            }

            ViewBag.CompanyNames = missingCompanies;

            return PartialView("AccessDenied");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Delete(UserModel model)
        {
            var api = new Extric.Towbook.API.Controllers.UsersController();
            await api.Delete(model.Id);

            return RedirectToAction("List");
        }

        [HttpGet]
        public async Task<ActionResult> Upload(int id)
        {
            if (id < 1)
                return Content("");

            var model = await InternalGetUserModelAsync(id);

            return View("Upload", model);
        }

        /// <summary>
        /// Upload files
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> StartUpload(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var file = Request.Form.Files[0];
            if (file.Length == 0)
               throw new TowbookException("No file is present on request to upload.");

            var postedFile = new FormFile(file.OpenReadStream(), 0, file.Length, file.Name, file.FileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = file.ContentType
            };

            var upload = new Extric.Towbook.API.Controllers.Users.FilesController();
            await upload.UploadFile(id, postedFile);


            return Content("<script>window.parent.finishUpload();</script>", "text/html");
        }

        [HttpGet]
        public async Task<ActionResult> UploadProfilePhoto(int id)
        {
            if (id < 1)
                return Content("");

            var model = await InternalGetUserModelAsync(id);

            return View("UploadProfilePhoto", model);
        }

        /// <summary>
        /// Upload files
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [DisableFormValueModelBinding]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> StartProfilePhotoUpload(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var file = Request.Form.Files[0];
            if (file.Length == 0)
                throw new TowbookException("No file is present on request to upload.");

            var postedFile = new FormFile(file.OpenReadStream(), 0, file.Length, file.Name, file.FileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = file.ContentType
            };
            

            string tempPathAndName = Path.GetTempFileName();
            using (var stream = new FileStream(tempPathAndName, FileMode.OpenOrCreate))
            {
                await file.CopyToAsync(stream);
            }
            var fileInfo = new FileInfo(tempPathAndName);
            var api = new Extric.Towbook.API.Controllers.UsersController();

            await api.UploadProfilePhoto(id, fileInfo);
            
            if (fileInfo.Exists)
                System.IO.File.Delete(tempPathAndName);
            var model = await InternalGetUserModelAsync(id);
            return View("~/Areas/Settings/Views/users/UploadProfilePhoto.cshtml", model);
        }

        /// <summary>
        /// Resuable Method to get UserModel
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private async Task<UserModel> InternalGetUserModelAsync(int userId)
        {
            var usersApi = new Extric.Towbook.API.Controllers.UsersController();
            return await usersApi.InternalGetUser(userId);
        }

        /// <summary>
        /// Convert to an internal model, because using Extric.Towbook.API.Models.UserHistoryModel
        /// the RAZOR user.cshtml expects to have a .NET 2.0 targeted framework.
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<Collection<InternalUserHistoryModel>> GetHistoryModelAsync(int userId)
        {
            var usersApi = new Extric.Towbook.API.Controllers.UsersController();
            var userHistory = await usersApi.HistoryAsync(userId);

            return userHistory.Select(item => new InternalUserHistoryModel
            {
                Id = item.Id,
                UserId = item.UserId,
                UserId2 = item.UserId2,
                IpAddress = item.IpAddress,
                PerformedBy = item.PerformedBy,
                CreateDate = item.CreateDate,
                Type = item.Type
            }).ToCollection();
        }

        /// <summary>
        /// Resuable Shared Companies
        /// </summary>
        /// <returns></returns>
        private async Task<IEnumerable<MinimalSharedCompany>> GetSharedCompaniesAsync(int targetUserId = 0)
        {
            // Manager is editing their own account and has access to the primary company id, they have full rights to all child companies.
            var managerHasPrimaryCompanyAccess = false;

            // This check is spefically for a manager editing their own account.
            if (WebGlobal.CurrentUser.Id == targetUserId)
                managerHasPrimaryCompanyAccess = HasAccessToPrimaryCompany();

            var sharedCoModel = new Extric.Towbook.API.Controllers.SharedCompaniesController().Get();
            var companiesApi = new Extric.Towbook.API.Controllers.CompaniesController();

            var sharedCompaniesResponse = await Task.WhenAll(sharedCoModel
                .Select(async c => await companiesApi.InternalGetCompanyAsync(c.SharedCompanyId.Value)));

            // Always take ShareAllUsers rows and isEditingManager rows. Check Manager access on non-ShareAllUsers rows
            var sharedCompanies = (await Task.WhenAll(
                sharedCompaniesResponse
                .Where(y => y != null)
                .Select(async y =>
            {
                var shareAllUsers = sharedCoModel
                    .FirstOrDefault(sc => sc.SharedCompanyId == y.Id)?.ShareAllUsers ?? false;

                var hasAccess = managerHasPrimaryCompanyAccess || await WebGlobal.CurrentUser.HasAccessToCompanyAsync(y.Id);

                var company = new MinimalSharedCompany
                {
                    Id = y.Id,
                    Name = y.ShortName ?? y.Name,
                    ShareAllUsers = shareAllUsers,
                    HasAccessToPrimaryCompany = hasAccess
                };

                return company;
            })
            )).Where(sc =>
                sc.ShareAllUsers || (managerHasPrimaryCompanyAccess || sc.HasAccessToPrimaryCompany)
            )
            .GroupBy(sc => sc.Id)
            .Select(g => g.First())
            .ToList();

            return sharedCompanies;
        }

        /// <summary>
        /// Check if a manager's  Primary Company Id == Parent Company Id, if so they have access to all companies.
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        private bool HasAccessToPrimaryCompany()
        {
            // Must be a multi-company
            var sharedCompanies = SharedCompany.GetByCompanyId(WebGlobal.CurrentUser.PrimaryCompanyId)
                .Where(s => s.SharedCompanyId > 0);

            // Return false for a single company since this method only applies to Multi-Company Access. 
            if (sharedCompanies == null || !sharedCompanies.Any())
                return false;

            // Check manager's primary company id == parent company id
            var parentCompanyId = sharedCompanies.FirstOrDefault().CompanyId;
            return (parentCompanyId == WebGlobal.CurrentUser.PrimaryCompanyId); 
        }

        /// <summary>
        /// Current User's Primary Company Name used in the primary company select list
        /// </summary>
        /// <returns></returns>
        private async Task<string> InternalGetPrimaryCompanyNameAsync()
        {
            var api = new Extric.Towbook.API.Controllers.CompaniesController();
            var company = await api.InternalGetCompanyAsync(WebGlobal.CurrentUser.PrimaryCompanyId);

            return company.Name;
        }

        public class InternalUserHistoryModel
        {
            public int Id { get; set; }
            public int UserId { get; set; }
            public int UserId2 { get; set; }
            public string IpAddress { get; set; }
            public User PerformedBy { get; set; }
            public DateTime CreateDate { get; set; }
            public Extric.Towbook.User.HistoryItem.TypeEnum Type { get; set; }
        }

        public class MinimalSharedCompany
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public bool ShareAllUsers { get; set; }
            public bool HasAccessToPrimaryCompany { get; set; }
        }
    }
}
