using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook;
using Extric.Towbook.WebShared;
using Extric.Towbook.Commissions;
using Extric.Towbook.Vehicle;
using Extric.Towbook.API.Models;
using Extric.Towbook.Utility;
using Extric.Towbook.Integrations.TomTom;
using static Ajax.AjaxUtility;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using RateItem = Extric.Towbook.RateItem;
using NLog;
using System.Collections.ObjectModel;
using Microsoft.AspNetCore.Http;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class DriversController : Controller
    {
        public static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private static bool IsAccessAllowed()
        {
            return (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager ||
                    WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant ||
                (WebGlobal.CurrentUser.Notes ?? "").Contains("AllowSettingsForTrucksDrivers") ||
                (WebGlobal.CurrentUser.Notes ?? "").Contains("GrantSupervisorRole"));
        }

        public ViewResult Index()
        {
            return List();
        }

        [HttpGet]
        public ViewResult List()
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers["X-Twbk-Title"] = "Manage Drivers";

            // Redis Block - 12doc_new_user_driver_fields
            ViewBag.ShowNewFields = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            return View("List");
        }

        /// <summary>
        /// Export Drivers to Excel
        /// </summary>
        public async Task Export()
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            //Get the Excel Export file from the API
            var api = new Extric.Towbook.API.Controllers.DriversController();
            var response = await api.ExportAsync();

            var fileBytes = await response.Content.ReadAsByteArrayAsync();
            var fileName = response.Content.Headers.ContentDisposition.FileName;

            // Write the file content to the MVC response
            Response.Clear();
            Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            Response.Headers.Append("content-disposition", $"attachment; filename={fileName}");
            await Response.Body.WriteAsync(fileBytes, 0, fileBytes.Length);
            await Response.CompleteAsync();
        }

        /// <summary>
        /// Returns a SelectList with the users for the current company with a (no user selected) option as the first.
        /// </summary>
        /// <returns></returns>
        public static async Task<SelectList> GetUsersSelectList(int? driverId)
        {
            //Call internal method to get Users
            var users = await GetUserAccounts();

            //Get Drivers (excludes deleted)
            var driverApi = new Extric.Towbook.API.Controllers.DriversController();
            var drivers = await driverApi.FullAsync();

            int userId = -1;

            // Get the user account linked for a driver (if it was linked)
            if (driverId != null && driverId != -1)
            {
                // Get the driver passed 
                var driver = drivers.FirstOrDefault(d => d.Id == driverId.Value);

                // Driver is not found, this is a deleted driver as it is not included in driverApi.Full().
                // The UI no longer supports deleting a driver, but deleted drivers can be displayed in the list. 
                if (driver == null)
                    driver = await new Extric.Towbook.API.Controllers.DriversController().GetAsync((int)driverId);

                userId = driver?.UserId ?? userId;
            }

            // filter out disabled users and users linked with other drivers - keep user if its linked with current driver
            var companyId = WebGlobal.CurrentUser.CompanyId;

            users = users.Where(u =>
                (drivers.All(d => d.UserId != u.Id) && !u.Disabled) || u.Id == userId
                )
                .Select(user =>
                {
                    user.FullName = Extric.Towbook.Core.HtmlEncode(user.FullName);
                    return user;
                })
                .ToCollection();

            users.Insert(0, new UserModel() { Id = -1, CompanyId = companyId, FullName = "Create a login account for this driver", Deleted = true });
            users.Insert(0, new UserModel() { Id = -2, CompanyId = companyId, FullName = "I don't want this driver to have a login account", Deleted = true });

            return new SelectList(users, "Id", "FullName", userId);
        }

        /// <summary>
        /// Add a new Driver
        /// </summary>
        /// <returns></returns>
        /// <exception cref="TowbookException"></exception>
        public async Task<ViewResult> New()
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers["X-Twbk-Title"] = "New Driver";

            ViewBag.ShowNewFields = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            var model = DriverModel.Map(new Driver());

            model.Users = await GetUsersSelectList(-1);
            model.EndDate = null;
            model.CommissionItems = new List<CommissionRateModel>();
    
            //Get users for login drop down
            ViewBag.UsersJson = (await GetUserAccounts())
                .Select(s => new
                {
                    Id = s.Id,
                    Email = s.Email
                })
                .OrderBy(o => o.Id)
                .ToJson();

            //TomTom: -1 for New Driver (for exception handling)
            ViewBag.GpsDrivers = GetGpsDriverJson(-1);

            ViewBag.newDriver = true;

            // Redis Block - 12doc_new_user_driver_fields
            ViewBag.ShowNewFields = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            return View("Driver", model);
        }

        /// <summary>
        /// Returns a read-only view of the Driver with supplied ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<PartialViewResult> Details(int id)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers["X-Twbk-Title"] = "Modify Driver";

            // Driver Model Get()
            var api = new Extric.Towbook.API.Controllers.DriversController();
            var model = await api.InternalGetAsync(id);

            // The user must have access to at least one company for this driver.
            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(model.Companies))
            {
                // Friendly error message. 
                var partialView = await HasCompanyAccess(model);
                if (partialView != null)
                    return partialView;
            }

            //throw new TowbookException("You don't have access to this driver.");

            var compApi = new Extric.Towbook.API.Controllers.CompaniesController();
            var company = (await compApi.GetAsync(WebGlobal.CurrentUser.CompanyId));

            if (company != null)
                ViewBag.Country = company.Country;

            //More than one company
            if (model.Companies.Length > 1)
                ViewBag.SharedObject = true;

            model.CommissionItems = new List<CommissionRateModel>();
            model.Users = await GetUsersSelectList(id);

            var filesApi = new Extric.Towbook.API.Controllers.Drivers.FilesController();
            model.Files = (await filesApi.GetAsync(model.Id)).ToList();

            //Get users for login drop down
            ViewBag.UsersJson = (await GetUserAccounts())
                .Select(s => new
                {
                    Id = s.Id,
                    Email = s.Email
                })
                .OrderBy(o => o.Id)
                .ToJson();

            //Absences
            var abApi = new Extric.Towbook.API.Drivers.Controllers.AbsencesController();
            ViewBag.AbsenceList = await abApi.Get(id);

            var absenceTypes = MvcExtensions.Enum<ScheduleAbsenceType>.GetAllValuesAsIEnumerable().Select(d =>
                new MvcExtensions.ConfigEnumTypeDefinitionModel(d));

            ViewBag.AbsenceTypes = absenceTypes;

            //TomTom
            ViewBag.GpsDrivers = GetGpsDriverJson(id);

            // Redis Block - 12doc_new_user_driver_fields
            ViewBag.ShowNewFields = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            return PartialView("Driver", model);
        }

        /// <summary>
        /// Get Upload View
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> Upload(int id)
        {
            if (id < 1)
                return Content("");

            var api = new Extric.Towbook.API.Controllers.DriversController();
            var model = await api.InternalGetAsync(id);
            return View("Upload", model);
        }

        [HttpGet]
        public ActionResult AbsenceNew(int id)
        {
            var model = new Extric.Towbook.API.Drivers.Controllers.AbsencesController.ScheduleAbsenceModel();

            model.Type = ScheduleAbsenceType.BreakOrLunch;
            model.DriverId = id;
            model.StartDate = WebGlobal.OffsetDateTime(DateTime.Now).Date;
            model.EndDate = WebGlobal.OffsetDateTime(DateTime.Now).Date;

            return View("AbsenceEditor", model);
        }

        [HttpGet]
        public async Task<ActionResult> AbsenceEditor(int id)
        {

            if (Request.Query.ContainsKey("id"))
            {
                int absenceId = Convert.ToInt32(Request.Query["id"]);

                if (absenceId < 1)
                    return Content("");

                var abApi = new Extric.Towbook.API.Drivers.Controllers.AbsencesController();
                var model = await abApi.Get(id, absenceId);

                if (model.DriverId != id)
                    throw new TowbookException("Invalid driverId.");

                model.StartDate = WebGlobal.OffsetDateTime(model.StartDate);
                model.EndDate = WebGlobal.OffsetDateTime(model.EndDate);

                return View("AbsenceEditor", model);
            }

            return View("AbsenceEditor", new Extric.Towbook.API.Drivers.Controllers.AbsencesController.ScheduleAbsenceModel()
            {
                Type = ScheduleAbsenceType.BreakOrLunch
            });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> StartUpload(int id)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var file = Request.Form.Files[0];
            if ((int)Request.Form.Files[0].Length == 0)
                throw new TowbookException("No file is present on request to upload.");

            var postedFile = new FormFile(file.OpenReadStream(), 0, file.Length, file.Name, file.FileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = file.ContentType
            };

            var upload = new Extric.Towbook.API.Controllers.Drivers.FilesController();
            await upload.UploadFile(id, postedFile);

            return Json(new { success = true });
        }

        /// <summary>
        /// Creates new driver or updates exisitng driver.
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Details([FromBody] DriverModel driverModel)
        {
            if (!IsAccessAllowed())
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (!ModelState.IsValid)
            {
                if (driverModel.Id <= 0)
                    throw new Exception("Error: The model Id is not valid in POST API request.");

                var errors = ModelState.Select(x => x.Value.Errors)
                           .Where(y => y.Count > 0)
                           .ToList();
                throw new Exception("IsValid=false; " + errors.ToJson(true));

                return (await Details(driverModel.Id));
            }

            var createUserLogin = false;

            //UserId == -1: Create User || UserId == -2: Don't Create User
            if (driverModel.UserId < 0)
            {
                if (driverModel.UserId == -1)
                {
                    createUserLogin = true;
                }

                driverModel.UserId = 0;
            }

            //Create User Login Account, and update the driver with the user login id
            if (createUserLogin)
            {
                var userModel = new UserModel()
                {
                    FullName = driverModel.Name,
                    Email = driverModel.Email,
                    Username = driverModel.Username,
                    Password = driverModel.Password,
                    Notes = driverModel.Notes,
                    Type = Extric.Towbook.User.TypeEnum.Driver,
                    MobilePhone = driverModel.MobilePhone
                };

                //Create a user account. Email and text notification will be sent
                var usersApi = new Extric.Towbook.API.Controllers.UsersController();
                var userResult = await usersApi.Post(userModel, false);

                //Set the UserId for the driver
                driverModel.UserId = userResult.Id;
            }

            //Update / Add new driver
            var driverApi = new Extric.Towbook.API.Controllers.DriversController();

            if (driverModel.Id > 0)
            {
                await driverApi.Put(driverModel.Id, driverModel);
            }
            else
            {
                await driverApi.Post(driverModel);
            }

            return RedirectToAction("List");
        }

        /// <summary>
        /// Updates the posted object
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ViewResult> Commissions(int rateitemId, int driverId)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
                WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var r = await RateItem.GetByIdAsync(rateitemId);
            var driver = await Driver.GetByIdAsync(driverId);

            await ThrowIfNoCompanyAccessAsync(r?.CompanyId);
            await ThrowIfNoCompanyAccessAsync(driver?.Companies);

            ViewBag.driverId = driverId;

            var c = await CommissionRateItem.GetByRateItemAsync(rateitemId, driverId);
            var ce = await CommissionRateItemBodyType.GetByRateItemAsync(rateitemId, driverId);

            var crm = new CommissionRateModel();

            crm.Name = r.Name;
            crm.RateItemId = r.RateItemId;

            if (c != null)
            {
                crm.CommissionValue = c.ToString();
                crm.DriverId = c.Driver != null ? c.Driver.Id : 0;
                crm.Id = c.Id;
            }


            crm.ExtendedRates = await CommissionRateExtendedModel.MapFromDomainObjectList(ce, WebGlobal.CurrentUser);

            Response.Headers.Add("X-Twbk-Title", "Modify Commission for " + crm.Name);
            return View("CommissionEditor", crm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [ResponseCache(NoStore = true, Duration = 0, VaryByQueryKeys = new[] { "*" })]
        public async Task<ActionResult> Commissions(CommissionRateModel model, int driverId)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
                WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                var rateItem = await RateItem.GetByIdAsync(model.RateItemId);
                var driver = await Driver.GetByIdAsync(driverId);

                await ThrowIfNoCompanyAccessAsync(rateItem?.CompanyId);
                await ThrowIfNoCompanyAccessAsync(driver?.CompanyId);

                var c = await CommissionRateItem.GetByRateItemAsync(model.RateItemId, driverId);
                var e = await CommissionRateItemBodyType.GetByRateItemAsync(model.RateItemId, driverId);

                if (c == null)
                {
                    c = new CommissionRateItem();
                    c.RateItem = await RateItem.GetByIdAsync(model.RateItemId);
                }

                if (e == null)
                {
                    e = new Dictionary<int, CommissionRateItemBodyType>();
                }

                decimal anyValue = 0;

                if (!String.IsNullOrEmpty(model.CommissionValue))
                {
                    anyValue = Math.Round(Convert.ToDecimal(model.CommissionValue.Replace("%", "").Replace("$", "").Trim()), 2, MidpointRounding.AwayFromZero);

                    if (model.CommissionValue.StartsWith("$"))
                    {
                        c.Percentage = 0;
                        c.FlatRate = anyValue;
                        c.Type = CommissionType.FlatRate;
                    }
                    else // if it ends with %, or no definition, treat it as %
                    {
                        c.Percentage = anyValue;
                        c.FlatRate = 0;
                        c.Type = CommissionType.Percentage;
                    }
                    c.Driver = await Driver.GetByIdAsync(driverId);
                    c.Save();
                }
                else
                {
                    if (c.Driver != null && c.Driver.Id == driverId)
                    {
                        c.Delete(WebGlobal.CurrentUser);
                    }
                }

                #region save extended rate item prices

                foreach (CommissionRateExtendedModel crm in model.ExtendedRates)
                {
                    decimal rAmount = 0;

                    if (!String.IsNullOrEmpty(crm.Value))
                        rAmount = Math.Round(Convert.ToDecimal(crm.Value.Replace("%", "").Replace("$", "").Trim()), 2, MidpointRounding.AwayFromZero);

                    if (rAmount == 0)
                    {
                        if (e.ContainsKey(crm.BodyTypeId))
                        {
                            e[crm.BodyTypeId].Delete(Extric.Towbook.WebShared.WebGlobal.CurrentUser);
                        }
                    }
                    else
                    {
                        if (!e.ContainsKey(crm.BodyTypeId))
                        {
                            e.Add(crm.BodyTypeId, new CommissionRateItemBodyType());
                            e[crm.BodyTypeId].RateItem = c.RateItem;
                            e[crm.BodyTypeId].BodyType = await BodyType.GetByIdAsync(crm.BodyTypeId);
                        }

                        if (crm.Value.StartsWith("$"))
                        {
                            e[crm.BodyTypeId].Type = CommissionType.FlatRate;
                            e[crm.BodyTypeId].FlatRate = rAmount;
                            e[crm.BodyTypeId].Percentage = 0;
                        }
                        else
                        {
                            e[crm.BodyTypeId].Type = CommissionType.Percentage;
                            e[crm.BodyTypeId].FlatRate = 0;
                            e[crm.BodyTypeId].Percentage = rAmount;
                        }
                        e[crm.BodyTypeId].Driver = await Driver.GetByIdAsync(driverId);
                        e[crm.BodyTypeId].Save();
                    }
                }
                #endregion

                return (await Details(driverId));
            }
            else
            {
                return await Commissions(model.RateItemId, driverId);
            }
        }

        /// <summary>
        /// Reusable method to get Users for the Select Box when assigning a user login for a driver
        /// </summary>
        /// <returns></returns>
        private static async Task<Collection<UserModel>> GetUserAccounts()
        {
            // Get all users by company
            var api = new Extric.Towbook.API.Controllers.CompaniesController();
            var users =  await api.Users(WebGlobal.CurrentUser.CompanyId);

            var usersCompanyAccess = users
                .Where(u => !(u.Deleted ?? false))
                .OrderBy(u => u.FullName)
                .ToCollection();

            return usersCompanyAccess;
        }

        /// <summary>
        /// Reusable method to get Gps Drivers JSON
        /// </summary>
        /// <returns></returns>
        private string GetGpsDriverJson(int driverId)
        {
            var ttApi = new Extric.Towbook.API.Integration.GPS.Providers.TomTom.Controllers.TomTomDriversController();
            var gpsDrivers = ttApi.GpsDrivers();

            string GpsDriverJson = null;

            if (gpsDrivers != null)
            {
                ViewBag.GpsDrivers = null;
                try
                {
                    GpsDriverJson = gpsDrivers.ToJson();
                }
                catch (TomTomException e)
                {
                    // silently ignore
                    if (driverId > 0)
                    {
                        logger.LogExceptionEvent("Driver Details Post TomTomException", e,
                            WebGlobal.CurrentUser,
                            new LogEventInfo { Properties = { ["driverId"] = driverId } });
                    }
                    else
                    {
                        logger.LogExceptionEvent("Driver.New() TomTom Error", e, WebGlobal.CurrentUser);
                    }
                }
            }

            return GpsDriverJson;
        }

        /// <summary>
        /// User must have access to at least one company for this driver. 
        /// </summary>
        /// <param name="model"></param>
        /// <param name="partialView"></param>
        /// <returns></returns>
        public async Task<PartialViewResult> HasCompanyAccess(DriverModel model)
        {
            var neededForAccess = new List<int>();

            foreach (var companyId in model.Companies)
            {
                // we collect all companies that are needed for access before redirecting
                // so that the error message lists everything that needs to be done to fix this situation.
                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(companyId))
                    neededForAccess.Add(companyId);
            }

            if (neededForAccess.Count > 0)
                return await AccessDenied(neededForAccess);

            return null;
        }

        /// <summary>
        /// Show error message AccessDenied.cshtml on the UI when user doesn't have access to a company.
        /// </summary>
        /// <param name="companyIds"></param>
        /// <returns></returns>
        public async Task<PartialViewResult> AccessDenied(List<int> companyIds)
        {
            var missingCompanies = new List<string>();
            var api = new Extric.Towbook.API.Controllers.CompaniesController();

            foreach (var companyId in companyIds)
            {
                var company = await api.InternalGetCompanyAsync(companyId);
                missingCompanies.Add(company.Name);
            }

            ViewBag.CompanyNames = missingCompanies;

            return PartialView("AccessDenied");
        }
    }
}
