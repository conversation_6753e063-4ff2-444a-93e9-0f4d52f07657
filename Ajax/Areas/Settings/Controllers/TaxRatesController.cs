using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Extric.Towbook;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models;
using static Ajax.AjaxUtility;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class TaxRatesController : Controller
    {
        //
        // GET: /Settings/TaxRate/
        [HttpGet]
        public async Task<ActionResult> Index()
        {
            return await List();
        }

        [HttpGet]
        public async Task<PartialViewResult> List()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Taxing Rates & Rules");

            var model = TaxRateModel.Map(await TaxRate.GetByCompanyAsync(WebGlobal.CurrentUser.Company));

            Extric.Towbook.Company.Company c = WebGlobal.CurrentUser.Company;

            ViewBag.MultipleTaxRates = c.TaxMode == Extric.Towbook.Company.Company.TaxModeEnum.Multiple; 

            if (c.TaxMode == Extric.Towbook.Company.Company.TaxModeEnum.Single && c.TaxRates.Any())
            {
                ViewBag.SingleTaxRate = c.TaxRates[0].Rate.ToString("0.000");
            }

            if (c.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada)
            {
				Extric.Towbook.Company.Countries.Canada cx = await Extric.Towbook.Company.Countries.Canada.GetByCompanyAsync(c);
                ViewBag.GST = cx.GST;
            }

            return PartialView("List", model);
        }

        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            //TODO can be async
            var taxRate = TaxRate.GetById(id, WebGlobal.CurrentUser.Company.Id);
            await ThrowIfNoCompanyAccessAsync(taxRate?.CompanyId);

            var model = TaxRateModel.Map(taxRate);

            Response.Headers.Add("X-Twbk-Title", "Modify " + model.Description);
            return PartialView("TaxRate", model);
        }


        [HttpGet]
        public PartialViewResult New()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Add a New Tax Rate");

            var model = new TaxRateModel();
            return PartialView("TaxRate", model);
        }

        [HttpPost]
        public async Task<ActionResult> Details(TaxRateModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (!ModelState.IsValid) 
                return New();

            TaxRate taxRate;

            if (model.Id > 0)
            {
                taxRate = await TaxRate.GetByIdAsync(model.Id);
                await ThrowIfNoCompanyAccessAsync(taxRate?.CompanyId);
                if (model.CompanyId > 0)
                    await ThrowIfNoCompanyAccessAsync(model?.CompanyId);
            }
            else
            {
                taxRate = new TaxRate {Company = WebGlobal.CurrentUser.Company};
            }

            await TaxRateModel.Map(model, taxRate).Save();

            WebGlobal.CurrentUser.Company.TaxMode = Extric.Towbook.Company.Company.TaxModeEnum.Multiple;
            await WebGlobal.CurrentUser.Company.Save();

            return RedirectToAction("List");
        }

        [HttpPost]
        public async Task<ActionResult> Save()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            string taxMode = Request.Form["TaxMode"];
            Extric.Towbook.Company.Company c = WebGlobal.CurrentUser.Company;

            if (taxMode == "True")
            {
                c.TaxMode = Extric.Towbook.Company.Company.TaxModeEnum.Multiple;
                if (c.TaxRates.Count == 0)
                {
                    throw new TowbookException("Must specify at least one tax rate.");
                }
            }
            else if (taxMode == "False")
            {
                c.TaxMode = Extric.Towbook.Company.Company.TaxModeEnum.Single;

                TaxRate tr = null;

                string rate = Request.Form["SingleTaxRate"];
                
                if (c.TaxRates.Count == 0)
                {
                    tr = new TaxRate();
                }
                else if (c.TaxRates.Count == 1)
                {
                    tr = c.TaxRates[0];
                }
                else if (c.TaxRates.Count > 1)
                {
                    // Delete all the tax rates.
                    foreach (TaxRate taxRate in c.TaxRates)
                    {
                        await taxRate.Delete();
                    }
                    tr = new TaxRate();
                }

                if (rate.Length > 0)
                {
                    tr.Company = c;
                    tr.Description = "Standard Tax Rate";
                    tr.Rate = Convert.ToDecimal(rate);
                    await tr.Save();
                }
            }
            else
                throw new TowbookException("Must choose Single or Multiple tax rate mode!");

            if (c.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada)
            {
                Extric.Towbook.Company.Countries.Canada cx = await Extric.Towbook.Company.Countries.Canada.GetByCompanyAsync(c);
                cx.GST = Request.Form["GST"];
                cx.Save();
            }

            await c.Save();

            return RedirectToAction("List");
        }

        [HttpPost]
        public async Task<ActionResult> Delete(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var x = TaxRate.GetById(id, WebGlobal.CurrentUser.Company.Id);
            if (x != null && await WebGlobal.CurrentUser.HasAccessToCompanyAsync(x.CompanyId))
            {
                await x.Delete();
            }

            return RedirectToAction("List");
        }
    }
}
