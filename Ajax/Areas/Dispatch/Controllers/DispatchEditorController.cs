using System;
using System.Linq;
using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Dispatch.QuoteModels;
using Extric.Towbook.WebShared;
using Extric.Towbook.Generated;
using Microsoft.AspNetCore.Http;
using System.Text.Json.Serialization;
using Extric.Towbook.API.Controllers;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Primitives;


namespace Ajax.Areas.Dispatch.Controllers
{
    [Area("Dispatch")]
    [Route("[controller]")]
    public class DispatchEditorController : Controller
    {
        [HttpGet("{id?}")]
        public async Task<IActionResult> IndexAsync(int? id,
            [FromQuery]int? c,
            [FromQuery]string q,
            [FromQuery]bool? duplicate)
        {
            var em = new EditorModel();

            em.Request = Request;
            em.Response = Response;

            await em.GetCallAsync(id.GetValueOrDefault(), c.<PERSON>al<PERSON>rDefault(), q, duplicate ?? false);

            return View(em);
        }
    }
}


public class EditorModel// : PageModel
{
    [JsonIgnore]
    public HttpRequest Request { get; set; }
    [JsonIgnore]
    public HttpResponse Response { get; set; }

    public string DefaultCompanyAddressJson;
    string GetCurrentUser()
    {
        return UserJson;
    }

    private int id;
    public int Id => id;

    [BindProperty(SupportsGet = true)]
    public int AccountId { get; set; }

    [BindProperty(SupportsGet = true)]
    public string AccountAddress { get; set; }

    [BindProperty(SupportsGet = true)]
    public decimal? AccountLatitude { get; set; }

    [BindProperty(SupportsGet = true)]
    public decimal? AccountLongitude { get; set; }

    public CallModel Call { get; set; }
    public QuoteModel Quote { get; set; }

    private int impoundId;
    public int ImpoundId => impoundId;

    public int DefaultTaxRateId { get; private set; }
    public bool HideCharges { get; set; }
    public async Task<bool> HasHourlyStorageAsync()
    {
        return await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Impounds_HourlyStorage);
    }
    public async Task<bool> HasAutoMileageAsync()
    {
        return await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.GPS_AutomaticMileage);
    }
    public async Task<bool> HasRoadsideAsync()
    {
        return await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Roadside);
    }
    public int[] allowedRoadsideMotorClubAccounts = new int[] { };
    public int[] alwaysIncludeDeadheadMileageLineItemAccounts = new int[] { };
    public bool AllowAdHocPricingItems => HasPermissionToCreateAdHocPricingItems();
    public bool BlockCommissionsOnAdHocItems => BlockCommissionOnAdHocPricingItems();
    public bool LimitPricingtoAccountRateItems => HasLimitPricingOptionsToAccountPricing();
    public bool AllowPurchaseOrderNumberChangeOnLockedCalls => HasPermissionToChangePurchaseOrderNumber();
    public bool ForceIncludeUnitNumberForAllAssetTypes => AlwaysIncludeUnitNumberForAllAssetTypes();

    private bool _autoFillMiles;
    public bool AutomaticallyFillInMiles => _autoFillMiles;
    public bool RoadsideOnly { get; set; }

    public string EditorName { get; set; }
    public string EditorAction { get; set; }

    public string PermitStatusesJson { get; set; }

    public bool HasQuotes { get; set; }
    public bool HasPermissionToRemovePoliceHolds => WebGlobal.CurrentUser.HasPermissionToRemovePoliceHolds();

    [BindProperty(SupportsGet = true)]
    public int QuoteNumber { get; set; }
    public string QuoteJson { get; set; }

    public string UserJson { get; set; }
    public bool HasImpoundOnlyEnabled { get; set; }
    public bool HasInvoiceItemNotesFeature = false;

    private int statusCode = 0;
    public async Task<string> GetCurrentUserJsonAsync()
    {
        var o = (await new UserController().Get()).ToJson();
        return o;
    }

    public string GetQuote(Guid id, bool? duplicate = false)
    {
        int statusCode = 0;
        string json = null;

        if (duplicate.GetValueOrDefault())
            json = WebGlobal.GetResponseFromUrl("/api/quotes/" + id + "/duplicate", out statusCode);
        else
            json = WebGlobal.GetResponseFromUrl("/api/quotes/" + id, out statusCode);

        if (json.Equals("[]"))
            throw new Exception("The quote cannot be found or you don't have permission to view it.");

        return json;
    }

    private bool HasPermissionToCreateAdHocPricingItems()
    {
        var kv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "PreventAdHocInvoiceItems").FirstOrDefault();
        return !(kv != null && kv.Value == "1");
    }

    private bool BlockCommissionOnAdHocPricingItems()
    {
        var kv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "BlockCommissionOnAdHocInvoiceItems").FirstOrDefault();
        return (kv != null && kv.Value == "1");
    }

    public bool HasLimitPricingOptionsToAccountPricing()
    {
        var kv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "LimitPricingOptionsInEditorToAccountPricingOnly").FirstOrDefault();
        return (kv != null && kv.Value == "1");
    }

    public bool HasPermissionToChangePurchaseOrderNumber()
    {
        var kv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.PrimaryCompanyId, Provider.Towbook.ProviderId, "AllowPurchaseOrderChangesOnLockedCalls").FirstOrDefault();
        return (kv != null && kv.Value == "1");
    }

    public bool AlwaysIncludeUnitNumberForAllAssetTypes()
    {
        var kv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "ForceIncludeUnitNumberForAllAssetsTypes").FirstOrDefault();
        return (kv != null && kv.Value == "1");
    }

    public bool DisablePreviousCallInfoSearchByPhoneNumber()
    {
        var kv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "DisableFetchingPreviousCallByContactPhoneNumber").FirstOrDefault();
        return (kv != null && kv.Value == "1");
    }

    public async Task GetCallAsync(int id, int c, string q, bool duplicate)
    {
        HasImpoundOnlyEnabled = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("impoundCustomFields");
        HasQuotes = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Dispatching_Quotes);
        EditorName = "Call";
        EditorAction = "Create";

        this.UserJson = await GetCurrentUserJsonAsync();
        this.id = id;
        int callNumber = 0;
        if (c>0)
        {
            callNumber = c;
            var cen = await Extric.Towbook.Dispatch.Entry.GetByCallNumberAsync(callNumber, WebGlobal.CurrentUser.Company);
            if (cen != null)
            {
                // Use Redirect for Razor Pages
                Response.Redirect("/ajax/DispatchEditor/" + cen.Id + (Request.Query["_"] != StringValues.Empty ? "?_=" + Request.Query["_"] : ""));

                return;
            }
        }

        HasInvoiceItemNotesFeature = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("invoice_item_notes");

        if (!string.IsNullOrEmpty(Request.Query["impoundId"]))
        {
            int.TryParse(Request.Query["impoundId"], out impoundId);
            EditorName = "Impound";
        }
        PermitStatusesJson = Extric.Towbook.Accounts.PermitStatus.GetAll().ToJson();

        QuoteJson = "{}";
        Guid quoteId = Guid.Empty;
        var isDuplicate = duplicate == true;
        if (HasQuotes)
        {
            if (!string.IsNullOrEmpty(q))
            {
                if (Guid.TryParse(q, out quoteId))
                {
                    EditorName = "Quote";
                    EditorAction = isDuplicate ? "Create" : "Update";
                    QuoteJson = GetQuote(quoteId, isDuplicate);

                    Quote = Newtonsoft.Json.JsonConvert.DeserializeObject<QuoteModel>(QuoteJson);
                    QuoteNumber = Quote.QuoteNumber;
                    Call = Quote.Call;
                    id = Call.Id;
                    AccountId = Call.Account.Id;
                }
            }
            else
            {
                QuoteNumber = 0;
                QuoteJson = "{}";// QuoteModelExtension.Map(new QuoteModel()).ToJson(true);
            }
        }

        if (id == 0 && impoundId > 0)
        {
            var imp = await Extric.Towbook.Impounds.Impound.GetByIdAsync(impoundId);
            if (await WebGlobal.CurrentUser.HasAccessToCompanyAsync(imp.Company.Id))
                this.id = id = imp.DispatchEntry.Id;
        }

        if (!string.IsNullOrEmpty(Request.Query["accountId"]))
            AccountId = Convert.ToInt32(Request.Query["accountId"]);

        if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser)
        {
            AccountId = WebGlobal.CurrentUser.AccountId;

            AccountAddress = string.Empty;
            var account = await Extric.Towbook.Accounts.Account.GetByIdAsync(WebGlobal.CurrentUser.AccountId);
            if (account != null)
            {
                AccountAddress = account.FormatAddress();
                AccountLatitude = account.Latitude;
                AccountLongitude = account.Longitude;
            }
        }

        int newCompanyId = WebGlobal.CurrentUser.CompanyId;
        var newCompany = WebGlobal.CurrentUser.Company;

        if (!string.IsNullOrEmpty(Request.Query["__companyId"]))
        {
            newCompanyId = Convert.ToInt32(Request.Query["__companyId"]);

            if (await WebGlobal.CurrentUser.HasAccessToCompanyAsync(newCompanyId))
            {
                WebGlobal.OverrideCompanyForThisRequestOnly(newCompanyId, WebGlobal.CurrentUser);
                newCompany = await Extric.Towbook.Company.Company.GetByIdAsync(newCompanyId);
            }
            else
            {
                newCompanyId = WebGlobal.CurrentUser.CompanyId;
            }
        }

        if (new int[] { 4599, 4598, 4597, 4596 }.Contains(newCompanyId))
            AccountId = 24596;

        if (Id > 0)
        {
            if (Request.Query["undelete"] == "1")
            {
                await Extric.Towbook.Dispatch.Entry.UndeleteById(new Extric.Towbook.AuthenticationToken()
                {
                    ClientVersionId = Extric.Towbook.Platform.ClientVersion.GetByGitHash("web-app",
                        Extric.Towbook.Platform.ClientVersionType.WebApp).Id,
                    UserId = WebGlobal.CurrentUser.Id,
                }, WebGlobal.GetRequestingIp(), Id);
            }

            if (Call == null)
            {
                var en = await Extric.Towbook.Dispatch.Entry.GetByIdAsync(this.id);

                if (en == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(en.CompanyId))
                {
                    // In ASP.NET Core, throw an exception or return NotFound() as needed
                    throw new Exception("You don't have access to this call.");
                }

                this.Call = await new CallsController().Get(id);

                if (this.Call.Status != null)
                {
                    if (this.Call.Status.Id == 252)
                        this.Call.Status.Id = 5;
                    if (this.Call.Status.Id == 253)
                        this.Call.Status.Id = 255;
                }

                newCompanyId = en.CompanyId;
                EditorAction = "Update";

                if (Request.Query["duplicate"] == "true")
                    EditorAction = "Duplicate";

                if (Request.Query["auction"] == "1")
                {
                    EditorAction = "Duplicate";
                    int nStat = 0;
                    var json = WebGlobal.GetResponseFromUrl("/api/calls/" + id + "/duplicate?type=auction", out nStat);
                    this.Call = Newtonsoft.Json.JsonConvert.DeserializeObject<CallModel>(json);
                }
            }
        }
        else
        {
            this.Call = null;
        }

        var serviceCallsOnly =  (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "ServiceCallsOnly")).FirstOrDefault();
        if (serviceCallsOnly != null && serviceCallsOnly.Value == "1")
            RoadsideOnly = true;

        var hideChargesFromDispatcher = (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromViewingInvoiceItems")).FirstOrDefault();
        var hideChargesFromDriver = (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingInvoiceItems")).FirstOrDefault();

        if (hideChargesFromDispatcher != null && hideChargesFromDispatcher.Value == "1" && WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher)
            HideCharges = true;
        if (hideChargesFromDriver != null && hideChargesFromDriver.Value == "1" && WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
            HideCharges = true;
        if (!string.IsNullOrEmpty(Request.Query["hidecharges"]))
            HideCharges = true;

        HideCharges = false;

        var defaultTaxRateId = (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "DefaultTaxRateId")).FirstOrDefault();
        if (defaultTaxRateId != null)
        {
            DefaultTaxRateId = Convert.ToInt32(defaultTaxRateId.Value);
        }
        else
        {
            var taxRates = await Extric.Towbook.TaxRate.GetByCompanyAsync(newCompany);
            if (taxRates != null && taxRates.Count() == 1)
            {
                var tr = taxRates.First();
                if (tr.CompanyId == newCompanyId)
                    DefaultTaxRateId = Convert.ToInt32(tr.Id);
            }
        }

        var fillMiles = (await CompanyKeyValue.GetByCompanyIdAsync(newCompanyId, Provider.Towbook.ProviderId, "Towbook_Calls_AutomaticallyAddMiles")).FirstOrDefault();
        if (fillMiles != null && fillMiles.Value == "1")
        {
            _autoFillMiles = true;
            if (Call != null && Call.Attributes != null)
            {
                var av = Call.Attributes.Where(o => o.AttributeId == 51).FirstOrDefault();
                if (av != null && av.Value == "2")
                    _autoFillMiles = false;
            }

            if (AccountId > 1)
            {
                var akv = AccountKeyValue.GetFirstValueOrNull(newCompanyId, AccountId, Provider.Towbook.ProviderId, "AutomaticallyAddMiles");
                if (akv != null && akv != "0")
                    _autoFillMiles = (akv == "1");
            }
        }

        if (string.IsNullOrEmpty(Request.Query["q"]) && isDuplicate)
        {
            if (Call != null)
            {
                Call.Insights = null;
                Call.AvailableActions = Array.Empty<string>();
            }
        }
    }
}

