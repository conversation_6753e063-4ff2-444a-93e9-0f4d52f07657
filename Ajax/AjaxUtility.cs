using Extric.Towbook;
using Extric.Towbook.WebShared;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace Ajax
{
    public class AjaxUtility
    {
        /// <summary>
        /// Throws a NotFound exception if companyId is null, or Forbidden if the current user doesn't have access to 
        /// the company passed.
        /// </summary>
        /// <param name="companyId"></param>
        public static async Task ThrowIfNoCompanyAccessAsync(int? companyId)
        {
            if (companyId == null)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The object you requested cannot be found.")
                });
            }

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(companyId.Value))
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your user account doesn't have access to this object due to a companyId restriction.")
                });
            }
        }

        public static async Task ThrowIfNoCompanyAccessAsync(int[] companyIds, string type = "object")
        {
            type ??= "object";

            if (companyIds == null)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The {type.ToLowerInvariant()} you requested doesn't exist.")
                });
            }

            var hasAccess = await WebGlobal.CurrentUser.HasAccessToCompanyAsync(companyIds);
            if (!hasAccess)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent($"Your user account doesn't have access to this {type} due to a companyId restriction.")
                });
            }
        }


        public static void ThrowIfPropertyMissing(string name)
        {
            throw new TowbookException($"Couldn't process request because the {name} property wasn't specified.");
        }


        public static void ThrowIfNotFound(object o, string type = "object")
        {
            if (o == null)
                throw new TowbookException($"The {type} you tried to access cannot be found.");
        }

        public static async Task ThrowIfFeatureNotAssignedAsync(Extric.Towbook.Generated.Features f)
        {
            if (! await WebGlobal.CurrentUser.Company.HasFeatureAsync(f))
                throw new TowbookException($"Your company doesn't have the {f.ToString()} feature included in its current service plan.");
        }

        
    }
}
