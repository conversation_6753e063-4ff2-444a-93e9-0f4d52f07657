using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Utility;
using Newtonsoft.Json;

namespace Extric.Towbook.API.Models
{
    public class CallRequestModel
    {
        public int CallRequestId { get; set; }
        public int CompanyId { get; set; }
        public int AccountId { get; set; }
        public string AccountName { get; set; }
        public int MasterAccountId { get; set; }
        public decimal OfferAmount { get; set; }
        public string OfferTypeText { get; set; }
        public int? DefaultEta { get; set; }
        public string StartingLocation { get; set; }
        public string Reason { get; set; }
        public string ServiceNeeded { get; set; }
        public string Vehicle { get; set; }
        public int? OwnerUserId { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string TowDestination { get; set; }
        public string ProviderId { get; set; }

        /// <summary>
        /// Comma-delimited DriverId's that were picked by the MC to assign this job to.
        /// </summary>
        [Newtonsoft.Json.JsonConverter(typeof(StringToIntArrayConverter))]
        public string Drivers { get; set; }
        [Newtonsoft.Json.JsonConverter(typeof(StringToIntArrayConverter))]
        public string Trucks { get; set; }

        /// <summary>
        /// If the ETA the user responds with is above this number, specify the ETA reason when responding.
        /// </summary>
        public int MaxEta { get; set; }

        public int[] SupportedEtas { get; set; } = new int[] {
            5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 90, 99,
            100,110, 120, 150, 180, 210, 240, 300, 360, 420, 480, 540, 600, 1000, 1200
        };

        /// <summary>
        /// Available actions for the call. 
        /// ACCEPT, REJECT, REQUEST_CALL
        /// </summary>
        public List<string> AvailableActions { get; set; }

        /// <summary>
        /// The time that the call MUST be accepted/rejected. If it isn't, the requester may call you, or send it to another towing company.
        /// </summary>
        public DateTime? ExpirationDate { get; set; }
        public DateTime RequestDate { get; set; }

        [Write(false)]
        public DateTime? ExpirationDateUtc { get; set; }
        [Write(false)]
        public DateTime RequestDateUtc { get; set; }

        /// <summary>
        /// If this request results in a DispatchEntryId being created, record it in this property.
        /// </summary>
        public int? DispatchEntryId { get; set; }

        /// <summary>
        /// Status of the request.. 0-Default, 1-Accepted, 2-Rejected, 3-Deferred.
        /// </summary>
        public CallRequestStatus Status { get; set; }

        public int? ResponseReasonId { get; set; }

        /// <summary>
        /// Distance from your home-base to the incident location.
        /// </summary>
        public double? Distance { get; set; }

        /// <summary>
        /// Distance from the incident location to tow destination.
        /// </summary>
        public double? LoadedDistance { get; set; }

        /// <summary>
        /// The ETA provided by the user to send back to the motor club. 
        /// </summary>
        public int? Eta { get; set; }

        /// <summary>
        /// The latitude of the starting location.
        /// </summary>
        public decimal? StartLocationLatitude { get; set; }
        /// <summary>
        /// The longitude of the starting location.
        /// </summary>
        public decimal? StartLocationLongitude { get; set; }

        public static async Task<CallRequestModel> MapAsync(CallRequest o)
        {
            if (o == null)
                return null;

            var acc = await Account.GetByIdAsync(o.AccountId);

            o.MasterAccountId = acc.MasterAccountId;
            o.AccountName = acc.Company;

            if (o.MasterAccountId == MasterAccountTypes.OonAgero ||
                o.MasterAccountId == MasterAccountTypes.OonAllstate)
            {
                if (o.MasterAccountId == MasterAccountTypes.OonAgero)
                {
                    o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 91).ToArray();
                    o.MaxEta = 90;
                }

                if (o.MasterAccountId == MasterAccountTypes.OonAllstate)
                {
                    o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 301).ToArray();
                    o.MaxEta = 300;
                }
                o.ProviderId = await Integrations.Email.EmailAddress.GetPrimaryTowbookEmailAddressAsync(o.CompanyId);
            }

            if (o.MasterAccountId == MasterAccountTypes.OonAllstate ||
                o.MasterAccountId == MasterAccountTypes.Allstate)
            {
                var json = await Core.GetRedisValueAsync(o.CallRequestId + ":extra");
                try
                {
                    if (json != null)
                    {
                        var extra = JsonConvert.DeserializeObject<CallRequestExtraModel>(json);
                        if (extra != null)
                        {
                            if (extra.Amount != 0 && extra.Type != null)
                            {
                                o.OfferAmount = extra.Amount;
                                o.OfferTypeText = extra.Type;
                            }
                        }
                    }
                }
                catch { }

                o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 1000).ToArray();

                if (o.MaxEta == 0)
                    o.MaxEta = 999;
            }

            if (o.MasterAccountId == MasterAccountTypes.Towbook &&
                o.AccountName != null &&
                o.AccountName.ToLowerInvariant().Contains("county towing & storage"))
            {
                // TODO: DTS, Dispatch to Subcontractor: make maxEta come from sender,
                // this is a bit of a hack to take care of county towing & storage,
                // but others will need this.

                o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 31).ToArray();
                o.MaxEta = 30;
            }

            if (o.MasterAccountId == MasterAccountTypes.Towbook &&
               o.AccountName != null &&
               o.AccountName.ToLowerInvariant().Contains("splendora"))
            {
                o.SupportedEtas = o.SupportedEtas.Where(eo => eo < 16).ToArray();
                o.MaxEta = 15;
            }

            if (o.MasterAccountId == MasterAccountTypes.Towbook &&
               o.AccountName != null &&
               o.AccountName.ToLowerInvariant().Contains("carvana"))
            {
                o.SupportedEtas = new int[] {
                    15, 30, 45, 60, 75, 90,
                    120, 180, 240, 360, 420, 480,
                    540, 600, 1000, 1200, 1440
                };
                o.MaxEta = 1440;
            }

            var defaultEta = await Towbook.Integration.AccountKeyValue.GetFirstValueOrNullAsync(o.CompanyId, o.AccountId, Towbook.Integration.Provider.Towbook.ProviderId, "DefaultEta");
            if (defaultEta != null && int.TryParse(defaultEta, out int deta))
            {
                o.DefaultEta = deta;
            }

            if (o.ProviderId != null && o.ProviderId.Contains(".") && o.MaxEta == 0) // hack to detect that the call is an agero call.
                o.MaxEta = 90;
            else if (o.MaxEta == 0)
            {
                // iOS Beta workaround
                o.MaxEta = 240;
            }
            else
            {
                // another fix for iOS
                if (acc.MasterAccountId == MasterAccountTypes.Nsd)
                    o.MaxEta = 240;
            }

            if (o.MasterAccountId == MasterAccountTypes.OonUrgently)
            {
                o.MaxEta = 90;
                o.SupportedEtas = o.SupportedEtas.Where(v => v >= 30 && v < 91).ToArray();
            }

            if (o.MasterAccountId == MasterAccountTypes.AlliedDispatch)
            {
                o.MaxEta = 90;
                o.SupportedEtas = o.SupportedEtas.Where(v => v >= 30 && v < 91).ToArray();
            }

            if (o.MasterAccountId == MasterAccountTypes.OonUrgently ||
                o.MasterAccountId == MasterAccountTypes.Honk)
            {
                o.Drivers = (await Driver.GetByExactCompanyIdAsync(o.CompanyId))
                    .Where(x => x.IsActive())
                    .Select(rx => rx.Id).ToCsv();
            }

            if (o.MasterAccountId == MasterAccountTypes.Gerber)
            {
                o.SupportedEtas = o.SupportedEtas.Where(eo =>
                   eo > 5 &&
                   eo < 61).ToArray();

                o.MaxEta = 120;
            }
            else if (o.MasterAccountId == MasterAccountTypes.Urgently ||
                o.MasterAccountId == MasterAccountTypes.OonUrgently)
            {
                o.SupportedEtas = o.SupportedEtas.Where(eo =>
                    eo >= 30 &&
                    eo < 181).ToArray();

                o.MaxEta = 180;
            }

            var model = new CallRequestModel
            {
                CallRequestId = o.CallRequestId,
                CompanyId = o.CompanyId,
                AccountId = o.AccountId,
                AccountName = o.AccountName,
                MasterAccountId = o.MasterAccountId,
                OfferAmount = o.OfferAmount,
                OfferTypeText = o.OfferTypeText,
                DefaultEta = o.DefaultEta,
                StartingLocation = o.StartingLocation,
                Reason = o.Reason,
                ServiceNeeded = o.ServiceNeeded,
                Vehicle = o.Vehicle,
                OwnerUserId = o.OwnerUserId,
                PurchaseOrderNumber = o.PurchaseOrderNumber,
                TowDestination = o.TowDestination,
                ProviderId = o.ProviderId,
                Drivers = o.Drivers,
                Trucks = o.Trucks,
                MaxEta = o.MaxEta,
                SupportedEtas = o.SupportedEtas,
                ExpirationDate = o.ExpirationDate,
                RequestDate = o.RequestDate,
                ExpirationDateUtc = o.ExpirationDateUtc,
                RequestDateUtc = o.RequestDateUtc,
                DispatchEntryId = o.DispatchEntryId,
                Status = o.Status,
                ResponseReasonId = o.ResponseReasonId,
                Distance = o.Distance,
                LoadedDistance = o.LoadedDistance,
                Eta = o.Eta,
                StartLocationLatitude = o.StartLocationLatitude,
                StartLocationLongitude = o.StartLocationLongitude,
                AvailableActions = o.AvailableActions.ToList(),
            };

            var company = await Company.Company.GetByIdAsync(o.CompanyId);
            bool hasDigitalDispatchImprovements = company.HasAccessToBaseFeature("digitialDispatchImprovements");
            if (hasDigitalDispatchImprovements)
            {
                model.AvailableActions.Add("NearestDrivers");
                model.AvailableActions.Add("LoadedDistance");
            }

            return model;
        }
    }
}
