using Extric.Towbook.Dispatch;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Models.Calls
{
    public static class DriverTruckPairModelExtensions
    {
        private static User CurrentUser => (User)Web.HttpContextFactory.Instance.CurrentUser;

        /// <summary>
        /// Takes a list of driverTruckPairs and returns a collection of them as EntryAssetDrivers
        /// </summary>
        /// <param name="input">The list of new driver/truck pairs. </param>
        /// <param name="asset">Original Asset which would contain driver/truck pairs</param>
        /// <returns></returns>
        public async static Task<Collection<Dispatch.EntryAssetDriver>> TranslateAsync(this Extric.Towbook.Dispatch.CallModels.DriverTruckPairModel[] input, EntryAsset asset)
        {
            // TODO: convert to extension method for API only.

            var retval = new Collection<Dispatch.EntryAssetDriver>();
            if (input == null)
                return retval;

            foreach (var ad in input)
            {
                if (ad.Driver == null && ad.Truck == null && asset != null)
                {
                    // if the id is specified, we need to leave the original as is
                    if (ad.Id > 0)
                    {
                        var o = asset.Drivers?.FirstOrDefault(w => w.Id == ad.Id);
                        if (o != null)
                            retval.Add(o);
                    }
                    else
                    {
                        continue;
                    }
                }

                var n = new Dispatch.EntryAssetDriver()
                {
                    Id = ad.Id.GetValueOrDefault(),
                    DriverId = ad.Driver?.Id,
                    TruckId = ad.Truck?.Id,
                    CurrentWaypointId = ad.Driver?.CurrentWaypointId
                };

                var existingDriverAndTruck = asset.Drivers?.FirstOrDefault(w => w.Id == ad.Id);
                if (n.DriverId.GetValueOrDefault() != 0)
                {
                    var dr = await Towbook.Driver.GetByIdAsync(n.DriverId.GetValueOrDefault());

                    // If you're trying to set this to a driver you can't access, leave it as the existing driver that was there before
                    if (dr != null && (CurrentUser == null || !await CurrentUser.HasAccessToCompanyAsync(dr.Companies)))
                        n.DriverId = existingDriverAndTruck?.DriverId;
                }

                if (n.TruckId.GetValueOrDefault() != 0)
                {
                    var tr = await Towbook.Truck.GetByIdAsync(n.TruckId.GetValueOrDefault());
                    // If you're trying to set this to a truck you can't access, leave it as the existing truck that was there before
                    if (tr != null && (CurrentUser == null || !await CurrentUser.HasAccessToCompanyAsync(tr.Companies)))
                        n.TruckId = existingDriverAndTruck?.TruckId;
                }

                // If the model only contains 1 item, and it's missing it's ID, automatically assume it's the first driver.

                if (asset != null && input.Length == 1 && asset.Drivers != null && asset.Drivers.Count == 1)
                {
                    if (n.Id == 0)
                        n.Id = asset.Drivers[0].Id;

                    var x = asset.Drivers.FirstOrDefault(o => o.Id == n.Id);

                    if (x != null)
                    {
                        if (n.TruckId == null && x.TruckId.GetValueOrDefault() > 0)
                        {
                            n.TruckId = x.TruckId;
                        }

                        if (n.CurrentWaypointId == null && x.CurrentWaypointId.GetValueOrDefault() > 0)
                        {
                            n.CurrentWaypointId = x.CurrentWaypointId;
                        }
                    }
                }

                retval.Add(n);
            }

            return retval;
        }

    }
}
