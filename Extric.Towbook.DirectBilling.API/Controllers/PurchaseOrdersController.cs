using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.DirectBilling.API.Models;
using Extric.Towbook.Integration.MotorClubs.Services;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.DirectBilling.API.ApiUtility;
//using Agero = Extric.Towbook.Integration.MotorClubs.Billing.Agero;
using Allstate = Extric.Towbook.Integration.MotorClubs.Billing.Allstate;
using Geico = Extric.Towbook.Integration.MotorClubs.Billing.Geico;

namespace Extric.Towbook.DirectBilling.API.Controllers
{
    /// <summary>
    /// Handles retrieval and submission of purchase orders to direct billing providers. 
    /// </summary>
    [Route("directBilling/purchaseOrders")]
    public class PurchaseOrdersController : ControllerBase
    {

        //routes.MapHttpRoute(
        //    name: "Direct_Billing_Default_Api_Get",
        //    routeTemplate: "directBilling/{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Get" },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //        id = new RestStyleConstraint()
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("")]
        [HttpGet]
        public object Get()
        {
            return Array.Empty<object>();
        }

        /// <summary>
        /// Get missing invoices that are on the motor club website, but not yet in Towbook
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "Direct_Billing_Default_Api_GetSingle",
        //    routeTemplate: "directBilling/{controller}/{action}",
        //    defaults: new { action = "Get" },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("missing")]
        [HttpGet]
        public async Task<IEnumerable<PurchaseOrderModel>> Missing(int accountId, int[] companyIds)
        {
            // Company access filter
            foreach (var c in companyIds)
                await ThrowIfNoCompanyAccessAsync(c, "Entry");

            var des = new PurchaseOrderListService();
            var models = new List<PurchaseOrderModel>();

            // Get this account
            var account = await Account.GetByIdAsync(accountId);
            if (account != null)
            {
                // Get its master account
                var masterAccount = await MasterAccount.GetByIdAsync(account.MasterAccountId);
                if (masterAccount != null)
                {
                    // Get the missing invoices 
                    switch (masterAccount.Id)
                    {
                        case MasterAccountTypes.Agero:
                            models = Map(Integration.MotorClubs.Billing.Agero.PurchaseOrder.GetMissing(accountId));
                            break;
                        case MasterAccountTypes.Geico:
                            models = Map(Geico.PurchaseOrder.GetMissing(accountId));
                            break;
                        case MasterAccountTypes.Allstate:
                            models = Map(Allstate.PurchaseOrder.GetMissing(accountId));
                            break;
                    }
                }
            }

            return models;
        }

        private List<PurchaseOrderModel> Map(IEnumerable<Integration.MotorClubs.Billing.Agero.PurchaseOrder> pos)
        {
            var models = new List<PurchaseOrderModel>();
            foreach (var po in pos)
            {
                models.Add(new PurchaseOrderModel()
                {
                    PoNumber = po.PurchaseOrderNumber,
                    CreateDate = po.ServiceDate.Value,
                    CompletionTime = po.ServiceDate.Value,
                    Vin = po.VIN,
                    Odometer = po.Odometer,
                    ContactName = $"{po.MemberFirstName} {po.MemberMiddleName} {po.MemberLastName}".Replace("  ", " "),
                });
            }
            return models;
        }

        private List<PurchaseOrderModel> Map(IEnumerable<Geico.PurchaseOrder> pos)
        {
            var models = new List<PurchaseOrderModel>();
            foreach (var po in pos)
            {
                var model = new PurchaseOrderModel()
                {
                    PoNumber = po.PurchaseOrderNumber,
                    CreateDate = Convert.ToDateTime(po.ServiceDate),
                    CompletionTime = Convert.ToDateTime(po.ServiceDate),
                    ContactName = po.CustomerName,
                    TowSource = po.LossLocation,
                };

                // Split vehicle info into parts.  Example: 2006 FORD F-150
                if (!string.IsNullOrWhiteSpace(po.Vehicle))
                {
                    string[] vehicleParts = po.Vehicle.Split(' ');
                    if (vehicleParts.Length >= 3)
                    {
                        int year = 0;
                        if (int.TryParse(vehicleParts[0], out year))
                            model.VehicleYear = vehicleParts[0];

                        model.VehicleMake = vehicleParts[1].Trim();
                        model.VehicleModel = vehicleParts[2].Trim();
                    }
                }

                models.Add(model);
            }
            return models;
        }

        private List<PurchaseOrderModel> Map(IEnumerable<Allstate.PurchaseOrder> pos)
        {
            var models = new List<PurchaseOrderModel>();
            foreach (var po in pos)
            {
                var pickup = po.GetPickupAddress();
                var dropOff = po.GetDropOffAddress();

                models.Add(new PurchaseOrderModel()
                {
                    PoNumber = po.PurchaseOrderNumber,
                    CreateDate = po.ServiceDate,
                    CompletionTime = po.ServiceDate,
                    Vin = po.VehicleVIN,
                    ContactName = po.CustomerName,
                    ContactNumber = po.CallbackNumber,
                    VehicleMake = po.VehicleMake,
                    VehicleModel = po.VehicleModel,
                    VehicleColor = po.VehicleColor,
                    VehicleLicensePlate = po.VehicleLicensePlate,
                    PickupAddress1 = pickup?.AddressLine1,
                    PickupAddress2 = pickup?.AddressLine2,
                    PickupCity = pickup?.City,
                    PickupState = pickup?.State,
                    PickupZip = pickup?.Zip,
                    PickupLatitude = pickup?.Latitude,
                    PickupLongitude = pickup?.Longitude,
                    DropoffAddress1 = dropOff?.AddressLine1,
                    DropoffAddress2 = dropOff?.AddressLine2,
                    DropoffCity = dropOff?.City,
                    DropoffState = dropOff?.State,
                    DropoffZip = dropOff?.Zip,
                    DropoffLatitude = dropOff?.Latitude,
                    DropoffLongitude = dropOff?.Longitude,
                });
            }
            return models;
        }
    }
}
