using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.WebShared;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using Extric.Towbook.Dispatch;
using Extric.Towbook.DirectBilling.API.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Geico = Extric.Towbook.Integration.MotorClubs.Billing.Geico;
using Agero = Extric.Towbook.Integration.MotorClubs.Billing.Agero;
using Allstate = Extric.Towbook.Integration.MotorClubs.Billing.Allstate;
using static Extric.Towbook.DirectBilling.API.ApiUtility;

namespace Extric.Towbook.DirectBilling.API.Controllers
{
    /// <summary>
    /// Handles submitting invoices to motor club and querying submission history
    /// </summary>
    [Route("directBilling/submissions")]
    public class SubmissionsController : ControllerBase
    {
        /// <summary>
        /// Get submitted entries
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "Direct_Billing_Default_Api_GetSingle",
        //        routeTemplate: "directBilling/{controller}/{action}",
        //        defaults: new { action = "Get" },
        //        constraints: new
        //        {
        //            httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //        }
        //        ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("submitted")]
        [HttpGet]
        public async Task<IEnumerable<SubmissionEntryModel>> Submitted(int accountId, int[] companyIds, int? statusFilter = null, bool? hiddenFilter = null, int pageNumber = 1, int pageSize = 50, string quickSearch = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            // Company access filter
            foreach (var c in companyIds)
                await ThrowIfNoCompanyAccessAsync(c, "Entry");

            var models = (await DispatchEntryQueueItem.GetSubmittedAsync(accountId, companyIds, statusFilter, hiddenFilter, pageNumber, pageSize, quickSearch, startDate, endDate))
                .Select(s => SubmissionEntryModel.Map(s))
                .ToCollection();

            // Add lock and audit info to model
            var callIds = models.Where(model => model.DispatchEntryId.GetValueOrDefault() > 0).Select(model => model.DispatchEntryId.Value).Distinct().ToArray();
            var entryLocks = EntryLock.GetByDispatchEntryIds(callIds);

            var entryAudits = (await Dispatch.AttributeValue.GetByDispatchEntriesAsync(callIds.ToCollection()))
                .Where(attributeValue => attributeValue.DispatchEntryAttributeId == Dispatch.AttributeValue.BUILTIN_DISPATCH_AUDITED)
                .ToCollection();

            models = (await SubmissionEntryModel.MapAsync(models, entryLocks, entryAudits)).ToCollection();

            return models;
        }

        /// <summary>
        /// Get unsubmitted entries
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "Direct_Billing_Default_Api_GetSingle",
        //        routeTemplate: "directBilling/{controller}/{action}",
        //        defaults: new { action = "Get" },
        //        constraints: new
        //        {
        //            httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //        }
        //        ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("unsubmitted")]
        [HttpGet]
        public async Task<IEnumerable<SubmissionEntryModel>> Unsubmitted(int accountId, int[] companyIds, string quickSearch = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            // Company access filter
            foreach (var c in companyIds)
                await ThrowIfNoCompanyAccessAsync(c, "Entry");

            var des = new DispatchEntryService();
            var models = new List<SubmissionEntryModel>();
            var account = await Account.GetByIdAsync(accountId);
            var availablePOs = new List<string>();
            var masterAccountName = "";

            // Get the list of PO numbers available to submit on the motor club website
            if (account != null)
            {
                var masterAccount = await MasterAccount.GetByIdAsync(account.MasterAccountId);
                if (masterAccount != null)
                {
                    masterAccountName = masterAccount.Name;

                    // Get the available POs
                    switch (masterAccount.Id)
                    {
                        case MasterAccountTypes.Agero:
                            availablePOs = Extric.Towbook.Integration.MotorClubs.Billing.Agero.PurchaseOrder.GetAvailable(accountId).ToList();
                            break;
                        case MasterAccountTypes.Geico:
                            availablePOs = Geico.PurchaseOrder.GetAvailable(accountId).ToList();
                            break;
                        case MasterAccountTypes.Allstate:
                            availablePOs = Allstate.PurchaseOrder.GetAvailable(accountId).ToList();
                            break;

                    }
                }
            }

            // Get the unsubmitted POs
            foreach (var o in await DispatchEntryQueueItem.GetUnsubmittedAsync(accountId, companyIds, quickSearch, startDate, endDate))
            {
                // Default - filter out calls created in the last 24 hours
                var dateFilter = DateTime.Now.AddHours(-24);

                if (account.MasterAccountId == MasterAccountTypes.Fleetnet)
                    dateFilter = DateTime.Now.AddDays(1);

                // Geico - filter out calls created in the last 48 hours
                if (account?.MasterAccountId == MasterAccountTypes.Geico)
                    dateFilter = DateTime.Now.AddHours(-48);
                
                // Agero - filter out calls created after today at 00:00
                if (account?.MasterAccountId == MasterAccountTypes.Agero ||
                    account?.MasterAccountId == MasterAccountTypes.Quest)
                    dateFilter = DateTime.Today;

                if (o.CreateDate < dateFilter && (o.InvoiceTotal == 0 || o.BalanceDue > 0 || account?.MasterAccountId == MasterAccountTypes.Geico))
                {
                    var model = SubmissionEntryModel.Map(o);
                    model.ValidationErrors = des.ValidateFieldsBeforeSubmit(o, o.Account.MasterAccountId);

                    // don't look at available PO's if the list is empty. emergency fix 8/19/2016 - all customers
                    // submit pages are showing not yet available to submit. 
                    if (availablePOs.Any())
                    {
                        // If this po is not available
                        if (!availablePOs.Contains(o.PurchaseOrderNumber))
                        {
                            // Create a validation error for it
                            model.ValidationErrors.Add($"Not yet available to submit on the {masterAccountName} website");
                        }
                    }
                    models.Add(model);
                }
            }

            // Add lock and audit info to model
            var callIds = models.Where(model => model.DispatchEntryId.GetValueOrDefault() > 0).Select(model => model.DispatchEntryId.Value).Distinct().ToArray();
            var entryLocks = EntryLock.GetByDispatchEntryIds(callIds);

            var entryAudits = Dispatch.AttributeValue.GetByDispatchEntries(callIds.ToCollection())
                .Where(attributeValue => attributeValue.DispatchEntryAttributeId == Dispatch.AttributeValue.BUILTIN_DISPATCH_AUDITED)
                .ToCollection();

            models = (await SubmissionEntryModel.MapAsync(models, entryLocks, entryAudits)).ToList();

            return models;
        }

        /// <summary>
        /// Get a single unsubmitted entry
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "Direct_Billing_Default_Api_Get",
        //        routeTemplate: "directBilling/{controller}/{id}",
        //        defaults: new { id = RouteParameter.Optional, action = "Get" },
        //        constraints: new
        //        {
        //            httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //            id = new RestStyleConstraint()
        //        }
        //        ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("{id}")]
        [HttpGet]
        public async Task<SubmissionEntryModel> Get(int id)
        {
            var o = await DispatchEntryQueueItem.GetEntryAsync(id);
            if (o != null)
            {
                // Company access filter
                await ThrowIfNoCompanyAccessAsync(o.CompanyId, "Entry");

                var des = new DispatchEntryService();
                var model = SubmissionEntryModel.Map(o);
                model.ValidationErrors = des.ValidateFieldsBeforeSubmit(o, o.Account.MasterAccountId);
                return model;
            }

            return null;
        }

        public class SubmitInvoicesModel
        {
            public int[] EntryIds { get; set; }
            public int AccountId { get; set; }
            public bool TestMode { get; set; }
        }

        /// <summary>
        /// Retrieve submission success ratio for a company
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "Direct_Billing_Default_Api_GetSingle",
        //    routeTemplate: "directBilling/{controller}/{action}",
        //    defaults: new { action = "Get" },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("getSubmissionSuccessRatio")]
        [HttpGet]
        public async Task<DispatchEntryQueueItem.SubmissionSuccessRatio> GetSubmissionSuccessRatio(int companyId, int accountId)
        {
            // Company access filter
            await ThrowIfNoCompanyAccessAsync(companyId, "Company");

            var account = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(account?.CompanyId);

            return DispatchEntryQueueItem.GetSubmissionSuccessRatio(companyId, accountId);
        }

        /// <summary>
        /// Create a new submission from a list of entries
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "Direct_Billing_Default_Api_Post",
        //        routeTemplate: "directBilling/{controller}/{id}",
        //        defaults: new { id = RouteParameter.Optional, action = "Post" },
        //        constraints: new
        //        {
        //            httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
        //            id = new RestStyleConstraint()
        //        })
        //        .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("")]
        [HttpPost]
        public async Task<object> Post(SubmitInvoicesModel model)
        {
            var errors = new List<object>();

            try
            {
                if (model.EntryIds.Count() > 0)
                {
                    // Create a new statement
                    var st = new Statement();
                    st.SourceId = StatementSource.DirectBilling;
                    st.AccountId = model.AccountId;
                    st.Company = WebGlobal.CurrentUser.Company;
                    st.StatementDate = DateTime.Now;
                    st.DueDate = DateTime.Now;
                    st.OwnerUserId = WebGlobal.CurrentUser.Id;

                    foreach(var x in await Entry.GetByIdsAsync(model.EntryIds, null, false))
                    {
                        st.DispatchEntries.Add(x);
                    }

                    // Save it to the database
                    await st.Save();

                    var des = new DispatchEntryService();
                    var entryCount = st.DispatchEntries.Count();

                    // For each entry to submit
                    for (int i = 0; i < entryCount; i++)
                    {
                        var entry = st.DispatchEntries[i];

                        try
                        {
                            // Create a queue item for it
                            var x = new DispatchEntryQueueItem()
                            {
                                CompanyId = st.Company.Id,
                                DispatchEntryId = entry.Id,
                                OwnerUserId = WebGlobal.CurrentUser.Id,
                                TestMode = model.TestMode
                            };

                            // Request it be added to the queue for processing
                            await des.RequestInvoiceSubmission(x, entry);
                        }
                        catch (Exception ex)
                        {
                            errors.Add(new
                            {
                                entryId = entry.Id,
                                callNumber = entry.CallNumber,
                                message = ex.Message,
                            });
                        }
                        finally
                        {
                            // Send pusher event up to UI, to increase progress bar
                            await Integration.PushNotificationProvider.Push(
                                WebGlobal.CurrentUser.Company.Id,
                                "invoice_submitted",
                                new
                                {
                                    accountId = st.AccountId,
                                    statementId = st.Id,
                                    dispatchEntryId = entry.Id,
                                    errors = errors,
                                    position = i + 1,
                                    total = entryCount,
                                });
                        }
                    }
                }

                if (errors.Count > 0)
                    return BadRequest(errors); //Request.CreateResponse<List<dynamic>>(HttpStatusCode.BadRequest, errors);
                else
                    return Ok();
            }
            catch (Exception ex)
            {
                errors.Add(new { message = ex.Message });
                return StatusCode((int)HttpStatusCode.InternalServerError, JsonConvert.SerializeObject(errors));
                //Request.CreateResponse<List<dynamic>>(HttpStatusCode.InternalServerError, errors, new System.Net.Http.Formatting.JsonMediaTypeFormatter());
            }
        }

        /// <summary>
        /// Hide/unhide a list of entries
        /// </summary>
        //routes.MapHttpRoute(
        //    name: "Direct_Billing_Default_Api_Post",
        //        routeTemplate: "directBilling/{controller}/{id}",
        //        defaults: new { id = RouteParameter.Optional, action = "Post" },
        //        constraints: new
        //        {
        //            httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
        //            id = new RestStyleConstraint()
        //        })
        //        .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("setHidden")]
        [HttpPost]
        public object SetHidden(SubmitInvoicesModel model)
        {
            var errors = new List<object>();

            try
            {
                if (model.EntryIds.Count() > 0)
                {
                    foreach (var id in model.EntryIds)
                    {
                        try
                        {
                            DispatchEntryQueueItem.SetHidden(id, model.TestMode);  // testMode is actually carrying hiddenStatus
                        }
                        catch (Exception ex)
                        {
                            errors.Add(new
                            {
                                entryId = id,
                                message = ex.Message,
                            });
                        }
                    }
                }

                if (errors.Count > 0)
                    return BadRequest(errors);
                    //Request.CreateResponse<List<dynamic>>(HttpStatusCode.BadRequest, errors);
                else
                    return Ok();
            }
            catch (Exception ex)
            {
                errors.Add(new { message = ex.Message });
                return StatusCode((int)HttpStatusCode.InternalServerError, JsonConvert.SerializeObject(errors)); 
                    //Request.CreateResponse<List<dynamic>>(HttpStatusCode.InternalServerError, errors, new System.Net.Http.Formatting.JsonMediaTypeFormatter());
            }
        }
    }
}
